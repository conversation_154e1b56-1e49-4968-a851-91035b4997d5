{"name": "@types/component-emitter", "version": "1.2.11", "description": "TypeScript definitions for component-emitter", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/component-emitter", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/psnider", "githubUsername": "psnider"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/component-emitter"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "d86d217b63101effae1228ebbfe02ac682ee4eb8abd6fc0dcc9948a4b6fdf572", "typeScriptVersion": "3.7"}