TODO

v0.5.0
+ Major cleanup
+ Remove old unused functionality
+ Load modules in parallel (faster dev mode)

v0.4.9
+ Make the max length of lines an option and make it quite short by default for easier debugging

v0.4.7
+ Remove default port and host - just use what the current page uses by default
+ Treat Android devices as mobile, with regards to loading resources in one big response rather than individual requests
+ Now works if script include put in head

v0.4.6
+ Add server.setOpts and server.handleRequest

v0.4.5
+ Set the mimetype of script files correctly
+ Remove node_modules directory - use dependencies in package.json instead
+ Implement addPath and addFile for specifying custom path resolutions
+ Implement addReplacement for replacing code during dev serving or compilation
+ Implement dontAddClosureForModule and dontIncludeModule for compiler
+ Remove dependency on std.js
+ Add usePagePort option to make module requests go to the host/port of the page it's served on, rather than the host/port passed in to listen(port, ...)

v0.4.4
+ Import js files directly under node_modules correctly, e.g. ./node_modules/foo.js

v0.4.3
+ Fix import of socket.io browser npm module
+ Enable nested npm module imports by reimplementing node's require.resolve algorithm

v0.4.2
+ Use uglifyjs to compress JS
+ Synchronous compilation API
+ Remove --paths option. Search for modules exactly like require.resolve does
+ Greatle simplify Readme instructions
+ Use util.resolve in compiler, minimizing repeated code
+ Implement require.mount(server, opts) - mounts itself onto an existing http server
+ Implement require.connect(opts) - connect middleware
+ Implement require.isRequireRequest - checks if an http request is for the require server

v0.4.1
+ Consult package.json's "main" field when resolving modules in the compiler
+ Fix bug where modules starting with (function() { ... }) would throw in their compiled form
+ Clearer compiler usage example in example/compile.js
+ Better error messages when a module cannot be found during compilation

v0.4.0
+ New API
+ New command line utility
+ New documentation

v0.3.2
+ Improved command line tool
+ new API: single method require('require/compiler').compile(<code | file path>, <compilation level 0 | 1 | 2 | 3>, <callback>)
+ allow chaining of methods on the require server
+ alert an error message client-side when a module is not found (rather than crash the process :)

v0.3.1
+ Fix context bug in delay
+ Change compiled instances of require._[PATH] to require[PATH]. require is simply a hash of module paths.
+ Add setRoot API
+ Improved docs in README
+ Binary request-dev --port 1234 --host localhost ./path

v0.3.0
+ Make a standalone server for dev mode

v0.2.8
+ Don't leave require.paths modified after require.resolve. Add paths right before they're needed, and remove paths after.

v0.2.7
+ Improve path resolution

v0.2.6
+ Improve the order in which we try to load modules

v0.2.5
+ Fix compilation order for __main__

v0.2.1
+ A development server that serves requires files in dev mode

v0.2.0
+ latest version published on npm

v0.1.13
+ better file layout - bin/ lib/
+ created bin/compile to compile files
+ created bin/compress to compile and compress files
+ improved readability of compiled files by flattening the require hierarchy
+ replace module names with short names, e.g. "_1", "_2", etc

v0.1.12
+ Support module path names in the compiler, e.g. require('require/compiler')

v0.1.11
+ Fix: compiler now resolves module/index.js paths correctly

v0.1.10
+ Allow for a node server to server require using require('require').toString()

v0.1.9
+ It's no longer required to add id="browser-require" to a script tag in order to load the main module automatically

v0.1.8
+ Rename to require

v0.1.7
+ Publish with npm

v0.1.6
+ Fix: Variables declared inside of require.js are no longer visible to imported modules
+ Remove the "root" configuration parameter added in v0.1.1. You cannot modify the root in node.

v0.1.5
+ Allow for a file inside of a folder to import the folder's index file with require('./')

v0.1.4
+ Allow modules with circular dependencies to reference each other

v0.1.3
+ Don't get stuck loading circular dependencies

v0.1.2
+ Support index.js - if a module is not found at location, look for it at location/index.js

v0.1.1
+ Support the "root" configuration parameter
+ Fix: consecutive slashes get replaced with a single slash as opposed to no slash

v0.1.0
First version, implements node's require() statement for the browser
