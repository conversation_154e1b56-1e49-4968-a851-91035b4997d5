{"version": 3, "file": "socket.io.min.js", "sources": ["../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export default (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import globalThis from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} options.\n     * @api private\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.readyState = \"\";\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @api protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     *\n     * @api public\n     */\n    open() {\n        if (\"closed\" === this.readyState || \"\" === this.readyState) {\n            this.readyState = \"opening\";\n            this.doOpen();\n        }\n        return this;\n    }\n    /**\n     * Closes the transport.\n     *\n     * @api public\n     */\n    close() {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     * @api public\n     */\n    send(packets) {\n        if (\"open\" === this.readyState) {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @api protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @api protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @api protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @api protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport globalThis from \"../globalThis.js\";\nexport default function (opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport XMLHttpRequest from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport globalThis from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    /**\n     * Transport name.\n     */\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @api private\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} callback upon buffers are flushed and transport is paused\n     * @api private\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @api public\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @api private\n     */\n    onData(data) {\n        const callback = packet => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @api private\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} data packets\n     * @param {Function} drain callback\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, data => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @api private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @api private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @api private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @api public\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @api private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @api private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @api private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @api private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @api public\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import globalThis from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return cb => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @api {Object} connection options\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Transport name.\n     *\n     * @api public\n     */\n    get name() {\n        return \"websocket\";\n    }\n    /**\n     * Opens socket.\n     *\n     * @api private\n     */\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @api private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = closeEvent => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent\n        });\n        this.ws.onmessage = ev => this.onData(ev.data);\n        this.ws.onerror = e => this.onError(\"websocket error\", e);\n    }\n    /**\n     * Writes data to socket.\n     *\n     * @param {Array} array of packets.\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, data => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    /**\n     * Closes socket.\n     *\n     * @api private\n     */\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @api public\n     */\n    check() {\n        return (!!WebSocket &&\n            !(\"__initialize\" in WebSocket && this.name === WS.prototype.name));\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri or options\n     * @param {Object} opts - options\n     * @api public\n     */\n    constructor(uri, opts = {}) {\n        super();\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.readyState = \"\";\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                addEventListener(\"beforeunload\", () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                }, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} transport name\n     * @return {Transport}\n     * @api private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @api private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @api private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", reason => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} transport name\n     * @api private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", msg => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = err => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @api private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState &&\n            this.opts.upgrade &&\n            this.transport.pause) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @api private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @api private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @api private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @api private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @api private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} message.\n     * @param {Function} callback function.\n     * @param {Object} options.\n     * @return {Socket} for chaining.\n     * @api public\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @api private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     *\n     * @api public\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @api private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @api private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} server upgrades\n     * @api private\n     *\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.connected = false;\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        const timeout = this.flags.timeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n        this.id = id;\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * ```\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     * ```\n     *\n     * @returns self\n     * @public\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(event);\n     * });\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(event);\n     * });\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * const handler = (event, ...args) => {\n     *   console.log(event);\n     * }\n     *\n     * socket.onAnyOutgoing(handler);\n     *\n     * // then later\n     * socket.offAnyOutgoing(handler);\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        this.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "obj", "encodeBlobAsBase64", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "this", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "globalThis", "clearTimeoutFn", "prev", "TransportError", "reason", "description", "context", "Error", "Transport", "writable", "query", "readyState", "socket", "doOpen", "doClose", "onClose", "packets", "write", "packet", "onPacket", "details", "alphabet", "map", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Polling", "polling", "location", "isSSL", "protocol", "port", "xd", "hostname", "xs", "secure", "forceBase64", "poll", "onPause", "pause", "_this2", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "_this3", "onOpen", "close", "_this4", "count", "encodePayload", "_this5", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "path", "Request", "uri", "req", "request", "method", "xhrStatus", "_this6", "onError", "onData", "_this7", "pollXhr", "async", "undefined", "xscheme", "xhr", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "_this9", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "name", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "substr", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "_this", "writeBuffer", "prevBufferLen", "_extends", "agent", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "transport", "offlineEventListener", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "_typeof", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "encodeAsString", "BINARY_EVENT", "BINARY_ACK", "encodeAsBinary", "nsp", "stringify", "deconstruction", "unshift", "Decoder", "reviver", "decodeString", "reconstructor", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "reconPack", "binData", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "auth", "_autoConnect", "subs", "onpacket", "subEvents", "_readyState", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "discardPacket", "notifyOutgoingListeners", "timer", "_packet", "onconnect", "onevent", "onack", "ondisconnect", "destroy", "message", "emitEvent", "_anyListeners", "sent", "emitBuffered", "subDestroy", "listener", "_anyOutgoingListeners", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "pow", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "_a", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "maybeReconnectOnOpen", "onping", "ondata", "ondecoded", "add", "active", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;glIAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAAAC,GAC9BH,EAAqBH,EAAaM,IAAQA,KCN9C,IDQA,IAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBEXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,WAAiBC,EAAgBC,OALvCC,EAKSZ,IAAAA,KAAMC,IAAAA,YACtBC,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTY,EAAmBZ,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVI,EAegCX,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOF,GACnBA,GAAOA,EAAIG,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTY,EAAmB,IAAIV,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,MAE5CY,EAAqB,SAACZ,EAAMU,OACxBK,EAAa,IAAIC,kBACvBD,EAAWE,OAAS,eACVC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,IDtC9BsB,EAAQ,mEAGRC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC9DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,MEH5BnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,SAACC,EAAeC,MACJ,iBAAlBD,QACA,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,QAGjC/B,EAAO8B,EAAcG,OAAO,SACrB,MAATjC,EACO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,GAWTmC,EAAqB,SAACjC,EAAM8B,MAC1BxB,EAAuB,KACjB6B,EFFQ,SAACC,OAGfX,EAEAY,EACAC,EACAC,EACAC,EAPAC,EAA+B,IAAhBL,EAAOV,OACtBgB,EAAMN,EAAOV,OAEbiB,EAAI,EAM0B,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,SAIFG,EAAc,IAAIrC,YAAYkC,GAChCI,EAAQ,IAAIrB,WAAWoB,OAEtBnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IAExCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,SAGnCI,EE7BaE,CAAO9C,UAChB+B,EAAUI,EAASL,SAGnB,CAAEM,QAAQ,EAAMpC,KAAAA,IAGzB+B,EAAY,SAAC/B,EAAM8B,SAEZ,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,GC3Cb+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQvC,MAClBA,EAAK,OAWX,SAAeA,OACR,IAAId,KAAOqD,EAAQ/C,UACtBQ,EAAId,GAAOqD,EAAQ/C,UAAUN,UAExBc,EAfSwC,CAAMxC,GA2BxBuC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,eAC9CC,WAAaC,KAAKD,YAAc,IACpCC,KAAKD,WAAW,IAAMF,GAASG,KAAKD,WAAW,IAAMF,IAAU,IAC7DI,KAAKH,GACDE,MAaTP,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,YAC9BH,SACFQ,IAAIN,EAAOF,GAChBG,EAAGM,MAAMJ,KAAMK,kBAGjBV,EAAGG,GAAKA,OACHH,GAAGE,EAAOF,GACRK,MAaTP,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,WACjDC,WAAaC,KAAKD,YAAc,GAGjC,GAAKM,UAAUpC,mBACZ8B,WAAa,GACXC,SAcLS,EAVAC,EAAYV,KAAKD,WAAW,IAAMF,OACjCa,EAAW,OAAOV,QAGnB,GAAKK,UAAUpC,qBACV+B,KAAKD,WAAW,IAAMF,GACtBG,SAKJ,IAAIhC,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,QACpCyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,gBAOC,IAArB0C,EAAUzC,eACL+B,KAAKD,WAAW,IAAMF,GAGxBG,MAWTP,EAAQ/C,UAAUkE,KAAO,SAASf,QAC3BE,WAAaC,KAAKD,YAAc,WAEjCc,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYV,KAAKD,WAAW,IAAMF,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,MAGtB0C,EAEG,CAAI1C,EAAI,MAAR,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAMJ,KAAMa,UAItBb,MAITP,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,eAChCE,WAAaC,KAAKD,YAAc,GAC9BC,KAAKD,WAAW,IAAMF,IAAU,IAWzCJ,EAAQ/C,UAAUwE,aAAe,SAASrB,WAC9BG,KAAKiB,UAAUpB,GAAO5B,cCtKV,oBAATkD,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKpE,8BAAQqE,mCAAAA,2BAClBA,EAAKC,QAAO,SAACC,EAAKC,UACjBxE,EAAIyE,eAAeD,KACnBD,EAAIC,GAAKxE,EAAIwE,IAEVD,IACR,IAGP,IAAMG,EAAqBC,WACrBC,EAAuBC,aACtB,SAASC,EAAsB9E,EAAK+E,GACnCA,EAAKC,iBACLhF,EAAIiF,aAAeP,EAAmBQ,KAAKC,GAC3CnF,EAAIoF,eAAiBR,EAAqBM,KAAKC,KAG/CnF,EAAIiF,aAAeN,WAAWO,KAAKC,GACnCnF,EAAIoF,eAAiBP,aAAaK,KAAKC,QChB1BE,ECAfC,2CACUC,EAAQC,EAAaC,yCACvBF,IACDC,YAAcA,IACdC,QAAUA,IACVrG,KAAO,+BALSsG,QAQhBC,2CAOGZ,2CAEHa,UAAW,EAChBd,OAA4BC,KACvBA,KAAOA,IACPc,MAAQd,EAAKc,QACbC,WAAa,KACbC,OAAShB,EAAKgB,0CAWvB,SAAQR,EAAQC,EAAaC,0DACN,QAAS,IAAIH,EAAeC,EAAQC,EAAaC,IAC7D3C,yBAOX,iBACQ,WAAaA,KAAKgD,YAAc,KAAOhD,KAAKgD,kBACvCA,WAAa,eACbE,UAEFlD,0BAOX,iBACQ,YAAcA,KAAKgD,YAAc,SAAWhD,KAAKgD,kBAC5CG,eACAC,WAEFpD,yBAQX,SAAKqD,GACG,SAAWrD,KAAKgD,iBACXM,MAAMD,yBAWnB,gBACSL,WAAa,YACbF,UAAW,kDACG,8BAQvB,SAAOvG,OACGgH,EAASpF,EAAa5B,EAAMyD,KAAKiD,OAAO5E,iBACzCmF,SAASD,2BAOlB,SAASA,mDACc,SAAUA,0BAOjC,SAAQE,QACCT,WAAa,yDACC,QAASS,UAtGLhE,GDTzBiE,EAAW,mEAAmE/F,MAAM,IAAkBgG,EAAM,GAC9GC,EAAO,EAAG5F,EAAI,EAQX,SAAS6F,EAAOC,OACfC,EAAU,MAEVA,EAAUL,EAASI,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,UACRC,EAsBJ,SAASG,QACNC,EAAMN,GAAQ,IAAIO,aACpBD,IAAQ5B,GACDqB,EAAO,EAAGrB,EAAO4B,GACrBA,EAAM,IAAMN,EAAOD,KAK9B,KAAO5F,EA9CiG,GA8CrFA,IACf2F,EAAID,EAAS1F,IAAMA,EEzChB,SAAS6F,EAAO3G,OACfmH,EAAM,OACL,IAAIrG,KAAKd,EACNA,EAAIyE,eAAe3D,KACfqG,EAAIpG,SACJoG,GAAO,KACXA,GAAOC,mBAAmBtG,GAAK,IAAMsG,mBAAmBpH,EAAIc,YAG7DqG,EAQJ,SAAShF,EAAOkF,WACfC,EAAM,GACNC,EAAQF,EAAG5G,MAAM,KACZK,EAAI,EAAG0G,EAAID,EAAMxG,OAAQD,EAAI0G,EAAG1G,IAAK,KACtC2G,EAAOF,EAAMzG,GAAGL,MAAM,KAC1B6G,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,WAExDH,EC/BX,IAAIK,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,eAEjC,MAAOC,IAIA,IAAMC,EAAUH,ECPR,WAAU5C,OACfgD,EAAUhD,EAAKgD,eAGb,oBAAuBH,kBAAoBG,GAAWD,UAC/C,IAAIF,eAGnB,MAAOI,QACFD,aAEU,IAAI5C,EAAW,CAAC,UAAU8C,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,KCRf,SAASG,KACT,IAAMC,EAIK,MAHK,IAAIR,EAAe,CAC3BG,SAAS,IAEMM,aAEVC,4CAOGvD,qCACFA,IACDwD,SAAU,EACS,oBAAbC,SAA0B,KAC3BC,EAAQ,WAAaD,SAASE,SAChCC,EAAOH,SAASG,KAEfA,IACDA,EAAOF,EAAQ,MAAQ,QAEtBG,GACoB,oBAAbJ,UACJzD,EAAK8D,WAAaL,SAASK,UAC3BF,IAAS5D,EAAK4D,OACjBG,GAAK/D,EAAKgE,SAAWN,MAKxBO,EAAcjE,GAAQA,EAAKiE,qBAC5BlJ,eAAiBsI,IAAYY,gCAKtC,iBACW,gCAQX,gBACSC,4BAQT,SAAMC,mBACGpD,WAAa,cACZqD,EAAQ,WACVC,EAAKtD,WAAa,SAClBoD,QAEApG,KAAKyF,UAAYzF,KAAK8C,SAAU,KAC5ByD,EAAQ,EACRvG,KAAKyF,UACLc,SACKrG,KAAK,gBAAgB,aACpBqG,GAASF,QAGdrG,KAAK8C,WACNyD,SACKrG,KAAK,SAAS,aACbqG,GAASF,aAKnBA,wBAQR,gBACSZ,SAAU,OACVe,cACAxF,aAAa,8BAOtB,SAAOzE,eTvFW,SAACkK,EAAgBpI,WAC7BqI,EAAiBD,EAAe9I,MAAM2B,GACtC+D,EAAU,GACPrF,EAAI,EAAGA,EAAI0I,EAAezI,OAAQD,IAAK,KACtC2I,EAAgBxI,EAAauI,EAAe1I,GAAIK,MACtDgF,EAAQpD,KAAK0G,GACc,UAAvBA,EAAcrK,kBAIf+G,GS4FHuD,CAAcrK,EAAMyD,KAAKiD,OAAO5E,YAAYlC,SAd3B,SAAAoH,MAET,YAAcsD,EAAK7D,YAA8B,SAAhBO,EAAOjH,MACxCuK,EAAKC,SAGL,UAAYvD,EAAOjH,YACnBuK,EAAKzD,QAAQ,CAAEV,YAAa,oCACrB,EAGXmE,EAAKrD,SAASD,MAKd,WAAavD,KAAKgD,kBAEbyC,SAAU,OACVzE,aAAa,gBACd,SAAWhB,KAAKgD,iBACXmD,+BAWjB,sBACUY,EAAQ,WACVC,EAAK1D,MAAM,CAAC,CAAEhH,KAAM,YAEpB,SAAW0D,KAAKgD,WAChB+D,SAKK7G,KAAK,OAAQ6G,wBAU1B,SAAM1D,mBACGP,UAAW,ET5JF,SAACO,EAASpG,OAEtBgB,EAASoF,EAAQpF,OACjByI,EAAiB,IAAI5F,MAAM7C,GAC7BgJ,EAAQ,EACZ5D,EAAQlH,SAAQ,SAACoH,EAAQvF,GAErBjB,EAAawG,GAAQ,GAAO,SAAAnF,GACxBsI,EAAe1I,GAAKI,IACd6I,IAAUhJ,GACZhB,EAASyJ,EAAetB,KAAK9F,USmJrC4H,CAAc7D,GAAS,SAAA9G,GACnB4K,EAAKC,QAAQ7K,GAAM,WACf4K,EAAKrE,UAAW,EAChBqE,EAAKnG,aAAa,kCAS9B,eACQ+B,EAAQ/C,KAAK+C,OAAS,GACpBsE,EAASrH,KAAKiC,KAAKgE,OAAS,QAAU,OACxCJ,EAAO,IAEP,IAAU7F,KAAKiC,KAAKqF,oBACpBvE,EAAM/C,KAAKiC,KAAKsF,gBAAkBrD,KAEjClE,KAAKhD,gBAAmB+F,EAAMyE,MAC/BzE,EAAM0E,IAAM,GAGZzH,KAAKiC,KAAK4D,OACR,UAAYwB,GAAqC,MAA3BK,OAAO1H,KAAKiC,KAAK4D,OACpC,SAAWwB,GAAqC,KAA3BK,OAAO1H,KAAKiC,KAAK4D,SAC3CA,EAAO,IAAM7F,KAAKiC,KAAK4D,UAErB8B,EAAe9D,EAAOd,UAEpBsE,EACJ,QAF8C,IAArCrH,KAAKiC,KAAK8D,SAAS6B,QAAQ,KAG5B,IAAM5H,KAAKiC,KAAK8D,SAAW,IAAM/F,KAAKiC,KAAK8D,UACnDF,EACA7F,KAAKiC,KAAK4F,MACTF,EAAa1J,OAAS,IAAM0J,EAAe,2BAQpD,eAAQ1F,yDAAO,YACGA,EAAM,CAAE6D,GAAI9F,KAAK8F,GAAIE,GAAIhG,KAAKgG,IAAMhG,KAAKiC,MAChD,IAAI6F,GAAQ9H,KAAK+H,MAAO9F,0BASnC,SAAQ1F,EAAMuD,cACJkI,EAAMhI,KAAKiI,QAAQ,CACrBC,OAAQ,OACR3L,KAAMA,IAEVyL,EAAIrI,GAAG,UAAWG,GAClBkI,EAAIrI,GAAG,SAAS,SAACwI,EAAWxF,GACxByF,EAAKC,QAAQ,iBAAkBF,EAAWxF,4BAQlD,sBACUqF,EAAMhI,KAAKiI,UACjBD,EAAIrI,GAAG,OAAQK,KAAKsI,OAAOlG,KAAKpC,OAChCgI,EAAIrI,GAAG,SAAS,SAACwI,EAAWxF,GACxB4F,EAAKF,QAAQ,iBAAkBF,EAAWxF,WAEzC6F,QAAUR,SA7NMnF,GAgOhBiF,4CAOGC,EAAK9F,0BAEbD,oBAA4BC,KACvBA,KAAOA,IACPiG,OAASjG,EAAKiG,QAAU,QACxBH,IAAMA,IACNU,OAAQ,IAAUxG,EAAKwG,QACvBlM,UAAOmM,IAAczG,EAAK1F,KAAO0F,EAAK1F,KAAO,OAC7CP,2CAOT,sBACUiG,EAAOX,EAAKtB,KAAKiC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKgD,UAAYjF,KAAKiC,KAAK6D,GAC3B7D,EAAK0G,UAAY3I,KAAKiC,KAAK+D,OACrB4C,EAAO5I,KAAK4I,IAAM,IAAI9D,EAAe7C,OAEvC2G,EAAIC,KAAK7I,KAAKkI,OAAQlI,KAAK+H,IAAK/H,KAAKyI,cAE7BzI,KAAKiC,KAAK6G,iBAEL,IAAI9K,KADT4K,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzC/I,KAAKiC,KAAK6G,aAChB9I,KAAKiC,KAAK6G,aAAanH,eAAe3D,IACtC4K,EAAII,iBAAiBhL,EAAGgC,KAAKiC,KAAK6G,aAAa9K,IAK/D,MAAOkH,OACH,SAAWlF,KAAKkI,WAEZU,EAAII,iBAAiB,eAAgB,4BAEzC,MAAO9D,QAGP0D,EAAII,iBAAiB,SAAU,OAEnC,MAAO9D,IAEH,oBAAqB0D,IACrBA,EAAIK,gBAAkBjJ,KAAKiC,KAAKgH,iBAEhCjJ,KAAKiC,KAAKiH,iBACVN,EAAIO,QAAUnJ,KAAKiC,KAAKiH,gBAE5BN,EAAIQ,mBAAqB,WACjB,IAAMR,EAAI5F,aAEV,MAAQ4F,EAAIS,QAAU,OAAST,EAAIS,OACnCC,EAAKC,SAKLD,EAAKnH,cAAa,WACdmH,EAAKjB,QAA8B,iBAAfO,EAAIS,OAAsBT,EAAIS,OAAS,KAC5D,KAGXT,EAAIY,KAAKxJ,KAAKzD,MAElB,MAAO2I,oBAIE/C,cAAa,WACdmH,EAAKjB,QAAQnD,KACd,GAGiB,oBAAbuE,gBACFC,MAAQ5B,EAAQ6B,gBACrB7B,EAAQ8B,SAAS5J,KAAK0J,OAAS1J,6BAQvC,SAAQ+E,QACC/D,aAAa,QAAS+D,EAAK/E,KAAK4I,UAChCiB,SAAQ,0BAOjB,SAAQC,WACA,IAAuB9J,KAAK4I,KAAO,OAAS5I,KAAK4I,aAGhDA,IAAIQ,mBAAqB/D,EAC1ByE,WAESlB,IAAImB,QAEb,MAAO7E,IAEa,oBAAbuE,iBACA3B,EAAQ8B,SAAS5J,KAAK0J,YAE5Bd,IAAM,4BAOf,eACUrM,EAAOyD,KAAK4I,IAAIoB,aACT,OAATzN,SACKyE,aAAa,OAAQzE,QACrByE,aAAa,gBACb6I,gCAQb,gBACSA,iBAxIgBpK,GAkJ7B,GAPAqI,GAAQ6B,cAAgB,EACxB7B,GAAQ8B,SAAW,GAMK,oBAAbH,YAEoB,mBAAhBQ,YAEPA,YAAY,WAAYC,SAEvB,GAAgC,mBAArBtK,iBAAiC,CAE7CA,iBADyB,eAAgByC,EAAa,WAAa,SAChC6H,IAAe,GAG1D,SAASA,SACA,IAAIlM,KAAK8J,GAAQ8B,SACd9B,GAAQ8B,SAASjI,eAAe3D,IAChC8J,GAAQ8B,SAAS5L,GAAG+L,QC9YzB,IAAMI,GACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAAA5J,UAAM2J,QAAQC,UAAUC,KAAK7J,IAG7B,SAACA,EAAI0B,UAAiBA,EAAa1B,EAAI,IAGzC8J,GAAYlI,EAAWkI,WAAalI,EAAWmI,aCHtDC,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,4CAOG5I,yCACFA,IACDjF,gBAAkBiF,EAAKiE,0CAOhC,iBACW,kCAOX,cACSlG,KAAK8K,aAIJ/C,EAAM/H,KAAK+H,MACXgD,EAAY/K,KAAKiC,KAAK8I,UAEtB9I,EAAOwI,GACP,GACAnJ,EAAKtB,KAAKiC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMjC,KAAKiC,KAAK6G,eACV7G,EAAK+I,QAAUhL,KAAKiC,KAAK6G,uBAGpBmC,GACyBR,GAIpB,IAAIF,GAAUxC,EAAKgD,EAAW9I,GAH9B8I,EACI,IAAIR,GAAUxC,EAAKgD,GACnB,IAAIR,GAAUxC,GAGhC,MAAOhD,UACI/E,KAAKgB,aAAa,QAAS+D,QAEjCkG,GAAG5M,WAAa2B,KAAKiD,OAAO5E,YD/CR,mBCgDpB6M,sDAOT,2BACSD,GAAGE,OAAS,WACT7E,EAAKrE,KAAKmJ,WACV9E,EAAK2E,GAAGI,QAAQC,QAEpBhF,EAAKQ,eAEJmE,GAAGM,QAAU,SAAAC,UAAclF,EAAKlD,QAAQ,CACzCV,YAAa,8BACbC,QAAS6I,UAERP,GAAGQ,UAAY,SAAAC,UAAMpF,EAAKgC,OAAOoD,EAAGnP,YACpC0O,GAAGU,QAAU,SAAAzG,UAAKoB,EAAK+B,QAAQ,kBAAmBnD,yBAQ3D,SAAM7B,mBACGP,UAAW,qBAGP9E,OACCuF,EAASF,EAAQrF,GACjB4N,EAAa5N,IAAMqF,EAAQpF,OAAS,EAC1ClB,EAAawG,EAAQsD,EAAK7J,gBAAgB,SAAAT,OAsB9BsK,EAAKoE,GAAGzB,KAAKjN,GAMrB,MAAO2I,IAEH0G,GAGAzB,IAAS,WACLtD,EAAK/D,UAAW,EAChB+D,EAAK7F,aAAa,WACnB6F,EAAK1E,kBAvCXnE,EAAI,EAAGA,EAAIqF,EAAQpF,OAAQD,MAA3BA,0BAiDb,gBAC2B,IAAZgC,KAAKiL,UACPA,GAAGlE,aACHkE,GAAK,yBAQlB,eACQlI,EAAQ/C,KAAK+C,OAAS,GACpBsE,EAASrH,KAAKiC,KAAKgE,OAAS,MAAQ,KACtCJ,EAAO,GAEP7F,KAAKiC,KAAK4D,OACR,QAAUwB,GAAqC,MAA3BK,OAAO1H,KAAKiC,KAAK4D,OAClC,OAASwB,GAAqC,KAA3BK,OAAO1H,KAAKiC,KAAK4D,SACzCA,EAAO,IAAM7F,KAAKiC,KAAK4D,MAGvB7F,KAAKiC,KAAKqF,oBACVvE,EAAM/C,KAAKiC,KAAKsF,gBAAkBrD,KAGjClE,KAAKhD,iBACN+F,EAAM0E,IAAM,OAEVE,EAAe9D,EAAOd,UAEpBsE,EACJ,QAF8C,IAArCrH,KAAKiC,KAAK8D,SAAS6B,QAAQ,KAG5B,IAAM5H,KAAKiC,KAAK8D,SAAW,IAAM/F,KAAKiC,KAAK8D,UACnDF,EACA7F,KAAKiC,KAAK4F,MACTF,EAAa1J,OAAS,IAAM0J,EAAe,yBAQpD,oBACc4C,IACJ,iBAAkBA,IAAavK,KAAK6L,OAAShB,EAAGnO,UAAUmP,aAhLhDhJ,GCRXiJ,GAAa,CACtBC,UAAWlB,GACXpF,QAASD,ICGPwG,GAAK,0OACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAM7H,OACZ8H,EAAM9H,EAAK+H,EAAI/H,EAAIuD,QAAQ,KAAM1C,EAAIb,EAAIuD,QAAQ,MAC7C,GAANwE,IAAiB,GAANlH,IACXb,EAAMA,EAAI5F,UAAU,EAAG2N,GAAK/H,EAAI5F,UAAU2N,EAAGlH,GAAGmH,QAAQ,KAAM,KAAOhI,EAAI5F,UAAUyG,EAAGb,EAAIpG,iBA0B3E8E,EACbxG,EAzBF+P,EAAIN,GAAGO,KAAKlI,GAAO,IAAK0D,EAAM,GAAI/J,EAAI,GACnCA,KACH+J,EAAIkE,GAAMjO,IAAMsO,EAAEtO,IAAM,UAElB,GAANoO,IAAiB,GAANlH,IACX6C,EAAIyE,OAASL,EACbpE,EAAI0E,KAAO1E,EAAI0E,KAAKhO,UAAU,EAAGsJ,EAAI0E,KAAKxO,OAAS,GAAGoO,QAAQ,KAAM,KACpEtE,EAAI2E,UAAY3E,EAAI2E,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EtE,EAAI4E,SAAU,GAElB5E,EAAI6E,UAIR,SAAmB1P,EAAK2K,OACdgF,EAAO,WAAYC,EAAQjF,EAAKwE,QAAQQ,EAAM,KAAKlP,MAAM,KACtC,KAArBkK,EAAKkF,OAAO,EAAG,IAA6B,IAAhBlF,EAAK5J,QACjC6O,EAAMnM,OAAO,EAAG,GAEmB,KAAnCkH,EAAKkF,OAAOlF,EAAK5J,OAAS,EAAG,IAC7B6O,EAAMnM,OAAOmM,EAAM7O,OAAS,EAAG,UAE5B6O,EAZSF,CAAU7E,EAAKA,EAAG,MAClCA,EAAIiF,UAaejK,EAbUgF,EAAG,MAc1BxL,EAAO,GACbwG,EAAMsJ,QAAQ,6BAA6B,SAAUY,EAAIC,EAAIC,GACrDD,IACA3Q,EAAK2Q,GAAMC,MAGZ5Q,GAnBAwL,MCtBEqF,4CAQGrF,SAAK9F,yDAAO,mCAEhB8F,GAAO,aAAoBA,KAC3B9F,EAAO8F,EACPA,EAAM,MAENA,GACAA,EAAMmE,GAAMnE,GACZ9F,EAAK8D,SAAWgC,EAAI0E,KACpBxK,EAAKgE,OAA0B,UAAjB8B,EAAInC,UAAyC,QAAjBmC,EAAInC,SAC9C3D,EAAK4D,KAAOkC,EAAIlC,KACZkC,EAAIhF,QACJd,EAAKc,MAAQgF,EAAIhF,QAEhBd,EAAKwK,OACVxK,EAAK8D,SAAWmG,GAAMjK,EAAKwK,MAAMA,MAErCzK,OAA4BC,KACvBgE,OACD,MAAQhE,EAAKgE,OACPhE,EAAKgE,OACe,oBAAbP,UAA4B,WAAaA,SAASE,SAC/D3D,EAAK8D,WAAa9D,EAAK4D,OAEvB5D,EAAK4D,KAAOwH,EAAKpH,OAAS,MAAQ,QAEjCF,SACD9D,EAAK8D,WACoB,oBAAbL,SAA2BA,SAASK,SAAW,eAC1DF,KACD5D,EAAK4D,OACoB,oBAAbH,UAA4BA,SAASG,KACvCH,SAASG,KACTwH,EAAKpH,OACD,MACA,QACb6F,WAAa7J,EAAK6J,YAAc,CAAC,UAAW,eAC5C9I,WAAa,KACbsK,YAAc,KACdC,cAAgB,IAChBtL,KAAOuL,EAAc,CACtB3F,KAAM,aACN4F,OAAO,EACPxE,iBAAiB,EACjByE,SAAS,EACTnG,eAAgB,IAChBoG,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,GAClBC,qBAAqB,GACtB/L,KACEA,KAAK4F,KAAOwF,EAAKpL,KAAK4F,KAAKwE,QAAQ,MAAO,IAAM,IACtB,iBAApBgB,EAAKpL,KAAKc,UACZd,KAAKc,MAAQ1D,EAAOgO,EAAKpL,KAAKc,UAGlCkL,GAAK,OACLC,SAAW,OACXC,aAAe,OACfC,YAAc,OAEdC,iBAAmB,KACQ,mBAArBzO,mBACHyN,EAAKpL,KAAK+L,qBAIVpO,iBAAiB,gBAAgB,WACzByN,EAAKiB,cAEAA,UAAU/N,uBACV+N,UAAUvH,YAEpB,GAEe,cAAlBsG,EAAKtH,aACAwI,qBAAuB,aACnBnL,QAAQ,kBAAmB,CAC5BV,YAAa,6BAGrB9C,iBAAiB,UAAWyN,EAAKkB,sBAAsB,OAG1D1F,kDAST,SAAgBgD,OACN9I,EAAQyK,EAAc,GAAIxN,KAAKiC,KAAKc,OAE1CA,EAAMyL,IdnFU,EcqFhBzL,EAAMuL,UAAYzC,EAEd7L,KAAKiO,KACLlL,EAAMyE,IAAMxH,KAAKiO,QACfhM,EAAOuL,EAAc,GAAIxN,KAAKiC,KAAK8L,iBAAiBlC,GAAO7L,KAAKiC,KAAM,CACxEc,MAAAA,EACAE,OAAQjD,KACR+F,SAAU/F,KAAK+F,SACfE,OAAQjG,KAAKiG,OACbJ,KAAM7F,KAAK6F,cAER,IAAIiG,GAAWD,GAAM5J,uBAOhC,eACQqM,YACAtO,KAAKiC,KAAK0L,iBACVP,EAAOqB,wBACmC,IAA1CzO,KAAK8L,WAAWlE,QAAQ,aACxB0G,EAAY,gBAEX,CAAA,GAAI,IAAMtO,KAAK8L,WAAW7N,wBAEtBkE,cAAa,WACdmE,EAAKtF,aAAa,QAAS,6BAC5B,GAIHsN,EAAYtO,KAAK8L,WAAW,QAE3B9I,WAAa,cAGdsL,EAAYtO,KAAK0O,gBAAgBJ,GAErC,MAAOpJ,eACE4G,WAAW6C,kBACX9F,OAGTyF,EAAUzF,YACL+F,aAAaN,+BAOtB,SAAaA,cACLtO,KAAKsO,gBACAA,UAAU/N,0BAGd+N,UAAYA,EAEjBA,EACK3O,GAAG,QAASK,KAAK6O,QAAQzM,KAAKpC,OAC9BL,GAAG,SAAUK,KAAKwD,SAASpB,KAAKpC,OAChCL,GAAG,QAASK,KAAKqI,QAAQjG,KAAKpC,OAC9BL,GAAG,SAAS,SAAA8C,UAAUoE,EAAKzD,QAAQ,kBAAmBX,2BAQ/D,SAAMoJ,cACEyC,EAAYtO,KAAK0O,gBAAgB7C,GACjCiD,GAAS,EACb1B,EAAOqB,uBAAwB,MACzBM,EAAkB,WAChBD,IAEJR,EAAU9E,KAAK,CAAC,CAAElN,KAAM,OAAQC,KAAM,WACtC+R,EAAUpO,KAAK,UAAU,SAAA8O,OACjBF,KAEA,SAAWE,EAAI1S,MAAQ,UAAY0S,EAAIzS,KAAM,IAC7CyK,EAAKiI,WAAY,EACjBjI,EAAKhG,aAAa,YAAasN,IAC1BA,EACD,OACJlB,EAAOqB,sBAAwB,cAAgBH,EAAUzC,KACzD7E,EAAKsH,UAAUjI,OAAM,WACbyI,GAEA,WAAa9H,EAAKhE,aAEtB6G,IACA7C,EAAK4H,aAAaN,GAClBA,EAAU9E,KAAK,CAAC,CAAElN,KAAM,aACxB0K,EAAKhG,aAAa,UAAWsN,GAC7BA,EAAY,KACZtH,EAAKiI,WAAY,EACjBjI,EAAKkI,gBAGR,KACKnK,EAAM,IAAInC,MAAM,eAEtBmC,EAAIuJ,UAAYA,EAAUzC,KAC1B7E,EAAKhG,aAAa,eAAgB+D,kBAIrCoK,IACDL,IAGJA,GAAS,EACTjF,IACAyE,EAAUvH,QACVuH,EAAY,UAGV3C,EAAU,SAAA5G,OACNqK,EAAQ,IAAIxM,MAAM,gBAAkBmC,GAE1CqK,EAAMd,UAAYA,EAAUzC,KAC5BsD,IACAnI,EAAKhG,aAAa,eAAgBoO,aAE7BC,IACL1D,EAAQ,6BAGHJ,IACLI,EAAQ,0BAGH2D,EAAUC,GACXjB,GAAaiB,EAAG1D,OAASyC,EAAUzC,MACnCsD,QAIFtF,EAAU,WACZyE,EAAUhO,eAAe,OAAQyO,GACjCT,EAAUhO,eAAe,QAASqL,GAClC2C,EAAUhO,eAAe,QAAS+O,GAClCrI,EAAK7G,IAAI,QAASoL,GAClBvE,EAAK7G,IAAI,YAAamP,IAE1BhB,EAAUpO,KAAK,OAAQ6O,GACvBT,EAAUpO,KAAK,QAASyL,GACxB2C,EAAUpO,KAAK,QAASmP,QACnBnP,KAAK,QAASqL,QACdrL,KAAK,YAAaoP,GACvBhB,EAAUzF,6BAOd,mBACS7F,WAAa,OAClBoK,EAAOqB,sBAAwB,cAAgBzO,KAAKsO,UAAUzC,UACzD7K,aAAa,aACbkO,QAGD,SAAWlP,KAAKgD,YAChBhD,KAAKiC,KAAKyL,SACV1N,KAAKsO,UAAUjI,cACXrI,EAAI,EACF0G,EAAI1E,KAAKkO,SAASjQ,OACjBD,EAAI0G,EAAG1G,SACLwR,MAAMxP,KAAKkO,SAASlQ,4BASrC,SAASuF,MACD,YAAcvD,KAAKgD,YACnB,SAAWhD,KAAKgD,YAChB,YAAchD,KAAKgD,uBACdhC,aAAa,SAAUuC,QAEvBvC,aAAa,aACVuC,EAAOjH,UACN,YACImT,YAAYC,KAAKxD,MAAM3I,EAAOhH,iBAElC,YACIoT,wBACAC,WAAW,aACX5O,aAAa,aACbA,aAAa,kBAEjB,YACK+D,EAAM,IAAInC,MAAM,gBAEtBmC,EAAI8K,KAAOtM,EAAOhH,UACb8L,QAAQtD,aAEZ,eACI/D,aAAa,OAAQuC,EAAOhH,WAC5ByE,aAAa,UAAWuC,EAAOhH,kCAapD,SAAYA,QACHyE,aAAa,YAAazE,QAC1B0R,GAAK1R,EAAKiL,SACV8G,UAAUvL,MAAMyE,IAAMjL,EAAKiL,SAC3B0G,SAAWlO,KAAK8P,eAAevT,EAAK2R,eACpCC,aAAe5R,EAAK4R,kBACpBC,YAAc7R,EAAK6R,iBACnB2B,WAAaxT,EAAKwT,gBAClBjJ,SAED,WAAa9G,KAAKgD,iBAEjB2M,mDAOT,2BACSrN,eAAetC,KAAKqO,uBACpBA,iBAAmBrO,KAAKmC,cAAa,WACtCgF,EAAK/D,QAAQ,kBACdpD,KAAKmO,aAAenO,KAAKoO,aACxBpO,KAAKiC,KAAKmJ,gBACLiD,iBAAiB/C,+BAQ9B,gBACSgC,YAAY3M,OAAO,EAAGX,KAAKuN,oBAI3BA,cAAgB,EACjB,IAAMvN,KAAKsN,YAAYrP,YAClB+C,aAAa,cAGbkO,6BAQb,cACQ,WAAalP,KAAKgD,YAClBhD,KAAKsO,UAAUxL,WACd9C,KAAKiP,WACNjP,KAAKsN,YAAYrP,OAAQ,KACnBoF,EAAUrD,KAAKgQ,0BAChB1B,UAAU9E,KAAKnG,QAGfkK,cAAgBlK,EAAQpF,YACxB+C,aAAa,4CAS1B,gBACmChB,KAAK+P,YACR,YAAxB/P,KAAKsO,UAAUzC,MACf7L,KAAKsN,YAAYrP,OAAS,UAEnB+B,KAAKsN,oBXlYGpQ,EWoYf+S,EAAc,EACTjS,EAAI,EAAGA,EAAIgC,KAAKsN,YAAYrP,OAAQD,IAAK,KACxCzB,EAAOyD,KAAKsN,YAAYtP,GAAGzB,QAC7BA,IACA0T,GXvYO,iBADI/S,EWwYeX,GXjY1C,SAAoB8H,WACZ6L,EAAI,EAAGjS,EAAS,EACXD,EAAI,EAAG0G,EAAIL,EAAIpG,OAAQD,EAAI0G,EAAG1G,KACnCkS,EAAI7L,EAAInG,WAAWF,IACX,IACJC,GAAU,EAELiS,EAAI,KACTjS,GAAU,EAELiS,EAAI,OAAUA,GAAK,MACxBjS,GAAU,GAGVD,IACAC,GAAU,UAGXA,EAvBIkS,CAAWjT,GAGf8G,KAAKoM,KAPQ,MAOFlT,EAAImT,YAAcnT,EAAIoT,QWqY5BtS,EAAI,GAAKiS,EAAcjQ,KAAK+P,kBACrB/P,KAAKsN,YAAYvM,MAAM,EAAG/C,GAErCiS,GAAe,SAEZjQ,KAAKsN,iCAWhB,SAAM0B,EAAKuB,EAASzQ,eACX8P,WAAW,UAAWZ,EAAKuB,EAASzQ,GAClCE,yBAEX,SAAKgP,EAAKuB,EAASzQ,eACV8P,WAAW,UAAWZ,EAAKuB,EAASzQ,GAClCE,+BAWX,SAAW1D,EAAMC,EAAMgU,EAASzQ,MACxB,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAOmM,GAEP,mBAAsB6H,IACtBzQ,EAAKyQ,EACLA,EAAU,MAEV,YAAcvQ,KAAKgD,YAAc,WAAahD,KAAKgD,aAGvDuN,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,aAC/BjN,EAAS,CACXjH,KAAMA,EACNC,KAAMA,EACNgU,QAASA,QAERvP,aAAa,eAAgBuC,QAC7B+J,YAAYrN,KAAKsD,GAClBzD,GACAE,KAAKE,KAAK,QAASJ,QAClBoP,8BAOT,sBACUnI,EAAQ,WACVqB,EAAKhF,QAAQ,gBACbgF,EAAKkG,UAAUvH,SAEb0J,EAAkB,SAAlBA,IACFrI,EAAKjI,IAAI,UAAWsQ,GACpBrI,EAAKjI,IAAI,eAAgBsQ,GACzB1J,KAEE2J,EAAiB,WAEnBtI,EAAKlI,KAAK,UAAWuQ,GACrBrI,EAAKlI,KAAK,eAAgBuQ,UAE1B,YAAczQ,KAAKgD,YAAc,SAAWhD,KAAKgD,kBAC5CA,WAAa,UACdhD,KAAKsN,YAAYrP,YACZiC,KAAK,SAAS,WACXkI,EAAK6G,UACLyB,IAGA3J,OAIH/G,KAAKiP,UACVyB,IAGA3J,KAGD/G,4BAOX,SAAQ+E,GACJqI,EAAOqB,uBAAwB,OAC1BzN,aAAa,QAAS+D,QACtB3B,QAAQ,kBAAmB2B,0BAOpC,SAAQtC,EAAQC,GACR,YAAc1C,KAAKgD,YACnB,SAAWhD,KAAKgD,YAChB,YAAchD,KAAKgD,kBAEdV,eAAetC,KAAKqO,uBAEpBC,UAAU/N,mBAAmB,cAE7B+N,UAAUvH,aAEVuH,UAAU/N,qBACoB,mBAAxBC,qBACPA,oBAAoB,UAAWR,KAAKuO,sBAAsB,QAGzDvL,WAAa,cAEbiL,GAAK,UAELjN,aAAa,QAASyB,EAAQC,QAG9B4K,YAAc,QACdC,cAAgB,iCAU7B,SAAeW,WACLyC,EAAmB,GACrB3S,EAAI,EACF4S,EAAI1C,EAASjQ,OACZD,EAAI4S,EAAG5S,KACLgC,KAAK8L,WAAWlE,QAAQsG,EAASlQ,KAClC2S,EAAiB1Q,KAAKiO,EAASlQ,WAEhC2S,SAzjBalR,MA4jBrBmG,SdpiBiB,Ee9BxB,IAAM/I,GAA+C,mBAAhBC,YAM/BH,GAAWZ,OAAOW,UAAUC,SAC5BH,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,GAASC,KAAKH,MAChBoU,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBnU,GAASC,KAAKkU,MAMf,SAASC,GAAS7T,UACZL,KAA0BK,aAAeJ,aAlBvC,SAACI,SACyB,mBAAvBJ,YAAYM,OACpBN,YAAYM,OAAOF,GACnBA,EAAIG,kBAAkBP,YAeqCM,CAAOF,KACnEV,IAAkBU,aAAeT,MACjCoU,IAAkB3T,aAAe4T,KAEnC,SAASE,GAAU9T,EAAK+T,OACtB/T,GAAsB,WAAfgU,EAAOhU,UACR,KAEP4D,MAAMqQ,QAAQjU,GAAM,KACf,IAAIc,EAAI,EAAG0G,EAAIxH,EAAIe,OAAQD,EAAI0G,EAAG1G,OAC/BgT,GAAU9T,EAAIc,WACP,SAGR,KAEP+S,GAAS7T,UACF,KAEPA,EAAI+T,QACkB,mBAAf/T,EAAI+T,QACU,IAArB5Q,UAAUpC,cACH+S,GAAU9T,EAAI+T,UAAU,OAE9B,IAAM7U,KAAOc,KACVnB,OAAOW,UAAUiF,eAAe/E,KAAKM,EAAKd,IAAQ4U,GAAU9T,EAAId,WACzD,SAGR,ECxCJ,SAASgV,GAAkB7N,OACxB8N,EAAU,GACVC,EAAa/N,EAAOhH,KACpBgV,EAAOhO,SACbgO,EAAKhV,KAAOiV,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQpT,OACpB,CAAEsF,OAAQgO,EAAMF,QAASA,GAEpC,SAASG,GAAmBjV,EAAM8U,OACzB9U,EACD,OAAOA,KACPwU,GAASxU,GAAO,KACVmV,EAAc,CAAEC,cAAc,EAAM7N,IAAKuN,EAAQpT,eACvDoT,EAAQpR,KAAK1D,GACNmV,EAEN,GAAI5Q,MAAMqQ,QAAQ5U,GAAO,SACpBqV,EAAU,IAAI9Q,MAAMvE,EAAK0B,QACtBD,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7B4T,EAAQ5T,GAAKwT,GAAmBjV,EAAKyB,GAAIqT,UAEtCO,EAEN,GAAoB,WAAhBV,EAAO3U,MAAuBA,aAAgB6H,MAAO,KACpDwN,EAAU,OACX,IAAMxV,KAAOG,EACVR,OAAOW,UAAUiF,eAAe/E,KAAKL,EAAMH,KAC3CwV,EAAQxV,GAAOoV,GAAmBjV,EAAKH,GAAMiV,WAG9CO,SAEJrV,EAUJ,SAASsV,GAAkBtO,EAAQ8N,UACtC9N,EAAOhH,KAAOuV,GAAmBvO,EAAOhH,KAAM8U,GAC9C9N,EAAOkO,iBAAc/I,EACdnF,EAEX,SAASuO,GAAmBvV,EAAM8U,OACzB9U,EACD,OAAOA,KACPA,GAAQA,EAAKoV,oBACNN,EAAQ9U,EAAKuH,KAEnB,GAAIhD,MAAMqQ,QAAQ5U,OACd,IAAIyB,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BzB,EAAKyB,GAAK8T,GAAmBvV,EAAKyB,GAAIqT,QAGzC,GAAoB,WAAhBH,EAAO3U,OACP,IAAMH,KAAOG,EACVR,OAAOW,UAAUiF,eAAe/E,KAAKL,EAAMH,KAC3CG,EAAKH,GAAO0V,GAAmBvV,EAAKH,GAAMiV,WAI/C9U,ECjEJ,IACIwV,IACX,SAAWA,GACPA,EAAWA,EAAU,QAAc,GAAK,UACxCA,EAAWA,EAAU,WAAiB,GAAK,aAC3CA,EAAWA,EAAU,MAAY,GAAK,QACtCA,EAAWA,EAAU,IAAU,GAAK,MACpCA,EAAWA,EAAU,cAAoB,GAAK,gBAC9CA,EAAWA,EAAU,aAAmB,GAAK,eAC7CA,EAAWA,EAAU,WAAiB,GAAK,aAP/C,CAQGA,KAAeA,GAAa,SAIlBC,yBAMGC,kBACHA,SAAWA,kCAQpB,SAAO/U,UACCA,EAAIZ,OAASyV,GAAWG,OAAShV,EAAIZ,OAASyV,GAAWI,MACrDnB,GAAU9T,GAQX,CAAC8C,KAAKoS,eAAelV,KAPpBA,EAAIZ,KACAY,EAAIZ,OAASyV,GAAWG,MAClBH,GAAWM,aACXN,GAAWO,WACdtS,KAAKuS,eAAerV,kCAQvC,SAAeA,OAEPmH,EAAM,GAAKnH,EAAIZ,YAEfY,EAAIZ,OAASyV,GAAWM,cACxBnV,EAAIZ,OAASyV,GAAWO,aACxBjO,GAAOnH,EAAIuU,YAAc,KAIzBvU,EAAIsV,KAAO,MAAQtV,EAAIsV,MACvBnO,GAAOnH,EAAIsV,IAAM,KAGjB,MAAQtV,EAAI+Q,KACZ5J,GAAOnH,EAAI+Q,IAGX,MAAQ/Q,EAAIX,OACZ8H,GAAOqL,KAAK+C,UAAUvV,EAAIX,KAAMyD,KAAKiS,WAElC5N,gCAOX,SAAenH,OACLwV,EAAiBtB,GAAkBlU,GACnCqU,EAAOvR,KAAKoS,eAAeM,EAAenP,QAC1C8N,EAAUqB,EAAerB,eAC/BA,EAAQsB,QAAQpB,GACTF,WAQFuB,4CAMGC,2CAEHA,QAAUA,iCAOnB,SAAI3V,OACIqG,KACe,iBAARrG,GACPqG,EAASvD,KAAK8S,aAAa5V,IAChBZ,OAASyV,GAAWM,cAC3B9O,EAAOjH,OAASyV,GAAWO,iBAEtBS,cAAgB,IAAIC,GAAoBzP,GAElB,IAAvBA,EAAOkO,6DACY,UAAWlO,oDAKf,UAAWA,OAGjC,CAAA,IAAIwN,GAAS7T,KAAQA,EAAIyB,aAepB,IAAIiE,MAAM,iBAAmB1F,OAb9B8C,KAAK+S,oBACA,IAAInQ,MAAM,qDAGhBW,EAASvD,KAAK+S,cAAcE,eAAe/V,WAGlC6V,cAAgB,qDACF,UAAWxP,iCAc9C,SAAac,OACLrG,EAAI,EAEFkB,EAAI,CACN5C,KAAMoL,OAAOrD,EAAI9F,OAAO,aAEDmK,IAAvBqJ,GAAW7S,EAAE5C,YACP,IAAIsG,MAAM,uBAAyB1D,EAAE5C,SAG3C4C,EAAE5C,OAASyV,GAAWM,cACtBnT,EAAE5C,OAASyV,GAAWO,WAAY,SAC5BY,EAAQlV,EAAI,EACS,MAApBqG,EAAI9F,SAASP,IAAcA,GAAKqG,EAAIpG,aACrCkV,EAAM9O,EAAI5F,UAAUyU,EAAOlV,MAC7BmV,GAAOzL,OAAOyL,IAA0B,MAAlB9O,EAAI9F,OAAOP,SAC3B,IAAI4E,MAAM,uBAEpB1D,EAAEuS,YAAc/J,OAAOyL,MAGvB,MAAQ9O,EAAI9F,OAAOP,EAAI,GAAI,SACrBkV,EAAQlV,EAAI,IACTA,GAAG,IAEJ,MADMqG,EAAI9F,OAAOP,GAEjB,SACAA,IAAMqG,EAAIpG,OACV,MAERiB,EAAEsT,IAAMnO,EAAI5F,UAAUyU,EAAOlV,QAG7BkB,EAAEsT,IAAM,QAGNY,EAAO/O,EAAI9F,OAAOP,EAAI,MACxB,KAAOoV,GAAQ1L,OAAO0L,IAASA,EAAM,SAC/BF,EAAQlV,EAAI,IACTA,GAAG,KACFkS,EAAI7L,EAAI9F,OAAOP,MACjB,MAAQkS,GAAKxI,OAAOwI,IAAMA,EAAG,GAC3BlS,WAGFA,IAAMqG,EAAIpG,OACV,MAERiB,EAAE+O,GAAKvG,OAAOrD,EAAI5F,UAAUyU,EAAOlV,EAAI,OAGvCqG,EAAI9F,SAASP,GAAI,KACXqV,EAAUrT,KAAKsT,SAASjP,EAAI0I,OAAO/O,QACrC4U,EAAQW,eAAerU,EAAE5C,KAAM+W,SAIzB,IAAIzQ,MAAM,mBAHhB1D,EAAE3C,KAAO8W,SAMVnU,0BAEX,SAASmF,cAEMqL,KAAKxD,MAAM7H,EAAKrE,KAAK6S,SAEhC,MAAO3N,UACI,qCAuBPlF,KAAK+S,oBACAA,cAAcS,yDArB3B,SAAsBlX,EAAM+W,UAChB/W,QACCyV,GAAW0B,cACc,WAAnBvC,EAAOmC,QACbtB,GAAW2B,uBACOhL,IAAZ2K,OACNtB,GAAW4B,oBACc,iBAAZN,GAA2C,WAAnBnC,EAAOmC,QAC5CtB,GAAWG,WACXH,GAAWM,oBACLvR,MAAMqQ,QAAQkC,IAAYA,EAAQpV,OAAS,OACjD8T,GAAWI,SACXJ,GAAWO,kBACLxR,MAAMqQ,QAAQkC,WA5IR5T,GAgKvBuT,yBACUzP,kBACHA,OAASA,OACT8N,QAAU,QACVuC,UAAYrQ,0CAUrB,SAAesQ,WACNxC,QAAQpR,KAAK4T,GACd7T,KAAKqR,QAAQpT,SAAW+B,KAAK4T,UAAUnC,YAAa,KAE9ClO,EAASsO,GAAkB7R,KAAK4T,UAAW5T,KAAKqR,qBACjDmC,yBACEjQ,SAEJ,2CAKX,gBACSqQ,UAAY,UACZvC,QAAU,sDAlRC,sDCRjB,SAAS1R,GAAGzC,EAAKwO,EAAI5L,UACxB5C,EAAIyC,GAAG+L,EAAI5L,GACJ,WACH5C,EAAIiD,IAAIuL,EAAI5L,ICIpB,IAAMgU,GAAkB/X,OAAOgY,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb9T,eAAgB,IAEP8M,4CAMGiH,EAAI7B,EAAKvQ,2CAEZqS,WAAY,IACZC,cAAgB,KAChBC,WAAa,KACbC,IAAM,IACNC,KAAO,KACPC,MAAQ,KACRN,GAAKA,IACL7B,IAAMA,EACPvQ,GAAQA,EAAK2S,SACRA,KAAO3S,EAAK2S,MAEjBvH,EAAKgH,GAAGQ,cACRxH,EAAKxE,6CAKb,kBACY7I,KAAKsU,mCAOjB,eACQtU,KAAK8U,UAEHT,EAAKrU,KAAKqU,QACXS,KAAO,CACRnV,GAAG0U,EAAI,OAAQrU,KAAKmL,OAAO/I,KAAKpC,OAChCL,GAAG0U,EAAI,SAAUrU,KAAK+U,SAAS3S,KAAKpC,OACpCL,GAAG0U,EAAI,QAASrU,KAAK2L,QAAQvJ,KAAKpC,OAClCL,GAAG0U,EAAI,QAASrU,KAAKuL,QAAQnJ,KAAKpC,6BAM1C,mBACaA,KAAK8U,4BAOlB,kBACQ9U,KAAKsU,iBAEJU,YACAhV,KAAKqU,GAAL,eACDrU,KAAKqU,GAAGxL,OACR,SAAW7I,KAAKqU,GAAGY,aACnBjV,KAAKmL,UALEnL,yBAWf,kBACWA,KAAKgU,8BAQhB,sCAAQnT,2BAAAA,yBACJA,EAAK8R,QAAQ,gBACR/R,KAAKR,MAAMJ,KAAMa,GACfb,yBASX,SAAK0L,MACGoI,GAAgBnS,eAAe+J,SACzB,IAAI9I,MAAM,IAAM8I,EAAK,yDAFvB7K,mCAAAA,oBAIRA,EAAK8R,QAAQjH,OACPnI,EAAS,CACXjH,KAAMyV,GAAWG,MACjB3V,KAAMsE,EAEV0C,QAAiB,OACjBA,EAAOgN,QAAQC,UAAmC,IAAxBxQ,KAAK2U,MAAMnE,SAEjC,mBAAsB3P,EAAKA,EAAK5C,OAAS,GAAI,KACvCgQ,EAAKjO,KAAKyU,MACVS,EAAMrU,EAAKsU,WACZC,qBAAqBnH,EAAIiH,GAC9B3R,EAAO0K,GAAKA,MAEVoH,EAAsBrV,KAAKqU,GAAGiB,QAChCtV,KAAKqU,GAAGiB,OAAOhH,WACftO,KAAKqU,GAAGiB,OAAOhH,UAAUxL,SACvByS,EAAgBvV,KAAK2U,kBAAoBU,IAAwBrV,KAAKsU,kBACxEiB,IAEKvV,KAAKsU,gBACLkB,wBAAwBjS,QACxBA,OAAOA,SAGPiR,WAAWvU,KAAKsD,SAEpBoR,MAAQ,GACN3U,yCAKX,SAAqBiO,EAAIiH,cACf/L,EAAUnJ,KAAK2U,MAAMxL,gBACXT,IAAZS,OAKEsM,EAAQzV,KAAKqU,GAAGlS,cAAa,kBACxBmE,EAAKoO,KAAKzG,OACZ,IAAIjQ,EAAI,EAAGA,EAAIsI,EAAKkO,WAAWvW,OAAQD,IACpCsI,EAAKkO,WAAWxW,GAAGiQ,KAAOA,GAC1B3H,EAAKkO,WAAW7T,OAAO3C,EAAG,GAGlCkX,EAAItY,KAAK0J,EAAM,IAAI1D,MAAM,8BAC1BuG,QACEuL,KAAKzG,GAAM,WAEZ3H,EAAK+N,GAAG/R,eAAemT,8BAFP5U,2BAAAA,kBAGhBqU,EAAI9U,MAAMkG,GAAO,aAASzF,eAhBrB6T,KAAKzG,GAAMiH,wBAyBxB,SAAO3R,GACHA,EAAOiP,IAAMxS,KAAKwS,SACb6B,GAAGqB,QAAQnS,yBAOpB,sBAC4B,mBAAbvD,KAAK4U,UACPA,MAAK,SAACrY,GACPsK,EAAKtD,OAAO,CAAEjH,KAAMyV,GAAW0B,QAASlX,KAAAA,YAIvCgH,OAAO,CAAEjH,KAAMyV,GAAW0B,QAASlX,KAAMyD,KAAK4U,8BAS3D,SAAQ7P,GACC/E,KAAKsU,gBACDtT,aAAa,gBAAiB+D,0BAU3C,SAAQtC,EAAQC,QACP4R,WAAY,SACVtU,KAAKiO,QACPjN,aAAa,aAAcyB,EAAQC,2BAQ5C,SAASa,MACiBA,EAAOiP,MAAQxS,KAAKwS,WAGlCjP,EAAOjH,WACNyV,GAAW0B,WACRlQ,EAAOhH,MAAQgH,EAAOhH,KAAKiL,IAAK,KAC1ByG,EAAK1K,EAAOhH,KAAKiL,SAClBmO,UAAU1H,aAGVjN,aAAa,gBAAiB,IAAI4B,MAAM,yMAGhDmP,GAAWG,WACXH,GAAWM,kBACPuD,QAAQrS,cAEZwO,GAAWI,SACXJ,GAAWO,gBACPuD,MAAMtS,cAEVwO,GAAW2B,gBACPoC,0BAEJ/D,GAAW4B,mBACPoC,cACChR,EAAM,IAAInC,MAAMW,EAAOhH,KAAKyZ,SAElCjR,EAAIxI,KAAOgH,EAAOhH,KAAKA,UAClByE,aAAa,gBAAiB+D,2BAU/C,SAAQxB,OACE1C,EAAO0C,EAAOhH,MAAQ,GACxB,MAAQgH,EAAO0K,IACfpN,EAAKZ,KAAKD,KAAKkV,IAAI3R,EAAO0K,KAE1BjO,KAAKsU,eACA2B,UAAUpV,QAGV0T,cAActU,KAAKlE,OAAOgY,OAAOlT,6BAG9C,SAAUA,MACFb,KAAKkW,eAAiBlW,KAAKkW,cAAcjY,OAAQ,WAC/B+B,KAAKkW,cAAcnV,wCACH,SACrBX,MAAMJ,KAAMa,iEAGlBT,MAAMJ,KAAMa,sBAO3B,SAAIoN,OACM9M,EAAOnB,KACTmW,GAAO,SACJ,eAECA,GAEJA,GAAO,6BAJStV,2BAAAA,kBAKhBM,EAAKoC,OAAO,CACRjH,KAAMyV,GAAWI,IACjBlE,GAAIA,EACJ1R,KAAMsE,2BAUlB,SAAM0C,OACI2R,EAAMlV,KAAK0U,KAAKnR,EAAO0K,IACzB,mBAAsBiH,IACtBA,EAAI9U,MAAMJ,KAAMuD,EAAOhH,aAChByD,KAAK0U,KAAKnR,EAAO0K,8BAUhC,SAAUA,QACDA,GAAKA,OACLqG,WAAY,OACZ8B,oBACApV,aAAa,uCAOtB,2BACSuT,cAAcpY,SAAQ,SAAC0E,UAASmG,EAAKiP,UAAUpV,WAC/C0T,cAAgB,QAChBC,WAAWrY,SAAQ,SAACoH,GACrByD,EAAKwO,wBAAwBjS,GAC7ByD,EAAKzD,OAAOA,WAEXiR,WAAa,+BAOtB,gBACSuB,eACAxK,QAAQ,+CASjB,WACQvL,KAAK8U,YAEAA,KAAK3Y,SAAQ,SAACka,UAAeA,YAC7BvB,UAAOpM,QAEX2L,GAAL,SAAoBrU,gCAQxB,kBACQA,KAAKsU,gBACA/Q,OAAO,CAAEjH,KAAMyV,GAAW2B,kBAG9BqC,UACD/V,KAAKsU,gBAEA/I,QAAQ,wBAEVvL,0BAQX,kBACWA,KAAKkU,qCAShB,SAAS1D,eACAmE,MAAMnE,SAAWA,EACfxQ,2BASX,uBACS2U,gBAAiB,EACf3U,4BAiBX,SAAQmJ,eACCwL,MAAMxL,QAAUA,EACdnJ,0BASX,SAAMsW,eACGJ,cAAgBlW,KAAKkW,eAAiB,QACtCA,cAAcjW,KAAKqW,GACjBtW,+BASX,SAAWsW,eACFJ,cAAgBlW,KAAKkW,eAAiB,QACtCA,cAAcvD,QAAQ2D,GACpBtW,2BAQX,SAAOsW,OACEtW,KAAKkW,qBACClW,QAEPsW,WACMrV,EAAYjB,KAAKkW,cACdlY,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,OAC9BsY,IAAarV,EAAUjD,UACvBiD,EAAUN,OAAO3C,EAAG,GACbgC,eAKVkW,cAAgB,UAElBlW,iCAQX,kBACWA,KAAKkW,eAAiB,gCAkBjC,SAAcI,eACLC,sBAAwBvW,KAAKuW,uBAAyB,QACtDA,sBAAsBtW,KAAKqW,GACzBtW,uCAkBX,SAAmBsW,eACVC,sBAAwBvW,KAAKuW,uBAAyB,QACtDA,sBAAsB5D,QAAQ2D,GAC5BtW,mCAsBX,SAAesW,OACNtW,KAAKuW,6BACCvW,QAEPsW,WACMrV,EAAYjB,KAAKuW,sBACdvY,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,OAC9BsY,IAAarV,EAAUjD,UACvBiD,EAAUN,OAAO3C,EAAG,GACbgC,eAKVuW,sBAAwB,UAE1BvW,yCAQX,kBACWA,KAAKuW,uBAAyB,0CASzC,SAAwBhT,MAChBvD,KAAKuW,uBAAyBvW,KAAKuW,sBAAsBtY,OAAQ,WAC/C+B,KAAKuW,sBAAsBxV,wCACX,SACrBX,MAAMJ,KAAMuD,EAAOhH,8CA5jBhBkD,GCLrB,SAAS+W,GAAQvU,GACpBA,EAAOA,GAAQ,QACVwU,GAAKxU,EAAKyU,KAAO,SACjBC,IAAM1U,EAAK0U,KAAO,SAClBC,OAAS3U,EAAK2U,QAAU,OACxBC,OAAS5U,EAAK4U,OAAS,GAAK5U,EAAK4U,QAAU,EAAI5U,EAAK4U,OAAS,OAC7DC,SAAW,EAQpBN,GAAQ9Z,UAAUqa,SAAW,eACrBN,EAAKzW,KAAKyW,GAAKzS,KAAKgT,IAAIhX,KAAK4W,OAAQ5W,KAAK8W,eAC1C9W,KAAK6W,OAAQ,KACTI,EAAOjT,KAAKkT,SACZC,EAAYnT,KAAKC,MAAMgT,EAAOjX,KAAK6W,OAASJ,GAChDA,EAAoC,IAAN,EAAxBzS,KAAKC,MAAa,GAAPgT,IAAuBR,EAAKU,EAAYV,EAAKU,SAElC,EAAzBnT,KAAK0S,IAAID,EAAIzW,KAAK2W,MAO7BH,GAAQ9Z,UAAU0a,MAAQ,gBACjBN,SAAW,GAOpBN,GAAQ9Z,UAAU2a,OAAS,SAAUX,QAC5BD,GAAKC,GAOdF,GAAQ9Z,UAAU4a,OAAS,SAAUX,QAC5BA,IAAMA,GAOfH,GAAQ9Z,UAAU6a,UAAY,SAAUV,QAC/BA,OAASA,OC1DLW,4CACGzP,EAAK9F,SACTwV,6BAECC,KAAO,KACP5C,KAAO,GACR/M,GAAO,aAAoBA,KAC3B9F,EAAO8F,EACPA,OAAMW,IAEVzG,EAAOA,GAAQ,IACV4F,KAAO5F,EAAK4F,MAAQ,eACpB5F,KAAOA,EACZD,OAA4BC,KACvB0V,cAAmC,IAAtB1V,EAAK0V,gBAClBC,qBAAqB3V,EAAK2V,sBAAwBC,EAAAA,KAClDC,kBAAkB7V,EAAK6V,mBAAqB,OAC5CC,qBAAqB9V,EAAK8V,sBAAwB,OAClDC,oBAAwD,QAAnCP,EAAKxV,EAAK+V,2BAAwC,IAAPP,EAAgBA,EAAK,MACrFQ,QAAU,IAAIzB,GAAQ,CACvBE,IAAKrJ,EAAKyK,oBACVnB,IAAKtJ,EAAK0K,uBACVlB,OAAQxJ,EAAK2K,0BAEZ7O,QAAQ,MAAQlH,EAAKkH,QAAU,IAAQlH,EAAKkH,WAC5C8L,YAAc,WACdlN,IAAMA,MACLmQ,EAAUjW,EAAKkW,QAAUA,YAC1BC,QAAU,IAAIF,EAAQlG,UACtBqG,QAAU,IAAIH,EAAQtF,UACtBiC,cAAoC,IAArB5S,EAAKqW,YACrBjL,EAAKwH,cACLxH,EAAKxE,+CAEb,SAAa0P,UACJlY,UAAUpC,aAEVua,gBAAkBD,EAChBvY,MAFIA,KAAKwY,kDAIpB,SAAqBD,eACP7P,IAAN6P,EACOvY,KAAKyY,4BACXA,sBAAwBF,EACtBvY,uCAEX,SAAkBuY,OACVd,cACM/O,IAAN6P,EACOvY,KAAK0Y,yBACXA,mBAAqBH,EACF,QAAvBd,EAAKzX,KAAKiY,eAA4B,IAAPR,GAAyBA,EAAGJ,OAAOkB,GAC5DvY,yCAEX,SAAoBuY,OACZd,cACM/O,IAAN6P,EACOvY,KAAK2Y,2BACXA,qBAAuBJ,EACJ,QAAvBd,EAAKzX,KAAKiY,eAA4B,IAAPR,GAAyBA,EAAGF,UAAUgB,GAC/DvY,0CAEX,SAAqBuY,OACbd,cACM/O,IAAN6P,EACOvY,KAAK4Y,4BACXA,sBAAwBL,EACL,QAAvBd,EAAKzX,KAAKiY,eAA4B,IAAPR,GAAyBA,EAAGH,OAAOiB,GAC5DvY,6BAEX,SAAQuY,UACClY,UAAUpC,aAEV4a,SAAWN,EACTvY,MAFIA,KAAK6Y,6CAUpB,YAES7Y,KAAK8Y,eACN9Y,KAAKwY,eACqB,IAA1BxY,KAAKiY,QAAQnB,eAERiC,gCAUb,SAAKjZ,kBACIE,KAAKiV,YAAYrN,QAAQ,QAC1B,OAAO5H,UACNsV,OAAS,IAAI0D,GAAOhZ,KAAK+H,IAAK/H,KAAKiC,UAClCgB,EAASjD,KAAKsV,OACdnU,EAAOnB,UACRiV,YAAc,eACdgE,eAAgB,MAEfC,EAAiBvZ,GAAGsD,EAAQ,QAAQ,WACtC9B,EAAKgK,SACLrL,GAAMA,OAGJqZ,EAAWxZ,GAAGsD,EAAQ,SAAS,SAAC8B,GAClC5D,EAAK0I,UACL1I,EAAK8T,YAAc,SACnB3O,EAAKtF,aAAa,QAAS+D,GACvBjF,EACAA,EAAGiF,GAIH5D,EAAKiY,8BAGT,IAAUpZ,KAAK6Y,SAAU,KACnB1P,EAAUnJ,KAAK6Y,SACL,IAAZ1P,GACA+P,QAGEzD,EAAQzV,KAAKmC,cAAa,WAC5B+W,IACAjW,EAAO8D,QAEP9D,EAAOrC,KAAK,QAAS,IAAIgC,MAAM,cAChCuG,GACCnJ,KAAKiC,KAAKmJ,WACVqK,EAAMnK,aAELwJ,KAAK7U,MAAK,WACX8B,aAAa0T,kBAGhBX,KAAK7U,KAAKiZ,QACVpE,KAAK7U,KAAKkZ,GACRnZ,4BAQX,SAAQF,UACGE,KAAK6I,KAAK/I,yBAOrB,gBAES+J,eAEAoL,YAAc,YACdjU,aAAa,YAEZiC,EAASjD,KAAKsV,YACfR,KAAK7U,KAAKN,GAAGsD,EAAQ,OAAQjD,KAAKqZ,OAAOjX,KAAKpC,OAAQL,GAAGsD,EAAQ,OAAQjD,KAAKsZ,OAAOlX,KAAKpC,OAAQL,GAAGsD,EAAQ,QAASjD,KAAK2L,QAAQvJ,KAAKpC,OAAQL,GAAGsD,EAAQ,QAASjD,KAAKuL,QAAQnJ,KAAKpC,OAAQL,GAAGK,KAAKqY,QAAS,UAAWrY,KAAKuZ,UAAUnX,KAAKpC,8BAOvP,gBACSgB,aAAa,8BAOtB,SAAOzE,QACE8b,QAAQmB,IAAIjd,4BAOrB,SAAUgH,QACDvC,aAAa,SAAUuC,0BAOhC,SAAQwB,QACC/D,aAAa,QAAS+D,yBAQ/B,SAAOyN,EAAKvQ,OACJgB,EAASjD,KAAK0X,KAAKlF,UAClBvP,IACDA,EAAS,IAAImK,GAAOpN,KAAMwS,EAAKvQ,QAC1ByV,KAAKlF,GAAOvP,GAEdA,0BAQX,SAASA,iBACQlH,OAAOG,KAAK8D,KAAK0X,qBACN,KAAblF,UACQxS,KAAK0X,KAAKlF,GACdiH,mBAIVC,gCAQT,SAAQnW,WACEmD,EAAiB1G,KAAKoY,QAAQvU,OAAON,GAClCvF,EAAI,EAAGA,EAAI0I,EAAezI,OAAQD,SAClCsX,OAAOhS,MAAMoD,EAAe1I,GAAIuF,EAAOgN,gCAQpD,gBACSuE,KAAK3Y,SAAQ,SAACka,UAAeA,YAC7BvB,KAAK7W,OAAS,OACdoa,QAAQtC,gCAOjB,gBACSkD,eAAgB,OAChBH,eAAgB,OAChBvN,QAAQ,gBACTvL,KAAKsV,QACLtV,KAAKsV,OAAOvO,kCAOpB,kBACW/G,KAAK0Z,gCAOhB,SAAQjX,EAAQC,QACPmH,eACAoO,QAAQb,aACRnC,YAAc,cACdjU,aAAa,QAASyB,EAAQC,GAC/B1C,KAAKwY,gBAAkBxY,KAAKiZ,oBACvBF,qCAQb,yBACQ/Y,KAAK8Y,eAAiB9Y,KAAKiZ,cAC3B,OAAOjZ,SACLmB,EAAOnB,QACTA,KAAKiY,QAAQnB,UAAY9W,KAAKyY,2BACzBR,QAAQb,aACRpW,aAAa,yBACb8X,eAAgB,MAEpB,KACKa,EAAQ3Z,KAAKiY,QAAQlB,gBACtB+B,eAAgB,MACfrD,EAAQzV,KAAKmC,cAAa,WACxBhB,EAAK8X,gBAETpS,EAAK7F,aAAa,oBAAqBG,EAAK8W,QAAQnB,UAEhD3V,EAAK8X,eAET9X,EAAK0H,MAAK,SAAC9D,GACHA,GACA5D,EAAK2X,eAAgB,EACrB3X,EAAK4X,YACLlS,EAAK7F,aAAa,kBAAmB+D,IAGrC5D,EAAKyY,oBAGdD,GACC3Z,KAAKiC,KAAKmJ,WACVqK,EAAMnK,aAELwJ,KAAK7U,MAAK,WACX8B,aAAa0T,kCASzB,eACUoE,EAAU7Z,KAAKiY,QAAQnB,cACxBgC,eAAgB,OAChBb,QAAQb,aACRpW,aAAa,YAAa6Y,UArVVpa,GCAvBqa,GAAQ,GACd,SAAShc,GAAOiK,EAAK9F,GACE,WAAfiP,EAAOnJ,KACP9F,EAAO8F,EACPA,OAAMW,OAYN2L,EATE0F,ECHH,SAAahS,OAAKF,yDAAO,GAAImS,yCAC5B9c,EAAM6K,EAEViS,EAAMA,GAA4B,oBAAbtU,UAA4BA,SAC7C,MAAQqC,IACRA,EAAMiS,EAAIpU,SAAW,KAAOoU,EAAIvN,MAEjB,iBAAR1E,IACH,MAAQA,EAAIxJ,OAAO,KAEfwJ,EADA,MAAQA,EAAIxJ,OAAO,GACbyb,EAAIpU,SAAWmC,EAGfiS,EAAIvN,KAAO1E,GAGpB,sBAAsBkS,KAAKlS,KAExBA,OADA,IAAuBiS,EACjBA,EAAIpU,SAAW,KAAOmC,EAGtB,WAAaA,GAI3B7K,EAAMgP,GAAMnE,IAGX7K,EAAI2I,OACD,cAAcoU,KAAK/c,EAAI0I,UACvB1I,EAAI2I,KAAO,KAEN,eAAeoU,KAAK/c,EAAI0I,YAC7B1I,EAAI2I,KAAO,QAGnB3I,EAAI2K,KAAO3K,EAAI2K,MAAQ,QAEjB4E,GADkC,IAA3BvP,EAAIuP,KAAK7E,QAAQ,KACV,IAAM1K,EAAIuP,KAAO,IAAMvP,EAAIuP,YAE/CvP,EAAI+Q,GAAK/Q,EAAI0I,SAAW,MAAQ6G,EAAO,IAAMvP,EAAI2I,KAAOgC,EAExD3K,EAAIgd,KACAhd,EAAI0I,SACA,MACA6G,GACCuN,GAAOA,EAAInU,OAAS3I,EAAI2I,KAAO,GAAK,IAAM3I,EAAI2I,MAChD3I,ED5CQid,CAAIpS,GADnB9F,EAAOA,GAAQ,IACc4F,MAAQ,cAC/B2E,EAASuN,EAAOvN,OAChByB,EAAK8L,EAAO9L,GACZpG,EAAOkS,EAAOlS,KACduS,EAAgBN,GAAM7L,IAAOpG,KAAQiS,GAAM7L,GAAN,YACrBhM,EAAKoY,UACvBpY,EAAK,0BACL,IAAUA,EAAKqY,WACfF,EAGA/F,EAAK,IAAImD,GAAQhL,EAAQvK,IAGpB6X,GAAM7L,KACP6L,GAAM7L,GAAM,IAAIuJ,GAAQhL,EAAQvK,IAEpCoS,EAAKyF,GAAM7L,IAEX8L,EAAOhX,QAAUd,EAAKc,QACtBd,EAAKc,MAAQgX,EAAO/M,UAEjBqH,EAAGpR,OAAO8W,EAAOlS,KAAM5F,UAIlCuL,EAAc1P,GAAQ,CAClB0Z,QAAAA,GACApK,OAAAA,GACAiH,GAAIvW,GACJkW,QAASlW"}