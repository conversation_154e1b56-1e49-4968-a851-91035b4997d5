{"name": "socket.io-parser", "version": "4.0.4", "description": "socket.io protocol parser", "repository": {"type": "git", "url": "https://github.com/socketio/socket.io-parser.git"}, "files": ["dist/"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"@types/component-emitter": "^1.2.10", "component-emitter": "~1.3.0", "debug": "~4.3.1"}, "devDependencies": {"@babel/core": "~7.9.6", "@babel/preset-env": "~7.9.6", "@types/debug": "^4.1.5", "@types/node": "^14.11.1", "babelify": "~10.0.0", "benchmark": "2.1.2", "expect.js": "0.3.1", "mocha": "3.2.0", "prettier": "^2.1.2", "rimraf": "^3.0.2", "socket.io-browsers": "^1.0.0", "typescript": "^4.0.3", "zuul": "3.11.1", "zuul-ngrok": "4.0.0"}, "scripts": {"compile": "rimraf ./dist && tsc", "test": "npm run format:check && npm run compile && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:node": "mocha --reporter dot --bail test/index.js", "test:browser": "zuul test/index.js --no-coverage", "format:fix": "prettier --write --parser typescript 'lib/**/*.ts' 'test/**/*.js'", "format:check": "prettier --check --parser typescript 'lib/**/*.ts' 'test/**/*.js'", "prepack": "npm run compile"}, "license": "MIT", "engines": {"node": ">=10.0.0"}}