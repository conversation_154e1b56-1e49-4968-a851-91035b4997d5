{"name": "socket.io", "version": "4.5.0", "description": "node.js realtime framework server", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "socket", "io"], "files": ["dist/", "client-dist/", "wrapper.mjs", "!**/*.tsbuildinfo"], "directories": {"doc": "docs/", "example": "example/", "lib": "lib/", "test": "test/"}, "type": "commonjs", "main": "./dist/index.js", "exports": {"import": "./wrapper.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "types": "./dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/socketio/socket.io"}, "scripts": {"compile": "rimraf ./dist && tsc", "test": "npm run format:check && npm run compile && npm run test:types && npm run test:unit", "test:types": "tsd", "test:unit": "nyc mocha --require ts-node/register --reporter spec --slow 200 --bail --timeout 10000 test/socket.io.ts", "format:check": "prettier --check \"lib/**/*.ts\" \"test/**/*.ts\"", "format:fix": "prettier --write \"lib/**/*.ts\" \"test/**/*.ts\"", "prepack": "npm run compile"}, "dependencies": {"accepts": "~1.3.4", "base64id": "~2.0.0", "debug": "~4.3.2", "engine.io": "~6.2.0", "socket.io-adapter": "~2.4.0", "socket.io-parser": "~4.0.4"}, "devDependencies": {"@types/mocha": "^9.0.0", "expect.js": "0.3.1", "mocha": "^3.5.3", "nyc": "^15.1.0", "prettier": "^2.3.2", "rimraf": "^3.0.2", "socket.io-client": "4.5.0", "socket.io-client-v2": "npm:socket.io-client@^2.4.0", "superagent": "^6.1.0", "supertest": "^6.1.6", "ts-node": "^10.2.1", "tsd": "^0.17.0", "typescript": "^4.4.2", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.0.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=10.0.0"}, "tsd": {"directory": "test"}}