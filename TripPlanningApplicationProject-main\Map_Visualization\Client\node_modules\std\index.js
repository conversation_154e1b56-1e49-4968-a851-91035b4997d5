module.exports = {
	Class: require('./Class'),
	bind: require('./bind'),
	curry: require('./curry'),
	throttle: require('./throttle'),
	delay: require('./delay'),
	invoke: require('./invoke'),
	isArray: require('./isArray'),
	each: require('./each'),
	map: require('./map'),
	pick: require('./filter'), // deprecated in favor of filter
	filter: require('./filter'),
	flatten: require('./flatten'),
	extend: require('./extend'),
	slice: require('./slice'),
	pack: require('./pack'),
	unpack: require('./unpack'),
	crc32: require('./crc32'),
	strip: require('./strip')
}
