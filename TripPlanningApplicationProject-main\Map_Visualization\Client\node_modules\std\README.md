std
===

Standard library javascript functionality

Acknowledgements
----------------

- The following functions are taken from phpjs - https://github.com/kvz/phpjs, http://phpjs.org/pages/license: pack, unpack, crc32, utf8_encode
- dom.getOffset is lifted from jQuery with some modification
- dom.getWindowScroll is modified from http://stackoverflow.com/questions/1567327/using-jquery-to-get-elements-position-relative-to-viewport, which is based on a quirksmode.org snippet
- json.js taken from https://github.com/douglascrockford/JSON-js
- trim taken from http://code.google.com/p/closure-library/source/browse/trunk/closure/goog/string/string.js?r=2
- The following functions are taken from nodeunit - https://github.com/caolan/nodeunit, https://github.com/caolan/nodeunit/blob/master/LICENSE: deepEqual
- base64.js implementation taken from @dankogai's https://github.com/dankogai/js-base64, MIT licensed

