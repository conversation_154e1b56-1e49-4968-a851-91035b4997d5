{"name": "socket.io-client", "version": "4.5.0", "description": "Realtime application framework client", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "client"], "files": ["dist/", "build/"], "type": "commonjs", "main": "./build/cjs/index.js", "module": "./build/esm/index.js", "exports": {"./package.json": "./package.json", "./dist/socket.io.js": "./dist/socket.io.js", "./dist/socket.io.js.map": "./dist/socket.io.js.map", ".": {"import": {"node": "./build/esm-debug/index.js", "default": "./build/esm/index.js"}, "require": "./build/cjs/index.js", "types": "./build/esm/index.d.ts"}}, "types": "./build/esm/index.d.ts", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.2", "engine.io-client": "~6.2.1", "socket.io-parser": "~4.2.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/plugin-transform-object-assign": "^7.14.5", "@babel/preset-env": "^7.15.0", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-node-resolve": "^13.0.5", "@sinonjs/fake-timers": "^7.1.2", "@types/mocha": "^9.0.0", "@types/node": "^16.7.6", "@types/sinonjs__fake-timers": "^6.0.3", "babel-eslint": "^10.1.0", "babel-loader": "^8.1.0", "babel-preset-es2015": "6.24.1", "base64-arraybuffer": "^0.1.5", "expect.js": "0.3.1", "has-cors": "^1.1.0", "istanbul": "^0.4.5", "mocha": "^3.3.0", "prettier": "^2.3.2", "rimraf": "^3.0.2", "rollup": "^2.58.0", "rollup-plugin-terser": "^7.0.2", "socket.io": "3.0.0", "socket.io-browsers": "^1.0.0", "socket.io-msgpack-parser": "^3.0.0", "text-blob-builder": "0.0.1", "ts-loader": "^8.3.0", "ts-node": "^10.2.1", "tsd": "^0.17.0", "typescript": "^4.4.2", "webpack": "^4.44.2", "webpack-cli": "^3.3.12", "webpack-remove-debug": "^0.1.0", "zuul": "~3.11.1", "zuul-builder-webpack": "^1.2.0", "zuul-ngrok": "4.0.0"}, "scripts": {"compile": "rimraf ./build && tsc && tsc -p tsconfig.esm.json && ./postcompile.sh", "test": "npm run format:check && npm run compile && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:node": "mocha --require ts-node/register --reporter dot --require test/support/server.js test/index.js", "test:browser": "zuul test/index.js", "test:types": "tsd", "build": "rollup -c support/rollup.config.umd.js && rollup -c support/rollup.config.esm.js && rollup -c support/rollup.config.umd.msgpack.js", "format:check": "prettier --check \"lib/**/*.ts\" \"test/**/*.js\" \"test/**/*.ts\" \"support/**/*.js\"", "format:fix": "prettier --write \"lib/**/*.ts\" \"test/**/*.js\" \"test/**/*.ts\" \"support/**/*.js\"", "prepack": "npm run compile"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/socketio/socket.io-client.git"}, "license": "MIT", "engines": {"node": ">=10.0.0"}, "tsd": {"directory": "test"}, "browser": {"./test/node.ts": false}}