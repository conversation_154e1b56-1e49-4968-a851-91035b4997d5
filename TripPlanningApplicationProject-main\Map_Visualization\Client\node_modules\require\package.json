{"name": "require", "description": "javascript module management! brings node's require statement to the browser", "version": "2.4.20", "homepage": "https://github.com/marcuswestin/require", "main": "./require", "bin": "./bin/require-command.js", "engines": {"node": "*", "browsers": "*"}, "repository": {"type": "git", "url": "git://github.com/marcuswestin/require.git"}, "author": "<PERSON> <<EMAIL>> (http://marcuswest.in)", "dependencies": {"uglify-js": "2.3.0", "std": "0.1.40"}, "devDependencies": {}, "directories": {}}