{"version": 3, "file": "socket.io.esm.min.js", "sources": ["../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export default (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import globalThis from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} options.\n     * @api private\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.readyState = \"\";\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @api protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     *\n     * @api public\n     */\n    open() {\n        if (\"closed\" === this.readyState || \"\" === this.readyState) {\n            this.readyState = \"opening\";\n            this.doOpen();\n        }\n        return this;\n    }\n    /**\n     * Closes the transport.\n     *\n     * @api public\n     */\n    close() {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     * @api public\n     */\n    send(packets) {\n        if (\"open\" === this.readyState) {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @api protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @api protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @api protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @api protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport globalThis from \"../globalThis.js\";\nexport default function (opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport XMLHttpRequest from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport globalThis from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    /**\n     * Transport name.\n     */\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @api private\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} callback upon buffers are flushed and transport is paused\n     * @api private\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @api public\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @api private\n     */\n    onData(data) {\n        const callback = packet => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @api private\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} data packets\n     * @param {Function} drain callback\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, data => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @api private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @api private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @api private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @api public\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @api private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @api private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @api private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @api private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @api public\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import globalThis from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return cb => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @api {Object} connection options\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Transport name.\n     *\n     * @api public\n     */\n    get name() {\n        return \"websocket\";\n    }\n    /**\n     * Opens socket.\n     *\n     * @api private\n     */\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @api private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = closeEvent => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent\n        });\n        this.ws.onmessage = ev => this.onData(ev.data);\n        this.ws.onerror = e => this.onError(\"websocket error\", e);\n    }\n    /**\n     * Writes data to socket.\n     *\n     * @param {Array} array of packets.\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, data => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    /**\n     * Closes socket.\n     *\n     * @api private\n     */\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @api public\n     */\n    check() {\n        return (!!WebSocket &&\n            !(\"__initialize\" in WebSocket && this.name === WS.prototype.name));\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri or options\n     * @param {Object} opts - options\n     * @api public\n     */\n    constructor(uri, opts = {}) {\n        super();\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.readyState = \"\";\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                addEventListener(\"beforeunload\", () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                }, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} transport name\n     * @return {Transport}\n     * @api private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @api private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @api private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", reason => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} transport name\n     * @api private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", msg => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = err => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @api private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState &&\n            this.opts.upgrade &&\n            this.transport.pause) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @api private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @api private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @api private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @api private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @api private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} message.\n     * @param {Function} callback function.\n     * @param {Object} options.\n     * @return {Socket} for chaining.\n     * @api public\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @api private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     *\n     * @api public\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @api private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @api private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} server upgrades\n     * @api private\n     *\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.connected = false;\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        const timeout = this.flags.timeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n        this.id = id;\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * ```\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     * ```\n     *\n     * @returns self\n     * @public\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(event);\n     * });\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(event);\n     * });\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * const handler = (event, ...args) => {\n     *   console.log(event);\n     * }\n     *\n     * socket.onAnyOutgoing(handler);\n     *\n     * // then later\n     * socket.offAnyOutgoing(handler);\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        this.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "obj", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "globalThis", "clearTimeoutFn", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "readyState", "socket", "onError", "open", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "packet", "onPacket", "details", "alphabet", "map", "prev", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Request", "uri", "method", "async", "undefined", "xd", "xscheme", "xs", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "forceBase64", "name", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "schema", "secure", "port", "Number", "timestampRequests", "timestampParam", "b64", "<PERSON><PERSON><PERSON><PERSON>", "hostname", "indexOf", "path", "transports", "websocket", "polling", "location", "isSSL", "protocol", "poll", "pause", "onPause", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "encodePayload", "doWrite", "sid", "request", "assign", "req", "xhrStatus", "pollXhr", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "substr", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "transport", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "shift", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "PacketType", "Decoder", "reviver", "add", "decodeString", "BINARY_EVENT", "BINARY_ACK", "reconstructor", "BinaryReconstructor", "takeBinaryData", "start", "buf", "nsp", "next", "payload", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "static", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "EVENT", "ACK", "destroy", "finishedReconstruction", "reconPack", "binData", "replacer", "encodeAsString", "encodeAsBinary", "stringify", "deconstruction", "unshift", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "auth", "_autoConnect", "disconnected", "subEvents", "subs", "onpacket", "active", "_readyState", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "volatile", "notifyOutgoingListeners", "timer", "_packet", "onconnect", "onevent", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "listener", "sent", "emitBuffered", "subDestroy", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "pow", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "_a", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "Encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "_destroy", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQC,IAC9BH,EAAqBH,EAAaM,IAAQA,KAE9C,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,EAAGT,KAAAA,EAAMC,KAAAA,GAAQS,EAAgBC,KAClD,OAAIT,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTW,EAAmBX,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVK,EAegCZ,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOD,GACnBA,GAAOA,EAAIE,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTW,EAAmB,IAAIT,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,KAxBnCY,IAAAA,GA0BTD,EAAqB,CAACX,EAAMU,KAC9B,MAAMK,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,IC9BpC,IAHA,IAAIsB,EAAQ,mEAERC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC5DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,ECPlC,MAAMnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,CAACC,EAAeC,KACjC,GAA6B,iBAAlBD,EACP,MAAO,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,IAGvC,MAAM/B,EAAO8B,EAAcG,OAAO,GAClC,GAAa,MAATjC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,GAWTmC,EAAqB,CAACjC,EAAM8B,KAC9B,GAAIxB,EAAuB,CACvB,MAAM6B,EDLD,SAAUC,GACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,IAAIG,EAAc,IAAIrC,YAAYkC,GAAeI,EAAQ,IAAIrB,WAAWoB,GACxE,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,ECbaE,CAAO9C,GACvB,OAAO+B,EAAUI,EAASL,GAG1B,MAAO,CAAEM,QAAQ,EAAMpC,KAAAA,IAGzB+B,EAAY,CAAC/B,EAAM8B,IAEZ,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,EC3Cb+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQtC,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIf,KAAOqD,EAAQ/C,UACtBS,EAAIf,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOe,EAfSuC,CAAMvC,GA2BxBsC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,MAaTN,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,WAKjB,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,MAaTN,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKK,UAAUpC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUpC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,IAEpC,IADAyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,GACpB,MAUJ,OAJyB,IAArB0C,EAAUzC,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,MAWTN,EAAQ/C,UAAUkE,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYX,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,GAG1B,GAAI0C,EAEG,CAAI1C,EAAI,EAAb,IAAK,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAML,KAAMc,GAI7B,OAAOd,MAITN,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,IAWzCJ,EAAQ/C,UAAUwE,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAO5B,QCvKlC,MACwB,oBAATkD,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKnE,KAAQoE,GACzB,OAAOA,EAAKC,QAAO,CAACC,EAAKC,KACjBvE,EAAIwE,eAAeD,KACnBD,EAAIC,GAAKvE,EAAIuE,IAEVD,IACR,IAGP,MAAMG,EAAqBC,WACrBC,EAAuBC,aACtB,SAASC,EAAsB7E,EAAK8E,GACnCA,EAAKC,iBACL/E,EAAIgF,aAAeP,EAAmBQ,KAAKC,GAC3ClF,EAAImF,eAAiBR,EAAqBM,KAAKC,KAG/ClF,EAAIgF,aAAeN,WAAWO,KAAKC,GACnClF,EAAImF,eAAiBP,aAAaK,KAAKC,IChB/C,MAAME,UAAuBC,MACzBC,YAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACN3C,KAAK4C,YAAcA,EACnB5C,KAAK6C,QAAUA,EACf7C,KAAKzD,KAAO,kBAGb,MAAMwG,UAAkBrD,EAO3BgD,YAAYR,GACRY,QACA9C,KAAKgD,UAAW,EAChBf,EAAsBjC,KAAMkC,GAC5BlC,KAAKkC,KAAOA,EACZlC,KAAKiD,MAAQf,EAAKe,MAClBjD,KAAKkD,WAAa,GAClBlD,KAAKmD,OAASjB,EAAKiB,OAWvBC,QAAQT,EAAQC,EAAaC,GAEzB,OADAC,MAAM7B,aAAa,QAAS,IAAIuB,EAAeG,EAAQC,EAAaC,IAC7D7C,KAOXqD,OAKI,MAJI,WAAarD,KAAKkD,YAAc,KAAOlD,KAAKkD,aAC5ClD,KAAKkD,WAAa,UAClBlD,KAAKsD,UAEFtD,KAOXuD,QAKI,MAJI,YAAcvD,KAAKkD,YAAc,SAAWlD,KAAKkD,aACjDlD,KAAKwD,UACLxD,KAAKyD,WAEFzD,KAQX0D,KAAKC,GACG,SAAW3D,KAAKkD,YAChBlD,KAAK4D,MAAMD,GAWnBE,SACI7D,KAAKkD,WAAa,OAClBlD,KAAKgD,UAAW,EAChBF,MAAM7B,aAAa,QAQvB6C,OAAOtH,GACH,MAAMuH,EAAS3F,EAAa5B,EAAMwD,KAAKmD,OAAO7E,YAC9C0B,KAAKgE,SAASD,GAOlBC,SAASD,GACLjB,MAAM7B,aAAa,SAAU8C,GAOjCN,QAAQQ,GACJjE,KAAKkD,WAAa,SAClBJ,MAAM7B,aAAa,QAASgD,IC/GpC,MAAMC,EAAW,mEAAmEtG,MAAM,IAAkBuG,EAAM,GAClH,IAAqBC,EAAjBC,EAAO,EAAGpG,EAAI,EAQX,SAASqG,EAAOC,GACnB,IAAIC,EAAU,GACd,GACIA,EAAUN,EAASK,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,GACf,OAAOC,EAsBJ,SAASG,IACZ,MAAMC,EAAMN,GAAQ,IAAIO,MACxB,OAAID,IAAQR,GACDC,EAAO,EAAGD,EAAOQ,GACrBA,EAAM,IAAMN,EAAOD,KAK9B,KAAOpG,EA9CiG,GA8CrFA,IACfkG,EAAID,EAASjG,IAAMA,ECzChB,SAASqG,EAAOlH,GACnB,IAAI0H,EAAM,GACV,IAAK,IAAI7G,KAAKb,EACNA,EAAIwE,eAAe3D,KACf6G,EAAI5G,SACJ4G,GAAO,KACXA,GAAOC,mBAAmB9G,GAAK,IAAM8G,mBAAmB3H,EAAIa,KAGpE,OAAO6G,EChBX,IAAIE,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,eAEjC,MAAOC,IAIA,MAAMC,EAAUH,ECPR,WAAU9C,GACrB,MAAMkD,EAAUlD,EAAKkD,QAErB,IACI,GAAI,oBAAuBH,kBAAoBG,GAAWD,GACtD,OAAO,IAAIF,eAGnB,MAAOI,IACP,IAAKD,EACD,IACI,OAAO,IAAI9C,EAAW,CAAC,UAAUgD,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,KCRf,SAASG,KACT,MAAMC,EAIK,MAHK,IAAIR,EAAe,CAC3BG,SAAS,IAEMM,aAkOhB,MAAMC,UAAgBjG,EAOzBgD,YAAYkD,EAAK1D,GACbY,QACAb,EAAsBjC,KAAMkC,GAC5BlC,KAAKkC,KAAOA,EACZlC,KAAK6F,OAAS3D,EAAK2D,QAAU,MAC7B7F,KAAK4F,IAAMA,EACX5F,KAAK8F,OAAQ,IAAU5D,EAAK4D,MAC5B9F,KAAKxD,UAAOuJ,IAAc7D,EAAK1F,KAAO0F,EAAK1F,KAAO,KAClDwD,KAAK/D,SAOTA,SACI,MAAMiG,EAAOX,EAAKvB,KAAKkC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKkD,UAAYpF,KAAKkC,KAAK8D,GAC3B9D,EAAK+D,UAAYjG,KAAKkC,KAAKgE,GAC3B,MAAMC,EAAOnG,KAAKmG,IAAM,IAAIlB,EAAe/C,GAC3C,IACIiE,EAAI9C,KAAKrD,KAAK6F,OAAQ7F,KAAK4F,IAAK5F,KAAK8F,OACrC,IACI,GAAI9F,KAAKkC,KAAKkE,aAAc,CACxBD,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACvD,IAAK,IAAIpI,KAAK+B,KAAKkC,KAAKkE,aAChBpG,KAAKkC,KAAKkE,aAAaxE,eAAe3D,IACtCkI,EAAIG,iBAAiBrI,EAAG+B,KAAKkC,KAAKkE,aAAanI,KAK/D,MAAOoH,IACP,GAAI,SAAWrF,KAAK6F,OAChB,IACIM,EAAIG,iBAAiB,eAAgB,4BAEzC,MAAOjB,IAEX,IACIc,EAAIG,iBAAiB,SAAU,OAEnC,MAAOjB,IAEH,oBAAqBc,IACrBA,EAAII,gBAAkBvG,KAAKkC,KAAKqE,iBAEhCvG,KAAKkC,KAAKsE,iBACVL,EAAIM,QAAUzG,KAAKkC,KAAKsE,gBAE5BL,EAAIO,mBAAqB,KACjB,IAAMP,EAAIjD,aAEV,MAAQiD,EAAIQ,QAAU,OAASR,EAAIQ,OACnC3G,KAAK4G,SAKL5G,KAAKoC,cAAa,KACdpC,KAAKoD,QAA8B,iBAAf+C,EAAIQ,OAAsBR,EAAIQ,OAAS,KAC5D,KAGXR,EAAIzC,KAAK1D,KAAKxD,MAElB,MAAO6I,GAOH,YAHArF,KAAKoC,cAAa,KACdpC,KAAKoD,QAAQiC,KACd,GAGiB,oBAAbwB,WACP7G,KAAK8G,MAAQnB,EAAQoB,gBACrBpB,EAAQqB,SAAShH,KAAK8G,OAAS9G,MAQvCoD,QAAQ8B,GACJlF,KAAKiB,aAAa,QAASiE,EAAKlF,KAAKmG,KACrCnG,KAAKiH,SAAQ,GAOjBA,QAAQC,GACJ,QAAI,IAAuBlH,KAAKmG,KAAO,OAASnG,KAAKmG,IAArD,CAIA,GADAnG,KAAKmG,IAAIO,mBAAqBlB,EAC1B0B,EACA,IACIlH,KAAKmG,IAAIgB,QAEb,MAAO9B,IAEa,oBAAbwB,iBACAlB,EAAQqB,SAAShH,KAAK8G,OAEjC9G,KAAKmG,IAAM,MAOfS,SACI,MAAMpK,EAAOwD,KAAKmG,IAAIiB,aACT,OAAT5K,IACAwD,KAAKiB,aAAa,OAAQzE,GAC1BwD,KAAKiB,aAAa,WAClBjB,KAAKiH,WAQbE,QACInH,KAAKiH,WAUb,GAPAtB,EAAQoB,cAAgB,EACxBpB,EAAQqB,SAAW,GAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArBzH,iBAAiC,CAE7CA,iBADyB,eAAgByC,EAAa,WAAa,SAChCgF,GAAe,GAG1D,SAASA,IACL,IAAK,IAAIrJ,KAAK0H,EAAQqB,SACdrB,EAAQqB,SAASpF,eAAe3D,IAChC0H,EAAQqB,SAAS/I,GAAGkJ,QC9YzB,MAAMI,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE/G,GAAM8G,QAAQC,UAAUC,KAAKhH,GAG7B,CAACA,EAAI0B,IAAiBA,EAAa1B,EAAI,GAGzCiH,EAAYrF,EAAWqF,WAAarF,EAAWsF,aCHtDC,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACf,MAAMC,UAAWlF,EAOpBL,YAAYR,GACRY,MAAMZ,GACNlC,KAAK/C,gBAAkBiF,EAAKgG,YAO5BC,WACA,MAAO,YAOX7E,SACI,IAAKtD,KAAKoI,QAEN,OAEJ,MAAMxC,EAAM5F,KAAK4F,MACXyC,EAAYrI,KAAKkC,KAAKmG,UAEtBnG,EAAO2F,EACP,GACAtG,EAAKvB,KAAKkC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMlC,KAAKkC,KAAKkE,eACVlE,EAAKoG,QAAUtI,KAAKkC,KAAKkE,cAE7B,IACIpG,KAAKuI,GACyBV,EAIpB,IAAIF,EAAU/B,EAAKyC,EAAWnG,GAH9BmG,EACI,IAAIV,EAAU/B,EAAKyC,GACnB,IAAIV,EAAU/B,GAGhC,MAAOV,GACH,OAAOlF,KAAKiB,aAAa,QAASiE,GAEtClF,KAAKuI,GAAGjK,WAAa0B,KAAKmD,OAAO7E,YD/CR,cCgDzB0B,KAAKwI,oBAOTA,oBACIxI,KAAKuI,GAAGE,OAAS,KACTzI,KAAKkC,KAAKwG,WACV1I,KAAKuI,GAAGI,QAAQC,QAEpB5I,KAAK6D,UAET7D,KAAKuI,GAAGM,QAAUC,GAAc9I,KAAKyD,QAAQ,CACzCb,YAAa,8BACbC,QAASiG,IAEb9I,KAAKuI,GAAGQ,UAAYC,GAAMhJ,KAAK8D,OAAOkF,EAAGxM,MACzCwD,KAAKuI,GAAGU,QAAU5D,GAAKrF,KAAKoD,QAAQ,kBAAmBiC,GAQ3DzB,MAAMD,GACF3D,KAAKgD,UAAW,EAGhB,IAAK,IAAI/E,EAAI,EAAGA,EAAI0F,EAAQzF,OAAQD,IAAK,CACrC,MAAM8F,EAASJ,EAAQ1F,GACjBiL,EAAajL,IAAM0F,EAAQzF,OAAS,EAC1ClB,EAAa+G,EAAQ/D,KAAK/C,gBAAgBT,IAmBtC,IAGQwD,KAAKuI,GAAG7E,KAAKlH,GAMrB,MAAO6I,IAEH6D,GAGA3B,GAAS,KACLvH,KAAKgD,UAAW,EAChBhD,KAAKiB,aAAa,WACnBjB,KAAKoC,kBAUxBoB,eAC2B,IAAZxD,KAAKuI,KACZvI,KAAKuI,GAAGhF,QACRvD,KAAKuI,GAAK,MAQlB3C,MACI,IAAI3C,EAAQjD,KAAKiD,OAAS,GAC1B,MAAMkG,EAASnJ,KAAKkC,KAAKkH,OAAS,MAAQ,KAC1C,IAAIC,EAAO,GAEPrJ,KAAKkC,KAAKmH,OACR,QAAUF,GAAqC,MAA3BG,OAAOtJ,KAAKkC,KAAKmH,OAClC,OAASF,GAAqC,KAA3BG,OAAOtJ,KAAKkC,KAAKmH,SACzCA,EAAO,IAAMrJ,KAAKkC,KAAKmH,MAGvBrJ,KAAKkC,KAAKqH,oBACVtG,EAAMjD,KAAKkC,KAAKsH,gBAAkB7E,KAGjC3E,KAAK/C,iBACNgG,EAAMwG,IAAM,GAEhB,MAAMC,EAAepF,EAAOrB,GAE5B,OAAQkG,EACJ,QAF8C,IAArCnJ,KAAKkC,KAAKyH,SAASC,QAAQ,KAG5B,IAAM5J,KAAKkC,KAAKyH,SAAW,IAAM3J,KAAKkC,KAAKyH,UACnDN,EACArJ,KAAKkC,KAAK2H,MACTH,EAAaxL,OAAS,IAAMwL,EAAe,IAQpDtB,QACI,SAAUT,GACJ,iBAAkBA,GAAa3H,KAAKmI,OAASF,EAAGtL,UAAUwL,OCxLjE,MAAM2B,EAAa,CACtBC,UAAW9B,EACX+B,QHWG,cAAsBjH,EAOzBL,YAAYR,GAGR,GAFAY,MAAMZ,GACNlC,KAAKgK,SAAU,EACS,oBAAbC,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAId,EAAOY,SAASZ,KAEfA,IACDA,EAAOa,EAAQ,MAAQ,MAE3BlK,KAAKgG,GACoB,oBAAbiE,UACJ/H,EAAKyH,WAAaM,SAASN,UAC3BN,IAASnH,EAAKmH,KACtBrJ,KAAKkG,GAAKhE,EAAKkH,SAAWc,EAK9B,MAAMhC,EAAchG,GAAQA,EAAKgG,YACjClI,KAAK/C,eAAiBwI,IAAYyC,EAKlCC,WACA,MAAO,UAQX7E,SACItD,KAAKoK,OAQTC,MAAMC,GACFtK,KAAKkD,WAAa,UAClB,MAAMmH,EAAQ,KACVrK,KAAKkD,WAAa,SAClBoH,KAEJ,GAAItK,KAAKgK,UAAYhK,KAAKgD,SAAU,CAChC,IAAIuH,EAAQ,EACRvK,KAAKgK,UACLO,IACAvK,KAAKG,KAAK,gBAAgB,aACpBoK,GAASF,QAGdrK,KAAKgD,WACNuH,IACAvK,KAAKG,KAAK,SAAS,aACboK,GAASF,aAKnBA,IAQRD,OACIpK,KAAKgK,SAAU,EACfhK,KAAKwK,SACLxK,KAAKiB,aAAa,QAOtB6C,OAAOtH,GTvFW,EAACiO,EAAgBnM,KACnC,MAAMoM,EAAiBD,EAAe7M,MAAM2B,GACtCoE,EAAU,GAChB,IAAK,IAAI1F,EAAI,EAAGA,EAAIyM,EAAexM,OAAQD,IAAK,CAC5C,MAAM0M,EAAgBvM,EAAasM,EAAezM,GAAIK,GAEtD,GADAqF,EAAQzD,KAAKyK,GACc,UAAvBA,EAAcpO,KACd,MAGR,OAAOoH,GS4FHiH,CAAcpO,EAAMwD,KAAKmD,OAAO7E,YAAYlC,SAd3B2H,IAMb,GAJI,YAAc/D,KAAKkD,YAA8B,SAAhBa,EAAOxH,MACxCyD,KAAK6D,SAGL,UAAYE,EAAOxH,KAEnB,OADAyD,KAAKyD,QAAQ,CAAEb,YAAa,oCACrB,EAGX5C,KAAKgE,SAASD,MAKd,WAAa/D,KAAKkD,aAElBlD,KAAKgK,SAAU,EACfhK,KAAKiB,aAAa,gBACd,SAAWjB,KAAKkD,YAChBlD,KAAKoK,QAWjB5G,UACI,MAAMD,EAAQ,KACVvD,KAAK4D,MAAM,CAAC,CAAErH,KAAM,YAEpB,SAAWyD,KAAKkD,WAChBK,IAKAvD,KAAKG,KAAK,OAAQoD,GAU1BK,MAAMD,GACF3D,KAAKgD,UAAW,ET5JF,EAACW,EAASzG,KAE5B,MAAMgB,EAASyF,EAAQzF,OACjBwM,EAAiB,IAAI3J,MAAM7C,GACjC,IAAI2M,EAAQ,EACZlH,EAAQvH,SAAQ,CAAC2H,EAAQ9F,KAErBjB,EAAa+G,GAAQ,GAAO1F,IACxBqM,EAAezM,GAAKI,IACdwM,IAAU3M,GACZhB,EAASwN,EAAenF,KAAKhG,WSmJrCuL,CAAcnH,GAASnH,IACnBwD,KAAK+K,QAAQvO,GAAM,KACfwD,KAAKgD,UAAW,EAChBhD,KAAKiB,aAAa,eAS9B2E,MACI,IAAI3C,EAAQjD,KAAKiD,OAAS,GAC1B,MAAMkG,EAASnJ,KAAKkC,KAAKkH,OAAS,QAAU,OAC5C,IAAIC,EAAO,IAEP,IAAUrJ,KAAKkC,KAAKqH,oBACpBtG,EAAMjD,KAAKkC,KAAKsH,gBAAkB7E,KAEjC3E,KAAK/C,gBAAmBgG,EAAM+H,MAC/B/H,EAAMwG,IAAM,GAGZzJ,KAAKkC,KAAKmH,OACR,UAAYF,GAAqC,MAA3BG,OAAOtJ,KAAKkC,KAAKmH,OACpC,SAAWF,GAAqC,KAA3BG,OAAOtJ,KAAKkC,KAAKmH,SAC3CA,EAAO,IAAMrJ,KAAKkC,KAAKmH,MAE3B,MAAMK,EAAepF,EAAOrB,GAE5B,OAAQkG,EACJ,QAF8C,IAArCnJ,KAAKkC,KAAKyH,SAASC,QAAQ,KAG5B,IAAM5J,KAAKkC,KAAKyH,SAAW,IAAM3J,KAAKkC,KAAKyH,UACnDN,EACArJ,KAAKkC,KAAK2H,MACTH,EAAaxL,OAAS,IAAMwL,EAAe,IAQpDuB,QAAQ/I,EAAO,IAEX,OADAlG,OAAOkP,OAAOhJ,EAAM,CAAE8D,GAAIhG,KAAKgG,GAAIE,GAAIlG,KAAKkG,IAAMlG,KAAKkC,MAChD,IAAIyD,EAAQ3F,KAAK4F,MAAO1D,GASnC6I,QAAQvO,EAAMuD,GACV,MAAMoL,EAAMnL,KAAKiL,QAAQ,CACrBpF,OAAQ,OACRrJ,KAAMA,IAEV2O,EAAIvL,GAAG,UAAWG,GAClBoL,EAAIvL,GAAG,SAAS,CAACwL,EAAWvI,KACxB7C,KAAKoD,QAAQ,iBAAkBgI,EAAWvI,MAQlD2H,SACI,MAAMW,EAAMnL,KAAKiL,UACjBE,EAAIvL,GAAG,OAAQI,KAAK8D,OAAOzB,KAAKrC,OAChCmL,EAAIvL,GAAG,SAAS,CAACwL,EAAWvI,KACxB7C,KAAKoD,QAAQ,iBAAkBgI,EAAWvI,MAE9C7C,KAAKqL,QAAUF,KIrOjBG,EAAK,0OACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM1G,GAClB,MAAM2G,EAAM3G,EAAK4G,EAAI5G,EAAI8E,QAAQ,KAAMvE,EAAIP,EAAI8E,QAAQ,MAC7C,GAAN8B,IAAiB,GAANrG,IACXP,EAAMA,EAAIpG,UAAU,EAAGgN,GAAK5G,EAAIpG,UAAUgN,EAAGrG,GAAGsG,QAAQ,KAAM,KAAO7G,EAAIpG,UAAU2G,EAAGP,EAAI5G,SAE9F,IAAI0N,EAAIN,EAAGO,KAAK/G,GAAO,IAAKc,EAAM,GAAI3H,EAAI,GAC1C,KAAOA,KACH2H,EAAI2F,EAAMtN,IAAM2N,EAAE3N,IAAM,GAU5B,OARU,GAANyN,IAAiB,GAANrG,IACXO,EAAIkG,OAASL,EACb7F,EAAImG,KAAOnG,EAAImG,KAAKrN,UAAU,EAAGkH,EAAImG,KAAK7N,OAAS,GAAGyN,QAAQ,KAAM,KACpE/F,EAAIoG,UAAYpG,EAAIoG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9E/F,EAAIqG,SAAU,GAElBrG,EAAIsG,UAIR,SAAmB9O,EAAKyM,GACpB,MAAMsC,EAAO,WAAYC,EAAQvC,EAAK8B,QAAQQ,EAAM,KAAKvO,MAAM,KACtC,KAArBiM,EAAKwC,OAAO,EAAG,IAA6B,IAAhBxC,EAAK3L,QACjCkO,EAAMxL,OAAO,EAAG,GAEmB,KAAnCiJ,EAAKwC,OAAOxC,EAAK3L,OAAS,EAAG,IAC7BkO,EAAMxL,OAAOwL,EAAMlO,OAAS,EAAG,GAEnC,OAAOkO,EAZSF,CAAUtG,EAAKA,EAAU,MACzCA,EAAI0G,SAaR,SAAkB1G,EAAK3C,GACnB,MAAMzG,EAAO,GAMb,OALAyG,EAAM0I,QAAQ,6BAA6B,SAAUY,EAAIC,EAAIC,GACrDD,IACAhQ,EAAKgQ,GAAMC,MAGZjQ,EApBQ8P,CAAS1G,EAAKA,EAAW,OACjCA,ECtBJ,MAAM8G,UAAehN,EAQxBgD,YAAYkD,EAAK1D,EAAO,IACpBY,QACI8C,GAAO,iBAAoBA,IAC3B1D,EAAO0D,EACPA,EAAM,MAENA,GACAA,EAAM4F,EAAM5F,GACZ1D,EAAKyH,SAAW/D,EAAImG,KACpB7J,EAAKkH,OAA0B,UAAjBxD,EAAIuE,UAAyC,QAAjBvE,EAAIuE,SAC9CjI,EAAKmH,KAAOzD,EAAIyD,KACZzD,EAAI3C,QACJf,EAAKe,MAAQ2C,EAAI3C,QAEhBf,EAAK6J,OACV7J,EAAKyH,SAAW6B,EAAMtJ,EAAK6J,MAAMA,MAErC9J,EAAsBjC,KAAMkC,GAC5BlC,KAAKoJ,OACD,MAAQlH,EAAKkH,OACPlH,EAAKkH,OACe,oBAAba,UAA4B,WAAaA,SAASE,SAC/DjI,EAAKyH,WAAazH,EAAKmH,OAEvBnH,EAAKmH,KAAOrJ,KAAKoJ,OAAS,MAAQ,MAEtCpJ,KAAK2J,SACDzH,EAAKyH,WACoB,oBAAbM,SAA2BA,SAASN,SAAW,aAC/D3J,KAAKqJ,KACDnH,EAAKmH,OACoB,oBAAbY,UAA4BA,SAASZ,KACvCY,SAASZ,KACTrJ,KAAKoJ,OACD,MACA,MAClBpJ,KAAK8J,WAAa5H,EAAK4H,YAAc,CAAC,UAAW,aACjD9J,KAAKkD,WAAa,GAClBlD,KAAK2M,YAAc,GACnB3M,KAAK4M,cAAgB,EACrB5M,KAAKkC,KAAOlG,OAAOkP,OAAO,CACtBrB,KAAM,aACNgD,OAAO,EACPtG,iBAAiB,EACjBuG,SAAS,EACTtD,eAAgB,IAChBuD,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,GAClBC,qBAAqB,GACtBlL,GACHlC,KAAKkC,KAAK2H,KAAO7J,KAAKkC,KAAK2H,KAAK8B,QAAQ,MAAO,IAAM,IACtB,iBAApB3L,KAAKkC,KAAKe,QACjBjD,KAAKkC,KAAKe,MR7Cf,SAAgBoK,GACnB,IAAIC,EAAM,GACNC,EAAQF,EAAGzP,MAAM,KACrB,IAAK,IAAIK,EAAI,EAAGuP,EAAID,EAAMrP,OAAQD,EAAIuP,EAAGvP,IAAK,CAC1C,IAAIwP,EAAOF,EAAMtP,GAAGL,MAAM,KAC1B0P,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE/D,OAAOH,EQsCmBhO,CAAOU,KAAKkC,KAAKe,QAGvCjD,KAAK2N,GAAK,KACV3N,KAAK4N,SAAW,KAChB5N,KAAK6N,aAAe,KACpB7N,KAAK8N,YAAc,KAEnB9N,KAAK+N,iBAAmB,KACQ,mBAArBlO,mBACHG,KAAKkC,KAAKkL,qBAIVvN,iBAAiB,gBAAgB,KACzBG,KAAKgO,YAELhO,KAAKgO,UAAUxN,qBACfR,KAAKgO,UAAUzK,YAEpB,GAEe,cAAlBvD,KAAK2J,WACL3J,KAAKiO,qBAAuB,KACxBjO,KAAKyD,QAAQ,kBAAmB,CAC5Bb,YAAa,6BAGrB/C,iBAAiB,UAAWG,KAAKiO,sBAAsB,KAG/DjO,KAAKqD,OAST6K,gBAAgB/F,GACZ,MAAMlF,EAAQjH,OAAOkP,OAAO,GAAIlL,KAAKkC,KAAKe,OAE1CA,EAAMkL,IdnFU,EcqFhBlL,EAAM+K,UAAY7F,EAEdnI,KAAK2N,KACL1K,EAAM+H,IAAMhL,KAAK2N,IACrB,MAAMzL,EAAOlG,OAAOkP,OAAO,GAAIlL,KAAKkC,KAAKiL,iBAAiBhF,GAAOnI,KAAKkC,KAAM,CACxEe,MAAAA,EACAE,OAAQnD,KACR2J,SAAU3J,KAAK2J,SACfP,OAAQpJ,KAAKoJ,OACbC,KAAMrJ,KAAKqJ,OAEf,OAAO,IAAIS,EAAW3B,GAAMjG,GAOhCmB,OACI,IAAI2K,EACJ,GAAIhO,KAAKkC,KAAK6K,iBACVL,EAAO0B,wBACmC,IAA1CpO,KAAK8J,WAAWF,QAAQ,aACxBoE,EAAY,gBAEX,CAAA,GAAI,IAAMhO,KAAK8J,WAAW5L,OAK3B,YAHA8B,KAAKoC,cAAa,KACdpC,KAAKiB,aAAa,QAAS,6BAC5B,GAIH+M,EAAYhO,KAAK8J,WAAW,GAEhC9J,KAAKkD,WAAa,UAElB,IACI8K,EAAYhO,KAAKkO,gBAAgBF,GAErC,MAAO3I,GAGH,OAFArF,KAAK8J,WAAWuE,aAChBrO,KAAKqD,OAGT2K,EAAU3K,OACVrD,KAAKsO,aAAaN,GAOtBM,aAAaN,GACLhO,KAAKgO,WACLhO,KAAKgO,UAAUxN,qBAGnBR,KAAKgO,UAAYA,EAEjBA,EACKpO,GAAG,QAASI,KAAKuO,QAAQlM,KAAKrC,OAC9BJ,GAAG,SAAUI,KAAKgE,SAAS3B,KAAKrC,OAChCJ,GAAG,QAASI,KAAKoD,QAAQf,KAAKrC,OAC9BJ,GAAG,SAAS+C,GAAU3C,KAAKyD,QAAQ,kBAAmBd,KAQ/D6L,MAAMrG,GACF,IAAI6F,EAAYhO,KAAKkO,gBAAgB/F,GACjCsG,GAAS,EACb/B,EAAO0B,uBAAwB,EAC/B,MAAMM,EAAkB,KAChBD,IAEJT,EAAUtK,KAAK,CAAC,CAAEnH,KAAM,OAAQC,KAAM,WACtCwR,EAAU7N,KAAK,UAAUwO,IACrB,IAAIF,EAEJ,GAAI,SAAWE,EAAIpS,MAAQ,UAAYoS,EAAInS,KAAM,CAG7C,GAFAwD,KAAK4O,WAAY,EACjB5O,KAAKiB,aAAa,YAAa+M,IAC1BA,EACD,OACJtB,EAAO0B,sBAAwB,cAAgBJ,EAAU7F,KACzDnI,KAAKgO,UAAU3D,OAAM,KACboE,GAEA,WAAazO,KAAKkD,aAEtB+D,IACAjH,KAAKsO,aAAaN,GAClBA,EAAUtK,KAAK,CAAC,CAAEnH,KAAM,aACxByD,KAAKiB,aAAa,UAAW+M,GAC7BA,EAAY,KACZhO,KAAK4O,WAAY,EACjB5O,KAAK6O,gBAGR,CACD,MAAM3J,EAAM,IAAIzC,MAAM,eAEtByC,EAAI8I,UAAYA,EAAU7F,KAC1BnI,KAAKiB,aAAa,eAAgBiE,SAI9C,SAAS4J,IACDL,IAGJA,GAAS,EACTxH,IACA+G,EAAUzK,QACVyK,EAAY,MAGhB,MAAM/E,EAAU/D,IACZ,MAAM6J,EAAQ,IAAItM,MAAM,gBAAkByC,GAE1C6J,EAAMf,UAAYA,EAAU7F,KAC5B2G,IACA9O,KAAKiB,aAAa,eAAgB8N,IAEtC,SAASC,IACL/F,EAAQ,oBAGZ,SAASJ,IACLI,EAAQ,iBAGZ,SAASgG,EAAUC,GACXlB,GAAakB,EAAG/G,OAAS6F,EAAU7F,MACnC2G,IAIR,MAAM7H,EAAU,KACZ+G,EAAUzN,eAAe,OAAQmO,GACjCV,EAAUzN,eAAe,QAAS0I,GAClC+E,EAAUzN,eAAe,QAASyO,GAClChP,KAAKI,IAAI,QAASyI,GAClB7I,KAAKI,IAAI,YAAa6O,IAE1BjB,EAAU7N,KAAK,OAAQuO,GACvBV,EAAU7N,KAAK,QAAS8I,GACxB+E,EAAU7N,KAAK,QAAS6O,GACxBhP,KAAKG,KAAK,QAAS0I,GACnB7I,KAAKG,KAAK,YAAa8O,GACvBjB,EAAU3K,OAOdQ,SAOI,GANA7D,KAAKkD,WAAa,OAClBwJ,EAAO0B,sBAAwB,cAAgBpO,KAAKgO,UAAU7F,KAC9DnI,KAAKiB,aAAa,QAClBjB,KAAK6O,QAGD,SAAW7O,KAAKkD,YAChBlD,KAAKkC,KAAK4K,SACV9M,KAAKgO,UAAU3D,MAAO,CACtB,IAAIpM,EAAI,EACR,MAAMuP,EAAIxN,KAAK4N,SAAS1P,OACxB,KAAOD,EAAIuP,EAAGvP,IACV+B,KAAKwO,MAAMxO,KAAK4N,SAAS3P,KASrC+F,SAASD,GACL,GAAI,YAAc/D,KAAKkD,YACnB,SAAWlD,KAAKkD,YAChB,YAAclD,KAAKkD,WAInB,OAHAlD,KAAKiB,aAAa,SAAU8C,GAE5B/D,KAAKiB,aAAa,aACV8C,EAAOxH,MACX,IAAK,OACDyD,KAAKmP,YAAYC,KAAK5D,MAAMzH,EAAOvH,OACnC,MACJ,IAAK,OACDwD,KAAKqP,mBACLrP,KAAKsP,WAAW,QAChBtP,KAAKiB,aAAa,QAClBjB,KAAKiB,aAAa,QAClB,MACJ,IAAK,QACD,MAAMiE,EAAM,IAAIzC,MAAM,gBAEtByC,EAAIqK,KAAOxL,EAAOvH,KAClBwD,KAAKoD,QAAQ8B,GACb,MACJ,IAAK,UACDlF,KAAKiB,aAAa,OAAQ8C,EAAOvH,MACjCwD,KAAKiB,aAAa,UAAW8C,EAAOvH,OAapD2S,YAAY3S,GACRwD,KAAKiB,aAAa,YAAazE,GAC/BwD,KAAK2N,GAAKnR,EAAKwO,IACfhL,KAAKgO,UAAU/K,MAAM+H,IAAMxO,EAAKwO,IAChChL,KAAK4N,SAAW5N,KAAKwP,eAAehT,EAAKoR,UACzC5N,KAAK6N,aAAerR,EAAKqR,aACzB7N,KAAK8N,YAActR,EAAKsR,YACxB9N,KAAKyP,WAAajT,EAAKiT,WACvBzP,KAAK6D,SAED,WAAa7D,KAAKkD,YAEtBlD,KAAKqP,mBAOTA,mBACIrP,KAAKuC,eAAevC,KAAK+N,kBACzB/N,KAAK+N,iBAAmB/N,KAAKoC,cAAa,KACtCpC,KAAKyD,QAAQ,kBACdzD,KAAK6N,aAAe7N,KAAK8N,aACxB9N,KAAKkC,KAAKwG,WACV1I,KAAK+N,iBAAiBnF,QAQ9B2F,UACIvO,KAAK2M,YAAY/L,OAAO,EAAGZ,KAAK4M,eAIhC5M,KAAK4M,cAAgB,EACjB,IAAM5M,KAAK2M,YAAYzO,OACvB8B,KAAKiB,aAAa,SAGlBjB,KAAK6O,QAQbA,QACI,GAAI,WAAa7O,KAAKkD,YAClBlD,KAAKgO,UAAUhL,WACdhD,KAAK4O,WACN5O,KAAK2M,YAAYzO,OAAQ,CACzB,MAAMyF,EAAU3D,KAAK0P,qBACrB1P,KAAKgO,UAAUtK,KAAKC,GAGpB3D,KAAK4M,cAAgBjJ,EAAQzF,OAC7B8B,KAAKiB,aAAa,UAS1ByO,qBAII,KAH+B1P,KAAKyP,YACR,YAAxBzP,KAAKgO,UAAU7F,MACfnI,KAAK2M,YAAYzO,OAAS,GAE1B,OAAO8B,KAAK2M,YAEhB,IAAIgD,EAAc,EAClB,IAAK,IAAI1R,EAAI,EAAGA,EAAI+B,KAAK2M,YAAYzO,OAAQD,IAAK,CAC9C,MAAMzB,EAAOwD,KAAK2M,YAAY1O,GAAGzB,KAIjC,GAHIA,IACAmT,GXvYO,iBADIvS,EWwYeZ,GXjY1C,SAAoBsI,GAChB,IAAI8K,EAAI,EAAG1R,EAAS,EACpB,IAAK,IAAID,EAAI,EAAGuP,EAAI1I,EAAI5G,OAAQD,EAAIuP,EAAGvP,IACnC2R,EAAI9K,EAAI3G,WAAWF,GACf2R,EAAI,IACJ1R,GAAU,EAEL0R,EAAI,KACT1R,GAAU,EAEL0R,EAAI,OAAUA,GAAK,MACxB1R,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,EAvBI2R,CAAWzS,GAGfqH,KAAKqL,KAPQ,MAOF1S,EAAI2S,YAAc3S,EAAI4S,QWqY5B/R,EAAI,GAAK0R,EAAc3P,KAAKyP,WAC5B,OAAOzP,KAAK2M,YAAY3L,MAAM,EAAG/C,GAErC0R,GAAe,EX7YpB,IAAoBvS,EW+YnB,OAAO4C,KAAK2M,YAWhB/I,MAAM+K,EAAKsB,EAASlQ,GAEhB,OADAC,KAAKsP,WAAW,UAAWX,EAAKsB,EAASlQ,GAClCC,KAEX0D,KAAKiL,EAAKsB,EAASlQ,GAEf,OADAC,KAAKsP,WAAW,UAAWX,EAAKsB,EAASlQ,GAClCC,KAWXsP,WAAW/S,EAAMC,EAAMyT,EAASlQ,GAS5B,GARI,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAOuJ,GAEP,mBAAsBkK,IACtBlQ,EAAKkQ,EACLA,EAAU,MAEV,YAAcjQ,KAAKkD,YAAc,WAAalD,KAAKkD,WACnD,QAEJ+M,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAMnM,EAAS,CACXxH,KAAMA,EACNC,KAAMA,EACNyT,QAASA,GAEbjQ,KAAKiB,aAAa,eAAgB8C,GAClC/D,KAAK2M,YAAYzM,KAAK6D,GAClBhE,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK6O,QAOTtL,QACI,MAAMA,EAAQ,KACVvD,KAAKyD,QAAQ,gBACbzD,KAAKgO,UAAUzK,SAEb4M,EAAkB,KACpBnQ,KAAKI,IAAI,UAAW+P,GACpBnQ,KAAKI,IAAI,eAAgB+P,GACzB5M,KAEE6M,EAAiB,KAEnBpQ,KAAKG,KAAK,UAAWgQ,GACrBnQ,KAAKG,KAAK,eAAgBgQ,IAqB9B,MAnBI,YAAcnQ,KAAKkD,YAAc,SAAWlD,KAAKkD,aACjDlD,KAAKkD,WAAa,UACdlD,KAAK2M,YAAYzO,OACjB8B,KAAKG,KAAK,SAAS,KACXH,KAAK4O,UACLwB,IAGA7M,OAIHvD,KAAK4O,UACVwB,IAGA7M,KAGDvD,KAOXoD,QAAQ8B,GACJwH,EAAO0B,uBAAwB,EAC/BpO,KAAKiB,aAAa,QAASiE,GAC3BlF,KAAKyD,QAAQ,kBAAmByB,GAOpCzB,QAAQd,EAAQC,GACR,YAAc5C,KAAKkD,YACnB,SAAWlD,KAAKkD,YAChB,YAAclD,KAAKkD,aAEnBlD,KAAKuC,eAAevC,KAAK+N,kBAEzB/N,KAAKgO,UAAUxN,mBAAmB,SAElCR,KAAKgO,UAAUzK,QAEfvD,KAAKgO,UAAUxN,qBACoB,mBAAxBC,qBACPA,oBAAoB,UAAWT,KAAKiO,sBAAsB,GAG9DjO,KAAKkD,WAAa,SAElBlD,KAAK2N,GAAK,KAEV3N,KAAKiB,aAAa,QAAS0B,EAAQC,GAGnC5C,KAAK2M,YAAc,GACnB3M,KAAK4M,cAAgB,GAU7B4C,eAAe5B,GACX,MAAMyC,EAAmB,GACzB,IAAIpS,EAAI,EACR,MAAMqS,EAAI1C,EAAS1P,OACnB,KAAOD,EAAIqS,EAAGrS,KACL+B,KAAK8J,WAAWF,QAAQgE,EAAS3P,KAClCoS,EAAiBnQ,KAAK0N,EAAS3P,IAEvC,OAAOoS,GAGf3D,EAAOvC,SdpiBiB,Ee9BxB,MAAMrN,EAA+C,mBAAhBC,YAM/BH,EAAWZ,OAAOW,UAAUC,SAC5BH,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,EAASC,KAAKH,MAChB6T,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxB5T,EAASC,KAAK2T,MAMf,SAASC,EAASrT,GACrB,OAASN,IAA0BM,aAAeL,aAlBvC,CAACK,GACyB,mBAAvBL,YAAYM,OACpBN,YAAYM,OAAOD,GACnBA,EAAIE,kBAAkBP,YAeqCM,CAAOD,KACnEX,GAAkBW,aAAeV,MACjC6T,GAAkBnT,aAAeoT,KAEnC,SAASE,EAAUtT,EAAKuT,GAC3B,IAAKvT,GAAsB,iBAARA,EACf,OAAO,EAEX,GAAI2D,MAAM6P,QAAQxT,GAAM,CACpB,IAAK,IAAIa,EAAI,EAAGuP,EAAIpQ,EAAIc,OAAQD,EAAIuP,EAAGvP,IACnC,GAAIyS,EAAUtT,EAAIa,IACd,OAAO,EAGf,OAAO,EAEX,GAAIwS,EAASrT,GACT,OAAO,EAEX,GAAIA,EAAIuT,QACkB,mBAAfvT,EAAIuT,QACU,IAArBrQ,UAAUpC,OACV,OAAOwS,EAAUtT,EAAIuT,UAAU,GAEnC,IAAK,MAAMtU,KAAOe,EACd,GAAIpB,OAAOW,UAAUiF,eAAe/E,KAAKO,EAAKf,IAAQqU,EAAUtT,EAAIf,IAChE,OAAO,EAGf,OAAO,ECxCJ,SAASwU,EAAkB9M,GAC9B,MAAM+M,EAAU,GACVC,EAAahN,EAAOvH,KACpBwU,EAAOjN,EAGb,OAFAiN,EAAKxU,KAAOyU,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQ5S,OACpB,CAAE6F,OAAQiN,EAAMF,QAASA,GAEpC,SAASG,GAAmBzU,EAAMsU,GAC9B,IAAKtU,EACD,OAAOA,EACX,GAAIiU,EAASjU,GAAO,CAChB,MAAM2U,EAAc,CAAEC,cAAc,EAAM7M,IAAKuM,EAAQ5S,QAEvD,OADA4S,EAAQ5Q,KAAK1D,GACN2U,EAEN,GAAIpQ,MAAM6P,QAAQpU,GAAO,CAC1B,MAAM6U,EAAU,IAAItQ,MAAMvE,EAAK0B,QAC/B,IAAK,IAAID,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BoT,EAAQpT,GAAKgT,GAAmBzU,EAAKyB,GAAI6S,GAE7C,OAAOO,EAEN,GAAoB,iBAAT7U,KAAuBA,aAAgBqI,MAAO,CAC1D,MAAMwM,EAAU,GAChB,IAAK,MAAMhV,KAAOG,EACVR,OAAOW,UAAUiF,eAAe/E,KAAKL,EAAMH,KAC3CgV,EAAQhV,GAAO4U,GAAmBzU,EAAKH,GAAMyU,IAGrD,OAAOO,EAEX,OAAO7U,EAUJ,SAAS8U,GAAkBvN,EAAQ+M,GAGtC,OAFA/M,EAAOvH,KAAO+U,GAAmBxN,EAAOvH,KAAMsU,GAC9C/M,EAAOmN,iBAAcnL,EACdhC,EAEX,SAASwN,GAAmB/U,EAAMsU,GAC9B,IAAKtU,EACD,OAAOA,EACX,GAAIA,GAAQA,EAAK4U,aACb,OAAON,EAAQtU,EAAK+H,KAEnB,GAAIxD,MAAM6P,QAAQpU,GACnB,IAAK,IAAIyB,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BzB,EAAKyB,GAAKsT,GAAmB/U,EAAKyB,GAAI6S,QAGzC,GAAoB,iBAATtU,EACZ,IAAK,MAAMH,KAAOG,EACVR,OAAOW,UAAUiF,eAAe/E,KAAKL,EAAMH,KAC3CG,EAAKH,GAAOkV,GAAmB/U,EAAKH,GAAMyU,IAItD,OAAOtU,ECjEC,MAAC2N,GAAW,EACjB,IAAIqH,IACX,SAAWA,GACPA,EAAWA,EAAoB,QAAI,GAAK,UACxCA,EAAWA,EAAuB,WAAI,GAAK,aAC3CA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAgB,IAAI,GAAK,MACpCA,EAAWA,EAA0B,cAAI,GAAK,gBAC9CA,EAAWA,EAAyB,aAAI,GAAK,eAC7CA,EAAWA,EAAuB,WAAI,GAAK,aAP/C,CAQGA,KAAeA,GAAa,KA2ExB,MAAMC,WAAgB/R,EAMzBgD,YAAYgP,GACR5O,QACA9C,KAAK0R,QAAUA,EAOnBC,IAAIvU,GACA,IAAI2G,EACJ,GAAmB,iBAAR3G,EACP2G,EAAS/D,KAAK4R,aAAaxU,GACvB2G,EAAOxH,OAASiV,GAAWK,cAC3B9N,EAAOxH,OAASiV,GAAWM,YAE3B9R,KAAK+R,cAAgB,IAAIC,GAAoBjO,GAElB,IAAvBA,EAAOmN,aACPpO,MAAM7B,aAAa,UAAW8C,IAKlCjB,MAAM7B,aAAa,UAAW8C,OAGjC,CAAA,IAAI0M,EAASrT,KAAQA,EAAIwB,OAe1B,MAAM,IAAI6D,MAAM,iBAAmBrF,GAbnC,IAAK4C,KAAK+R,cACN,MAAM,IAAItP,MAAM,oDAGhBsB,EAAS/D,KAAK+R,cAAcE,eAAe7U,GACvC2G,IAEA/D,KAAK+R,cAAgB,KACrBjP,MAAM7B,aAAa,UAAW8C,KAc9C6N,aAAa9M,GACT,IAAI7G,EAAI,EAER,MAAMkB,EAAI,CACN5C,KAAM+M,OAAOxE,EAAItG,OAAO,KAE5B,QAA2BuH,IAAvByL,GAAWrS,EAAE5C,MACb,MAAM,IAAIkG,MAAM,uBAAyBtD,EAAE5C,MAG/C,GAAI4C,EAAE5C,OAASiV,GAAWK,cACtB1S,EAAE5C,OAASiV,GAAWM,WAAY,CAClC,MAAMI,EAAQjU,EAAI,EAClB,KAA2B,MAApB6G,EAAItG,SAASP,IAAcA,GAAK6G,EAAI5G,SAC3C,MAAMiU,EAAMrN,EAAIpG,UAAUwT,EAAOjU,GACjC,GAAIkU,GAAO7I,OAAO6I,IAA0B,MAAlBrN,EAAItG,OAAOP,GACjC,MAAM,IAAIwE,MAAM,uBAEpBtD,EAAE+R,YAAc5H,OAAO6I,GAG3B,GAAI,MAAQrN,EAAItG,OAAOP,EAAI,GAAI,CAC3B,MAAMiU,EAAQjU,EAAI,EAClB,OAASA,GAAG,CAER,GAAI,MADM6G,EAAItG,OAAOP,GAEjB,MACJ,GAAIA,IAAM6G,EAAI5G,OACV,MAERiB,EAAEiT,IAAMtN,EAAIpG,UAAUwT,EAAOjU,QAG7BkB,EAAEiT,IAAM,IAGZ,MAAMC,EAAOvN,EAAItG,OAAOP,EAAI,GAC5B,GAAI,KAAOoU,GAAQ/I,OAAO+I,IAASA,EAAM,CACrC,MAAMH,EAAQjU,EAAI,EAClB,OAASA,GAAG,CACR,MAAM2R,EAAI9K,EAAItG,OAAOP,GACrB,GAAI,MAAQ2R,GAAKtG,OAAOsG,IAAMA,EAAG,GAC3B3R,EACF,MAEJ,GAAIA,IAAM6G,EAAI5G,OACV,MAERiB,EAAEwO,GAAKrE,OAAOxE,EAAIpG,UAAUwT,EAAOjU,EAAI,IAG3C,GAAI6G,EAAItG,SAASP,GAAI,CACjB,MAAMqU,EAAUtS,KAAKuS,SAASzN,EAAIuH,OAAOpO,IACzC,IAAIwT,GAAQe,eAAerT,EAAE5C,KAAM+V,GAI/B,MAAM,IAAI7P,MAAM,mBAHhBtD,EAAE3C,KAAO8V,EAMjB,OAAOnT,EAEXoT,SAASzN,GACL,IACI,OAAOsK,KAAK5D,MAAM1G,EAAK9E,KAAK0R,SAEhC,MAAOrM,GACH,OAAO,GAGfoN,sBAAsBlW,EAAM+V,GACxB,OAAQ/V,GACJ,KAAKiV,GAAWkB,QACZ,MAA0B,iBAAZJ,EAClB,KAAKd,GAAWmB,WACZ,YAAmB5M,IAAZuM,EACX,KAAKd,GAAWoB,cACZ,MAA0B,iBAAZN,GAA2C,iBAAZA,EACjD,KAAKd,GAAWqB,MAChB,KAAKrB,GAAWK,aACZ,OAAO9Q,MAAM6P,QAAQ0B,IAAYA,EAAQpU,OAAS,EACtD,KAAKsT,GAAWsB,IAChB,KAAKtB,GAAWM,WACZ,OAAO/Q,MAAM6P,QAAQ0B,IAMjCS,UACQ/S,KAAK+R,eACL/R,KAAK+R,cAAciB,0BAY/B,MAAMhB,GACFtP,YAAYqB,GACR/D,KAAK+D,OAASA,EACd/D,KAAK8Q,QAAU,GACf9Q,KAAKiT,UAAYlP,EAUrBkO,eAAeiB,GAEX,GADAlT,KAAK8Q,QAAQ5Q,KAAKgT,GACdlT,KAAK8Q,QAAQ5S,SAAW8B,KAAKiT,UAAU/B,YAAa,CAEpD,MAAMnN,EAASuN,GAAkBtR,KAAKiT,UAAWjT,KAAK8Q,SAEtD,OADA9Q,KAAKgT,yBACEjP,EAEX,OAAO,KAKXiP,yBACIhT,KAAKiT,UAAY,KACjBjT,KAAK8Q,QAAU,kDAlRC,sCAcjB,MAMHpO,YAAYyQ,GACRnT,KAAKmT,SAAWA,EAQpB7O,OAAOlH,GACH,OAAIA,EAAIb,OAASiV,GAAWqB,OAASzV,EAAIb,OAASiV,GAAWsB,MACrDpC,EAAUtT,GAQX,CAAC4C,KAAKoT,eAAehW,KAPpBA,EAAIb,KACAa,EAAIb,OAASiV,GAAWqB,MAClBrB,GAAWK,aACXL,GAAWM,WACd9R,KAAKqT,eAAejW,IAQvCgW,eAAehW,GAEX,IAAI0H,EAAM,GAAK1H,EAAIb,KAmBnB,OAjBIa,EAAIb,OAASiV,GAAWK,cACxBzU,EAAIb,OAASiV,GAAWM,aACxBhN,GAAO1H,EAAI8T,YAAc,KAIzB9T,EAAIgV,KAAO,MAAQhV,EAAIgV,MACvBtN,GAAO1H,EAAIgV,IAAM,KAGjB,MAAQhV,EAAIuQ,KACZ7I,GAAO1H,EAAIuQ,IAGX,MAAQvQ,EAAIZ,OACZsI,GAAOsK,KAAKkE,UAAUlW,EAAIZ,KAAMwD,KAAKmT,WAElCrO,EAOXuO,eAAejW,GACX,MAAMmW,EAAiB1C,EAAkBzT,GACnC4T,EAAOhR,KAAKoT,eAAeG,EAAexP,QAC1C+M,EAAUyC,EAAezC,QAE/B,OADAA,EAAQ0C,QAAQxC,GACTF,iBCrFR,SAASlR,GAAGxC,EAAK4L,EAAIjJ,GAExB,OADA3C,EAAIwC,GAAGoJ,EAAIjJ,GACJ,WACH3C,EAAIgD,IAAI4I,EAAIjJ,ICIpB,MAAM0T,GAAkBzX,OAAO0X,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACbxT,eAAgB,IAEb,MAAMmM,WAAehN,EAMxBgD,YAAYsR,EAAI5B,EAAKlQ,GACjBY,QACA9C,KAAKiU,WAAY,EACjBjU,KAAKkU,cAAgB,GACrBlU,KAAKmU,WAAa,GAClBnU,KAAKoU,IAAM,EACXpU,KAAKqU,KAAO,GACZrU,KAAKsU,MAAQ,GACbtU,KAAKgU,GAAKA,EACVhU,KAAKoS,IAAMA,EACPlQ,GAAQA,EAAKqS,OACbvU,KAAKuU,KAAOrS,EAAKqS,MAEjBvU,KAAKgU,GAAGQ,cACRxU,KAAKqD,OAKToR,mBACA,OAAQzU,KAAKiU,UAOjBS,YACI,GAAI1U,KAAK2U,KACL,OACJ,MAAMX,EAAKhU,KAAKgU,GAChBhU,KAAK2U,KAAO,CACR/U,GAAGoU,EAAI,OAAQhU,KAAKyI,OAAOpG,KAAKrC,OAChCJ,GAAGoU,EAAI,SAAUhU,KAAK4U,SAASvS,KAAKrC,OACpCJ,GAAGoU,EAAI,QAAShU,KAAKiJ,QAAQ5G,KAAKrC,OAClCJ,GAAGoU,EAAI,QAAShU,KAAK6I,QAAQxG,KAAKrC,QAMtC6U,aACA,QAAS7U,KAAK2U,KAOlBhB,UACI,OAAI3T,KAAKiU,YAETjU,KAAK0U,YACA1U,KAAKgU,GAAkB,eACxBhU,KAAKgU,GAAG3Q,OACR,SAAWrD,KAAKgU,GAAGc,aACnB9U,KAAKyI,UALEzI,KAWfqD,OACI,OAAOrD,KAAK2T,UAQhBjQ,QAAQ5C,GAGJ,OAFAA,EAAK0S,QAAQ,WACbxT,KAAKa,KAAKR,MAAML,KAAMc,GACfd,KASXa,KAAKmI,KAAOlI,GACR,GAAI2S,GAAgB7R,eAAeoH,GAC/B,MAAM,IAAIvG,MAAM,IAAMuG,EAAK,8BAE/BlI,EAAK0S,QAAQxK,GACb,MAAMjF,EAAS,CACXxH,KAAMiV,GAAWqB,MACjBrW,KAAMsE,EAEViD,QAAiB,IAGjB,GAFAA,EAAOkM,QAAQC,UAAmC,IAAxBlQ,KAAKsU,MAAMpE,SAEjC,mBAAsBpP,EAAKA,EAAK5C,OAAS,GAAI,CAC7C,MAAMyP,EAAK3N,KAAKoU,MACVW,EAAMjU,EAAKkU,MACjBhV,KAAKiV,qBAAqBtH,EAAIoH,GAC9BhR,EAAO4J,GAAKA,EAEhB,MAAMuH,EAAsBlV,KAAKgU,GAAGmB,QAChCnV,KAAKgU,GAAGmB,OAAOnH,WACfhO,KAAKgU,GAAGmB,OAAOnH,UAAUhL,SAY7B,OAXsBhD,KAAKsU,MAAMc,YAAcF,IAAwBlV,KAAKiU,aAGnEjU,KAAKiU,WACVjU,KAAKqV,wBAAwBtR,GAC7B/D,KAAK+D,OAAOA,IAGZ/D,KAAKmU,WAAWjU,KAAK6D,IAEzB/D,KAAKsU,MAAQ,GACNtU,KAKXiV,qBAAqBtH,EAAIoH,GACrB,MAAMtO,EAAUzG,KAAKsU,MAAM7N,QAC3B,QAAgBV,IAAZU,EAEA,YADAzG,KAAKqU,KAAK1G,GAAMoH,GAIpB,MAAMO,EAAQtV,KAAKgU,GAAG5R,cAAa,YACxBpC,KAAKqU,KAAK1G,GACjB,IAAK,IAAI1P,EAAI,EAAGA,EAAI+B,KAAKmU,WAAWjW,OAAQD,IACpC+B,KAAKmU,WAAWlW,GAAG0P,KAAOA,GAC1B3N,KAAKmU,WAAWvT,OAAO3C,EAAG,GAGlC8W,EAAIlY,KAAKmD,KAAM,IAAIyC,MAAM,8BAC1BgE,GACHzG,KAAKqU,KAAK1G,GAAM,IAAI7M,KAEhBd,KAAKgU,GAAGzR,eAAe+S,GACvBP,EAAI1U,MAAML,KAAM,CAAC,QAASc,KASlCiD,OAAOA,GACHA,EAAOqO,IAAMpS,KAAKoS,IAClBpS,KAAKgU,GAAGuB,QAAQxR,GAOpB0E,SAC4B,mBAAbzI,KAAKuU,KACZvU,KAAKuU,MAAM/X,IACPwD,KAAK+D,OAAO,CAAExH,KAAMiV,GAAWkB,QAASlW,KAAAA,OAI5CwD,KAAK+D,OAAO,CAAExH,KAAMiV,GAAWkB,QAASlW,KAAMwD,KAAKuU,OAS3DtL,QAAQ/D,GACClF,KAAKiU,WACNjU,KAAKiB,aAAa,gBAAiBiE,GAU3C2D,QAAQlG,EAAQC,GACZ5C,KAAKiU,WAAY,SACVjU,KAAK2N,GACZ3N,KAAKiB,aAAa,aAAc0B,EAAQC,GAQ5CgS,SAAS7Q,GAEL,GADsBA,EAAOqO,MAAQpS,KAAKoS,IAG1C,OAAQrO,EAAOxH,MACX,KAAKiV,GAAWkB,QACZ,GAAI3O,EAAOvH,MAAQuH,EAAOvH,KAAKwO,IAAK,CAChC,MAAM2C,EAAK5J,EAAOvH,KAAKwO,IACvBhL,KAAKwV,UAAU7H,QAGf3N,KAAKiB,aAAa,gBAAiB,IAAIwB,MAAM,8LAEjD,MACJ,KAAK+O,GAAWqB,MAChB,KAAKrB,GAAWK,aACZ7R,KAAKyV,QAAQ1R,GACb,MACJ,KAAKyN,GAAWsB,IAChB,KAAKtB,GAAWM,WACZ9R,KAAK0V,MAAM3R,GACX,MACJ,KAAKyN,GAAWmB,WACZ3S,KAAK2V,eACL,MACJ,KAAKnE,GAAWoB,cACZ5S,KAAK+S,UACL,MAAM7N,EAAM,IAAIzC,MAAMsB,EAAOvH,KAAKoZ,SAElC1Q,EAAI1I,KAAOuH,EAAOvH,KAAKA,KACvBwD,KAAKiB,aAAa,gBAAiBiE,IAU/CuQ,QAAQ1R,GACJ,MAAMjD,EAAOiD,EAAOvH,MAAQ,GACxB,MAAQuH,EAAO4J,IACf7M,EAAKZ,KAAKF,KAAK+U,IAAIhR,EAAO4J,KAE1B3N,KAAKiU,UACLjU,KAAK6V,UAAU/U,GAGfd,KAAKkU,cAAchU,KAAKlE,OAAO0X,OAAO5S,IAG9C+U,UAAU/U,GACN,GAAId,KAAK8V,eAAiB9V,KAAK8V,cAAc5X,OAAQ,CACjD,MAAMgD,EAAYlB,KAAK8V,cAAc9U,QACrC,IAAK,MAAM+U,KAAY7U,EACnB6U,EAAS1V,MAAML,KAAMc,GAG7BgC,MAAMjC,KAAKR,MAAML,KAAMc,GAO3BiU,IAAIpH,GACA,MAAMvM,EAAOpB,KACb,IAAIgW,GAAO,EACX,OAAO,YAAalV,GAEZkV,IAEJA,GAAO,EACP5U,EAAK2C,OAAO,CACRxH,KAAMiV,GAAWsB,IACjBnF,GAAIA,EACJnR,KAAMsE,MAUlB4U,MAAM3R,GACF,MAAMgR,EAAM/U,KAAKqU,KAAKtQ,EAAO4J,IACzB,mBAAsBoH,IACtBA,EAAI1U,MAAML,KAAM+D,EAAOvH,aAChBwD,KAAKqU,KAAKtQ,EAAO4J,KAUhC6H,UAAU7H,GACN3N,KAAK2N,GAAKA,EACV3N,KAAKiU,WAAY,EACjBjU,KAAKiW,eACLjW,KAAKiB,aAAa,WAOtBgV,eACIjW,KAAKkU,cAAc9X,SAAS0E,GAASd,KAAK6V,UAAU/U,KACpDd,KAAKkU,cAAgB,GACrBlU,KAAKmU,WAAW/X,SAAS2H,IACrB/D,KAAKqV,wBAAwBtR,GAC7B/D,KAAK+D,OAAOA,MAEhB/D,KAAKmU,WAAa,GAOtBwB,eACI3V,KAAK+S,UACL/S,KAAK6I,QAAQ,wBASjBkK,UACQ/S,KAAK2U,OAEL3U,KAAK2U,KAAKvY,SAAS8Z,GAAeA,MAClClW,KAAK2U,UAAO5O,GAEhB/F,KAAKgU,GAAa,SAAEhU,MAQxB6T,aAUI,OATI7T,KAAKiU,WACLjU,KAAK+D,OAAO,CAAExH,KAAMiV,GAAWmB,aAGnC3S,KAAK+S,UACD/S,KAAKiU,WAELjU,KAAK6I,QAAQ,wBAEV7I,KAQXuD,QACI,OAAOvD,KAAK6T,aAShB3D,SAASA,GAEL,OADAlQ,KAAKsU,MAAMpE,SAAWA,EACflQ,KASPoV,eAEA,OADApV,KAAKsU,MAAMc,UAAW,EACfpV,KAiBXyG,QAAQA,GAEJ,OADAzG,KAAKsU,MAAM7N,QAAUA,EACdzG,KASXmW,MAAMJ,GAGF,OAFA/V,KAAK8V,cAAgB9V,KAAK8V,eAAiB,GAC3C9V,KAAK8V,cAAc5V,KAAK6V,GACjB/V,KASXoW,WAAWL,GAGP,OAFA/V,KAAK8V,cAAgB9V,KAAK8V,eAAiB,GAC3C9V,KAAK8V,cAActC,QAAQuC,GACpB/V,KAQXqW,OAAON,GACH,IAAK/V,KAAK8V,cACN,OAAO9V,KAEX,GAAI+V,EAAU,CACV,MAAM7U,EAAYlB,KAAK8V,cACvB,IAAK,IAAI7X,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,IAClC,GAAI8X,IAAa7U,EAAUjD,GAEvB,OADAiD,EAAUN,OAAO3C,EAAG,GACb+B,UAKfA,KAAK8V,cAAgB,GAEzB,OAAO9V,KAQXsW,eACI,OAAOtW,KAAK8V,eAAiB,GAkBjCS,cAAcR,GAGV,OAFA/V,KAAKwW,sBAAwBxW,KAAKwW,uBAAyB,GAC3DxW,KAAKwW,sBAAsBtW,KAAK6V,GACzB/V,KAkBXyW,mBAAmBV,GAGf,OAFA/V,KAAKwW,sBAAwBxW,KAAKwW,uBAAyB,GAC3DxW,KAAKwW,sBAAsBhD,QAAQuC,GAC5B/V,KAsBX0W,eAAeX,GACX,IAAK/V,KAAKwW,sBACN,OAAOxW,KAEX,GAAI+V,EAAU,CACV,MAAM7U,EAAYlB,KAAKwW,sBACvB,IAAK,IAAIvY,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,IAClC,GAAI8X,IAAa7U,EAAUjD,GAEvB,OADAiD,EAAUN,OAAO3C,EAAG,GACb+B,UAKfA,KAAKwW,sBAAwB,GAEjC,OAAOxW,KAQX2W,uBACI,OAAO3W,KAAKwW,uBAAyB,GASzCnB,wBAAwBtR,GACpB,GAAI/D,KAAKwW,uBAAyBxW,KAAKwW,sBAAsBtY,OAAQ,CACjE,MAAMgD,EAAYlB,KAAKwW,sBAAsBxV,QAC7C,IAAK,MAAM+U,KAAY7U,EACnB6U,EAAS1V,MAAML,KAAM+D,EAAOvH,QCjkBrC,SAASoa,GAAQ1U,GACpBA,EAAOA,GAAQ,GACflC,KAAK6W,GAAK3U,EAAK4U,KAAO,IACtB9W,KAAK+W,IAAM7U,EAAK6U,KAAO,IACvB/W,KAAKgX,OAAS9U,EAAK8U,QAAU,EAC7BhX,KAAKiX,OAAS/U,EAAK+U,OAAS,GAAK/U,EAAK+U,QAAU,EAAI/U,EAAK+U,OAAS,EAClEjX,KAAKkX,SAAW,EAQpBN,GAAQja,UAAUwa,SAAW,WACzB,IAAIN,EAAK7W,KAAK6W,GAAKpS,KAAK2S,IAAIpX,KAAKgX,OAAQhX,KAAKkX,YAC9C,GAAIlX,KAAKiX,OAAQ,CACb,IAAII,EAAO5S,KAAK6S,SACZC,EAAY9S,KAAKC,MAAM2S,EAAOrX,KAAKiX,OAASJ,GAChDA,EAAoC,IAAN,EAAxBpS,KAAKC,MAAa,GAAP2S,IAAuBR,EAAKU,EAAYV,EAAKU,EAElE,OAAgC,EAAzB9S,KAAKqS,IAAID,EAAI7W,KAAK+W,MAO7BH,GAAQja,UAAU6a,MAAQ,WACtBxX,KAAKkX,SAAW,GAOpBN,GAAQja,UAAU8a,OAAS,SAAUX,GACjC9W,KAAK6W,GAAKC,GAOdF,GAAQja,UAAU+a,OAAS,SAAUX,GACjC/W,KAAK+W,IAAMA,GAOfH,GAAQja,UAAUgb,UAAY,SAAUV,GACpCjX,KAAKiX,OAASA,GC1DX,MAAMW,WAAgBlY,EACzBgD,YAAYkD,EAAK1D,GACb,IAAI2V,EACJ/U,QACA9C,KAAK8X,KAAO,GACZ9X,KAAK2U,KAAO,GACR/O,GAAO,iBAAoBA,IAC3B1D,EAAO0D,EACPA,OAAMG,IAEV7D,EAAOA,GAAQ,IACV2H,KAAO3H,EAAK2H,MAAQ,aACzB7J,KAAKkC,KAAOA,EACZD,EAAsBjC,KAAMkC,GAC5BlC,KAAK+X,cAAmC,IAAtB7V,EAAK6V,cACvB/X,KAAKgY,qBAAqB9V,EAAK8V,sBAAwBC,EAAAA,GACvDjY,KAAKkY,kBAAkBhW,EAAKgW,mBAAqB,KACjDlY,KAAKmY,qBAAqBjW,EAAKiW,sBAAwB,KACvDnY,KAAKoY,oBAAwD,QAAnCP,EAAK3V,EAAKkW,2BAAwC,IAAPP,EAAgBA,EAAK,IAC1F7X,KAAKqY,QAAU,IAAIzB,GAAQ,CACvBE,IAAK9W,KAAKkY,oBACVnB,IAAK/W,KAAKmY,uBACVlB,OAAQjX,KAAKoY,wBAEjBpY,KAAKyG,QAAQ,MAAQvE,EAAKuE,QAAU,IAAQvE,EAAKuE,SACjDzG,KAAK8U,YAAc,SACnB9U,KAAK4F,IAAMA,EACX,MAAM0S,EAAUpW,EAAKqW,QAAUA,GAC/BvY,KAAKwY,QAAU,IAAIF,EAAQG,QAC3BzY,KAAK0Y,QAAU,IAAIJ,EAAQ7G,QAC3BzR,KAAKwU,cAAoC,IAArBtS,EAAKyW,YACrB3Y,KAAKwU,cACLxU,KAAKqD,OAEb0U,aAAaa,GACT,OAAKtY,UAAUpC,QAEf8B,KAAK6Y,gBAAkBD,EAChB5Y,MAFIA,KAAK6Y,cAIpBb,qBAAqBY,GACjB,YAAU7S,IAAN6S,EACO5Y,KAAK8Y,uBAChB9Y,KAAK8Y,sBAAwBF,EACtB5Y,MAEXkY,kBAAkBU,GACd,IAAIf,EACJ,YAAU9R,IAAN6S,EACO5Y,KAAK+Y,oBAChB/Y,KAAK+Y,mBAAqBH,EACF,QAAvBf,EAAK7X,KAAKqY,eAA4B,IAAPR,GAAyBA,EAAGJ,OAAOmB,GAC5D5Y,MAEXoY,oBAAoBQ,GAChB,IAAIf,EACJ,YAAU9R,IAAN6S,EACO5Y,KAAKgZ,sBAChBhZ,KAAKgZ,qBAAuBJ,EACJ,QAAvBf,EAAK7X,KAAKqY,eAA4B,IAAPR,GAAyBA,EAAGF,UAAUiB,GAC/D5Y,MAEXmY,qBAAqBS,GACjB,IAAIf,EACJ,YAAU9R,IAAN6S,EACO5Y,KAAKiZ,uBAChBjZ,KAAKiZ,sBAAwBL,EACL,QAAvBf,EAAK7X,KAAKqY,eAA4B,IAAPR,GAAyBA,EAAGH,OAAOkB,GAC5D5Y,MAEXyG,QAAQmS,GACJ,OAAKtY,UAAUpC,QAEf8B,KAAKkZ,SAAWN,EACT5Y,MAFIA,KAAKkZ,SAUpBC,wBAESnZ,KAAKoZ,eACNpZ,KAAK6Y,eACqB,IAA1B7Y,KAAKqY,QAAQnB,UAEblX,KAAKqZ,YAUbhW,KAAKtD,GACD,IAAKC,KAAK8U,YAAYlL,QAAQ,QAC1B,OAAO5J,KACXA,KAAKmV,OAAS,IAAImE,EAAOtZ,KAAK4F,IAAK5F,KAAKkC,MACxC,MAAMiB,EAASnD,KAAKmV,OACd/T,EAAOpB,KACbA,KAAK8U,YAAc,UACnB9U,KAAKuZ,eAAgB,EAErB,MAAMC,EAAiB5Z,GAAGuD,EAAQ,QAAQ,WACtC/B,EAAKqH,SACL1I,GAAMA,OAGJ0Z,EAAW7Z,GAAGuD,EAAQ,SAAU+B,IAClC9D,EAAK6F,UACL7F,EAAK0T,YAAc,SACnB9U,KAAKiB,aAAa,QAASiE,GACvBnF,EACAA,EAAGmF,GAIH9D,EAAK+X,0BAGb,IAAI,IAAUnZ,KAAKkZ,SAAU,CACzB,MAAMzS,EAAUzG,KAAKkZ,SACL,IAAZzS,GACA+S,IAGJ,MAAMlE,EAAQtV,KAAKoC,cAAa,KAC5BoX,IACArW,EAAOI,QAEPJ,EAAOtC,KAAK,QAAS,IAAI4B,MAAM,cAChCgE,GACCzG,KAAKkC,KAAKwG,WACV4M,EAAM1M,QAEV5I,KAAK2U,KAAKzU,MAAK,WACX8B,aAAasT,MAKrB,OAFAtV,KAAK2U,KAAKzU,KAAKsZ,GACfxZ,KAAK2U,KAAKzU,KAAKuZ,GACRzZ,KAQX2T,QAAQ5T,GACJ,OAAOC,KAAKqD,KAAKtD,GAOrB0I,SAEIzI,KAAKiH,UAELjH,KAAK8U,YAAc,OACnB9U,KAAKiB,aAAa,QAElB,MAAMkC,EAASnD,KAAKmV,OACpBnV,KAAK2U,KAAKzU,KAAKN,GAAGuD,EAAQ,OAAQnD,KAAK0Z,OAAOrX,KAAKrC,OAAQJ,GAAGuD,EAAQ,OAAQnD,KAAK2Z,OAAOtX,KAAKrC,OAAQJ,GAAGuD,EAAQ,QAASnD,KAAKiJ,QAAQ5G,KAAKrC,OAAQJ,GAAGuD,EAAQ,QAASnD,KAAK6I,QAAQxG,KAAKrC,OAAQJ,GAAGI,KAAK0Y,QAAS,UAAW1Y,KAAK4Z,UAAUvX,KAAKrC,QAOvP0Z,SACI1Z,KAAKiB,aAAa,QAOtB0Y,OAAOnd,GACHwD,KAAK0Y,QAAQ/G,IAAInV,GAOrBod,UAAU7V,GACN/D,KAAKiB,aAAa,SAAU8C,GAOhCkF,QAAQ/D,GACJlF,KAAKiB,aAAa,QAASiE,GAQ/B/B,OAAOiP,EAAKlQ,GACR,IAAIiB,EAASnD,KAAK8X,KAAK1F,GAKvB,OAJKjP,IACDA,EAAS,IAAIuJ,GAAO1M,KAAMoS,EAAKlQ,GAC/BlC,KAAK8X,KAAK1F,GAAOjP,GAEdA,EAQX0W,SAAS1W,GACL,MAAM2U,EAAO9b,OAAOG,KAAK6D,KAAK8X,MAC9B,IAAK,MAAM1F,KAAO0F,EAAM,CAEpB,GADe9X,KAAK8X,KAAK1F,GACdyC,OACP,OAGR7U,KAAK8Z,SAQTvE,QAAQxR,GACJ,MAAM2G,EAAiB1K,KAAKwY,QAAQlU,OAAOP,GAC3C,IAAK,IAAI9F,EAAI,EAAGA,EAAIyM,EAAexM,OAAQD,IACvC+B,KAAKmV,OAAOvR,MAAM8G,EAAezM,GAAI8F,EAAOkM,SAQpDhJ,UACIjH,KAAK2U,KAAKvY,SAAS8Z,GAAeA,MAClClW,KAAK2U,KAAKzW,OAAS,EACnB8B,KAAK0Y,QAAQ3F,UAOjB+G,SACI9Z,KAAKuZ,eAAgB,EACrBvZ,KAAKoZ,eAAgB,EACrBpZ,KAAK6I,QAAQ,gBACT7I,KAAKmV,QACLnV,KAAKmV,OAAO5R,QAOpBsQ,aACI,OAAO7T,KAAK8Z,SAOhBjR,QAAQlG,EAAQC,GACZ5C,KAAKiH,UACLjH,KAAKqY,QAAQb,QACbxX,KAAK8U,YAAc,SACnB9U,KAAKiB,aAAa,QAAS0B,EAAQC,GAC/B5C,KAAK6Y,gBAAkB7Y,KAAKuZ,eAC5BvZ,KAAKqZ,YAQbA,YACI,GAAIrZ,KAAKoZ,eAAiBpZ,KAAKuZ,cAC3B,OAAOvZ,KACX,MAAMoB,EAAOpB,KACb,GAAIA,KAAKqY,QAAQnB,UAAYlX,KAAK8Y,sBAC9B9Y,KAAKqY,QAAQb,QACbxX,KAAKiB,aAAa,oBAClBjB,KAAKoZ,eAAgB,MAEpB,CACD,MAAMW,EAAQ/Z,KAAKqY,QAAQlB,WAC3BnX,KAAKoZ,eAAgB,EACrB,MAAM9D,EAAQtV,KAAKoC,cAAa,KACxBhB,EAAKmY,gBAETvZ,KAAKiB,aAAa,oBAAqBG,EAAKiX,QAAQnB,UAEhD9V,EAAKmY,eAETnY,EAAKiC,MAAM6B,IACHA,GACA9D,EAAKgY,eAAgB,EACrBhY,EAAKiY,YACLrZ,KAAKiB,aAAa,kBAAmBiE,IAGrC9D,EAAK4Y,oBAGdD,GACC/Z,KAAKkC,KAAKwG,WACV4M,EAAM1M,QAEV5I,KAAK2U,KAAKzU,MAAK,WACX8B,aAAasT,OASzB0E,cACI,MAAMC,EAAUja,KAAKqY,QAAQnB,SAC7BlX,KAAKoZ,eAAgB,EACrBpZ,KAAKqY,QAAQb,QACbxX,KAAKiB,aAAa,YAAagZ,ICrVvC,MAAMC,GAAQ,GACd,SAASnc,GAAO6H,EAAK1D,GACE,iBAAR0D,IACP1D,EAAO0D,EACPA,OAAMG,GAGV,MAAMoU,ECHH,SAAavU,EAAKiE,EAAO,GAAIuQ,GAChC,IAAIhd,EAAMwI,EAEVwU,EAAMA,GAA4B,oBAAbnQ,UAA4BA,SAC7C,MAAQrE,IACRA,EAAMwU,EAAIjQ,SAAW,KAAOiQ,EAAIrO,MAEjB,iBAARnG,IACH,MAAQA,EAAIpH,OAAO,KAEfoH,EADA,MAAQA,EAAIpH,OAAO,GACb4b,EAAIjQ,SAAWvE,EAGfwU,EAAIrO,KAAOnG,GAGpB,sBAAsByU,KAAKzU,KAExBA,OADA,IAAuBwU,EACjBA,EAAIjQ,SAAW,KAAOvE,EAGtB,WAAaA,GAI3BxI,EAAMoO,EAAM5F,IAGXxI,EAAIiM,OACD,cAAcgR,KAAKjd,EAAI+M,UACvB/M,EAAIiM,KAAO,KAEN,eAAegR,KAAKjd,EAAI+M,YAC7B/M,EAAIiM,KAAO,QAGnBjM,EAAIyM,KAAOzM,EAAIyM,MAAQ,IACvB,MACMkC,GADkC,IAA3B3O,EAAI2O,KAAKnC,QAAQ,KACV,IAAMxM,EAAI2O,KAAO,IAAM3O,EAAI2O,KAS/C,OAPA3O,EAAIuQ,GAAKvQ,EAAI+M,SAAW,MAAQ4B,EAAO,IAAM3O,EAAIiM,KAAOQ,EAExDzM,EAAIkd,KACAld,EAAI+M,SACA,MACA4B,GACCqO,GAAOA,EAAI/Q,OAASjM,EAAIiM,KAAO,GAAK,IAAMjM,EAAIiM,MAChDjM,ED5CQmd,CAAI3U,GADnB1D,EAAOA,GAAQ,IACc2H,MAAQ,cAC/BiC,EAASqO,EAAOrO,OAChB6B,EAAKwM,EAAOxM,GACZ9D,EAAOsQ,EAAOtQ,KACd2Q,EAAgBN,GAAMvM,IAAO9D,KAAQqQ,GAAMvM,GAAU,KAK3D,IAAIqG,EAaJ,OAjBsB9R,EAAKuY,UACvBvY,EAAK,0BACL,IAAUA,EAAKwY,WACfF,EAGAxG,EAAK,IAAI4D,GAAQ9L,EAAQ5J,IAGpBgY,GAAMvM,KACPuM,GAAMvM,GAAM,IAAIiK,GAAQ9L,EAAQ5J,IAEpC8R,EAAKkG,GAAMvM,IAEXwM,EAAOlX,QAAUf,EAAKe,QACtBf,EAAKe,MAAQkX,EAAO7N,UAEjB0H,EAAG7Q,OAAOgX,EAAOtQ,KAAM3H,GAIlClG,OAAOkP,OAAOnN,GAAQ,CAClB6Z,QAAAA,GACAlL,OAAAA,GACAsH,GAAIjW,GACJ4V,QAAS5V"}