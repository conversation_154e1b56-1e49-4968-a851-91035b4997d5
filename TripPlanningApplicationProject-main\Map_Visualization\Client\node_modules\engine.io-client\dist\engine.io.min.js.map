{"version": 3, "file": "engine.io.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/contrib/yeast.js", "../build/esm/transport.js", "../build/esm/contrib/parseqs.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/browser-entrypoint.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} options.\n     * @api private\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.readyState = \"\";\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @api protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     *\n     * @api public\n     */\n    open() {\n        if (\"closed\" === this.readyState || \"\" === this.readyState) {\n            this.readyState = \"opening\";\n            this.doOpen();\n        }\n        return this;\n    }\n    /**\n     * Closes the transport.\n     *\n     * @api public\n     */\n    close() {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     * @api public\n     */\n    send(packets) {\n        if (\"open\" === this.readyState) {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @api protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @api protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @api protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @api protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    /**\n     * Transport name.\n     */\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @api private\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} callback upon buffers are flushed and transport is paused\n     * @api private\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @api public\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @api private\n     */\n    onData(data) {\n        const callback = packet => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @api private\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} data packets\n     * @param {Function} drain callback\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, data => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @api private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @api private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @api private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @api public\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @api private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @api private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @api private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @api private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @api public\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return cb => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @api {Object} connection options\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Transport name.\n     *\n     * @api public\n     */\n    get name() {\n        return \"websocket\";\n    }\n    /**\n     * Opens socket.\n     *\n     * @api private\n     */\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @api private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = closeEvent => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent\n        });\n        this.ws.onmessage = ev => this.onData(ev.data);\n        this.ws.onerror = e => this.onError(\"websocket error\", e);\n    }\n    /**\n     * Writes data to socket.\n     *\n     * @param {Array} array of packets.\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, data => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    /**\n     * Closes socket.\n     *\n     * @api private\n     */\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @api public\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri or options\n     * @param {Object} opts - options\n     * @api public\n     */\n    constructor(uri, opts = {}) {\n        super();\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.readyState = \"\";\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                addEventListener(\"beforeunload\", () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                }, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} transport name\n     * @return {Transport}\n     * @api private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @api private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @api private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", reason => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} transport name\n     * @api private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", msg => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = err => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @api private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState &&\n            this.opts.upgrade &&\n            this.transport.pause) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @api private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @api private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @api private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @api private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @api private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} message.\n     * @param {Function} callback function.\n     * @param {Object} options.\n     * @return {Socket} for chaining.\n     * @api public\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @api private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     *\n     * @api public\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @api private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @api private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} server upgrades\n     * @api private\n     *\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport default (uri, opts) => new Socket(uri, opts);\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "obj", "encodeBlobAsBase64", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "this", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "globalThis", "clearTimeoutFn", "prev", "TransportError", "reason", "description", "context", "Error", "Transport", "writable", "query", "readyState", "socket", "doOpen", "doClose", "onClose", "packets", "write", "packet", "onPacket", "details", "alphabet", "map", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Polling", "polling", "location", "isSSL", "protocol", "port", "xd", "hostname", "xs", "secure", "forceBase64", "poll", "onPause", "pause", "_this2", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "_this3", "onOpen", "close", "_this4", "count", "encodePayload", "_this5", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "path", "Request", "uri", "req", "request", "method", "xhrStatus", "_this6", "onError", "onData", "_this7", "pollXhr", "async", "undefined", "xscheme", "xhr", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "_this9", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "substr", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "_this", "writeBuffer", "prevBufferLen", "_extends", "agent", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "transport", "offlineEventListener", "name", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j"], "mappings": ";;;;;29FAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAAAC,GAC9BH,EAAqBH,EAAaM,IAAQA,KCN9C,IDQA,IAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBEXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,WAAiBC,EAAgBC,OALvCC,EAKSZ,IAAAA,KAAMC,IAAAA,YACtBC,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTY,EAAmBZ,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVI,EAegCX,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOF,GACnBA,GAAOA,EAAIG,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTY,EAAmB,IAAIV,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,MAE5CY,EAAqB,SAACZ,EAAMU,OACxBK,EAAa,IAAIC,kBACvBD,EAAWE,OAAS,eACVC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,IDtC9BsB,EAAQ,mEAGRC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC9DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,MEH5BnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,SAACC,EAAeC,MACJ,iBAAlBD,QACA,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,QAGjC/B,EAAO8B,EAAcG,OAAO,SACrB,MAATjC,EACO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,GAWTmC,EAAqB,SAACjC,EAAM8B,MAC1BxB,EAAuB,KACjB6B,EFFQ,SAACC,OAGfX,EAEAY,EACAC,EACAC,EACAC,EAPAC,EAA+B,IAAhBL,EAAOV,OACtBgB,EAAMN,EAAOV,OAEbiB,EAAI,EAM0B,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,SAIFG,EAAc,IAAIrC,YAAYkC,GAChCI,EAAQ,IAAIrB,WAAWoB,OAEtBnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IAExCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,SAGnCI,EE7BaE,CAAO9C,UAChB+B,EAAUI,EAASL,SAGnB,CAAEM,QAAQ,EAAMpC,KAAAA,IAGzB+B,EAAY,SAAC/B,EAAM8B,SAEZ,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,GC3Cb+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQvC,MAClBA,EAAK,OAWX,SAAeA,OACR,IAAId,KAAOqD,EAAQ/C,UACtBQ,EAAId,GAAOqD,EAAQ/C,UAAUN,UAExBc,EAfSwC,CAAMxC,GA2BxBuC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,eAC9CC,WAAaC,KAAKD,YAAc,IACpCC,KAAKD,WAAW,IAAMF,GAASG,KAAKD,WAAW,IAAMF,IAAU,IAC7DI,KAAKH,GACDE,MAaTP,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,YAC9BH,SACFQ,IAAIN,EAAOF,GAChBG,EAAGM,MAAMJ,KAAMK,kBAGjBV,EAAGG,GAAKA,OACHH,GAAGE,EAAOF,GACRK,MAaTP,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,WACjDC,WAAaC,KAAKD,YAAc,GAGjC,GAAKM,UAAUpC,mBACZ8B,WAAa,GACXC,SAcLS,EAVAC,EAAYV,KAAKD,WAAW,IAAMF,OACjCa,EAAW,OAAOV,QAGnB,GAAKK,UAAUpC,qBACV+B,KAAKD,WAAW,IAAMF,GACtBG,SAKJ,IAAIhC,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,QACpCyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,gBAOC,IAArB0C,EAAUzC,eACL+B,KAAKD,WAAW,IAAMF,GAGxBG,MAWTP,EAAQ/C,UAAUkE,KAAO,SAASf,QAC3BE,WAAaC,KAAKD,YAAc,WAEjCc,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYV,KAAKD,WAAW,IAAMF,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,MAGtB0C,EAEG,CAAI1C,EAAI,MAAR,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAMJ,KAAMa,UAItBb,MAITP,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,eAChCE,WAAaC,KAAKD,YAAc,GAC9BC,KAAKD,WAAW,IAAMF,IAAU,IAWzCJ,EAAQ/C,UAAUwE,aAAe,SAASrB,WAC9BG,KAAKiB,UAAUpB,GAAO5B,QCvK3B,IAAMkD,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKrE,8BAAQsE,mCAAAA,2BAClBA,EAAKC,QAAO,SAACC,EAAKC,UACjBzE,EAAI0E,eAAeD,KACnBD,EAAIC,GAAKzE,EAAIyE,IAEVD,IACR,IAGP,IAAMG,EAAqBC,WACrBC,EAAuBC,aACtB,SAASC,EAAsB/E,EAAKgF,GACnCA,EAAKC,iBACLjF,EAAIkF,aAAeP,EAAmBQ,KAAKC,GAC3CpF,EAAIqF,eAAiBR,EAAqBM,KAAKC,KAG/CpF,EAAIkF,aAAeN,WAAWO,KAAKC,GACnCpF,EAAIqF,eAAiBP,aAAaK,KAAKC,QChB1BE,ECAfC,2CACUC,EAAQC,EAAaC,yCACvBF,IACDC,YAAcA,IACdC,QAAUA,IACVtG,KAAO,+BALSuG,QAQhBC,2CAOGZ,2CAEHa,UAAW,EAChBd,OAA4BC,KACvBA,KAAOA,IACPc,MAAQd,EAAKc,QACbC,WAAa,KACbC,OAAShB,EAAKgB,mDAWfR,EAAQC,EAAaC,0DACN,QAAS,IAAIH,EAAeC,EAAQC,EAAaC,IAC7D5C,0CAQH,WAAaA,KAAKiD,YAAc,KAAOjD,KAAKiD,kBACvCA,WAAa,eACbE,UAEFnD,2CAQH,YAAcA,KAAKiD,YAAc,SAAWjD,KAAKiD,kBAC5CG,eACAC,WAEFrD,kCAQNsD,GACG,SAAWtD,KAAKiD,iBACXM,MAAMD,yCAYVL,WAAa,YACbF,UAAW,kDACG,uCAQhBxG,OACGiH,EAASrF,EAAa5B,EAAMyD,KAAKkD,OAAO7E,iBACzCoF,SAASD,oCAOTA,mDACc,SAAUA,mCAOzBE,QACCT,WAAa,yDACC,QAASS,UAtGLjE,GDTzBkE,EAAW,mEAAmEhG,MAAM,IAAkBiG,EAAM,GAC9GC,EAAO,EAAG7F,EAAI,EAQX,SAAS8F,EAAOC,OACfC,EAAU,MAEVA,EAAUL,EAASI,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,UACRC,EAsBJ,SAASG,QACNC,EAAMN,GAAQ,IAAIO,aACpBD,IAAQ5B,GACDqB,EAAO,EAAGrB,EAAO4B,GACrBA,EAAM,IAAMN,EAAOD,KAK9B,KAAO7F,EA9CiG,GA8CrFA,IACf4F,EAAID,EAAS3F,IAAMA,EEzChB,SAAS8F,EAAO5G,OACfoH,EAAM,OACL,IAAItG,KAAKd,EACNA,EAAI0E,eAAe5D,KACfsG,EAAIrG,SACJqG,GAAO,KACXA,GAAOC,mBAAmBvG,GAAK,IAAMuG,mBAAmBrH,EAAIc,YAG7DsG,EAQJ,SAASjF,EAAOmF,WACfC,EAAM,GACNC,EAAQF,EAAG7G,MAAM,KACZK,EAAI,EAAG2G,EAAID,EAAMzG,OAAQD,EAAI2G,EAAG3G,IAAK,KACtC4G,EAAOF,EAAM1G,GAAGL,MAAM,KAC1B8G,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,WAExDH,EC/BX,IAAIK,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,eAEjC,MAAOC,IAIA,IAAMC,EAAUH,ECPhB,SAASI,EAAIhD,OACViD,EAAUjD,EAAKiD,eAGb,oBAAuBJ,kBAAoBI,GAAWF,UAC/C,IAAIF,eAGnB,MAAOK,QACFD,aAEU,IAAI7C,EAAW,CAAC,UAAU+C,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,KCRf,SAASG,KACT,IAAMC,EAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aAEVC,2CAOGxD,qCACFA,IACDyD,SAAU,EACS,oBAAbC,SAA0B,KAC3BC,EAAQ,WAAaD,SAASE,SAChCC,EAAOH,SAASG,KAEfA,IACDA,EAAOF,EAAQ,MAAQ,QAEtBG,GACoB,oBAAbJ,UACJ1D,EAAK+D,WAAaL,SAASK,UAC3BF,IAAS7D,EAAK6D,OACjBG,GAAKhE,EAAKiE,SAAWN,MAKxBO,EAAclE,GAAQA,EAAKkE,qBAC5BpJ,eAAiBwI,IAAYY,oDAe7BC,qCAQHC,mBACGrD,WAAa,cACZsD,EAAQ,WACVC,EAAKvD,WAAa,SAClBqD,QAEAtG,KAAK2F,UAAY3F,KAAK+C,SAAU,KAC5B0D,EAAQ,EACRzG,KAAK2F,UACLc,SACKvG,KAAK,gBAAgB,aACpBuG,GAASF,QAGdvG,KAAK+C,WACN0D,SACKvG,KAAK,SAAS,aACbuG,GAASF,aAKnBA,wCASCZ,SAAU,OACVe,cACA1F,aAAa,uCAOfzE,eTvFW,SAACoK,EAAgBtI,WAC7BuI,EAAiBD,EAAehJ,MAAM2B,GACtCgE,EAAU,GACPtF,EAAI,EAAGA,EAAI4I,EAAe3I,OAAQD,IAAK,KACtC6I,EAAgB1I,EAAayI,EAAe5I,GAAIK,MACtDiF,EAAQrD,KAAK4G,GACc,UAAvBA,EAAcvK,kBAIfgH,GS4FHwD,CAAcvK,EAAMyD,KAAKkD,OAAO7E,YAAYlC,SAd3B,SAAAqH,MAET,YAAcuD,EAAK9D,YAA8B,SAAhBO,EAAOlH,MACxCyK,EAAKC,SAGL,UAAYxD,EAAOlH,YACnByK,EAAK1D,QAAQ,CAAEV,YAAa,oCACrB,EAGXoE,EAAKtD,SAASD,MAKd,WAAaxD,KAAKiD,kBAEb0C,SAAU,OACV3E,aAAa,gBACd,SAAWhB,KAAKiD,iBACXoD,qDAYPY,EAAQ,WACVC,EAAK3D,MAAM,CAAC,CAAEjH,KAAM,YAEpB,SAAW0D,KAAKiD,WAChBgE,SAKK/G,KAAK,OAAQ+G,iCAUpB3D,mBACGP,UAAW,ET5JF,SAACO,EAASrG,OAEtBgB,EAASqF,EAAQrF,OACjB2I,EAAiB,IAAI9F,MAAM7C,GAC7BkJ,EAAQ,EACZ7D,EAAQnH,SAAQ,SAACqH,EAAQxF,GAErBjB,EAAayG,GAAQ,GAAO,SAAApF,GACxBwI,EAAe5I,GAAKI,IACd+I,IAAUlJ,GACZhB,EAAS2J,EAAetB,KAAKhG,USmJrC8H,CAAc9D,GAAS,SAAA/G,GACnB8K,EAAKC,QAAQ/K,GAAM,WACf8K,EAAKtE,UAAW,EAChBsE,EAAKrG,aAAa,iDAUtBgC,EAAQhD,KAAKgD,OAAS,GACpBuE,EAASvH,KAAKkC,KAAKiE,OAAS,QAAU,OACxCJ,EAAO,IAEP,IAAU/F,KAAKkC,KAAKsF,oBACpBxE,EAAMhD,KAAKkC,KAAKuF,gBAAkBtD,KAEjCnE,KAAKhD,gBAAmBgG,EAAM0E,MAC/B1E,EAAM2E,IAAM,GAGZ3H,KAAKkC,KAAK6D,OACR,UAAYwB,GAAqC,MAA3BK,OAAO5H,KAAKkC,KAAK6D,OACpC,SAAWwB,GAAqC,KAA3BK,OAAO5H,KAAKkC,KAAK6D,SAC3CA,EAAO,IAAM/F,KAAKkC,KAAK6D,UAErB8B,EAAe/D,EAAOd,UAEpBuE,EACJ,QAF8C,IAArCvH,KAAKkC,KAAK+D,SAAS6B,QAAQ,KAG5B,IAAM9H,KAAKkC,KAAK+D,SAAW,IAAMjG,KAAKkC,KAAK+D,UACnDF,EACA/F,KAAKkC,KAAK6F,MACTF,EAAa5J,OAAS,IAAM4J,EAAe,0CAQ5C3F,yDAAO,YACGA,EAAM,CAAE8D,GAAIhG,KAAKgG,GAAIE,GAAIlG,KAAKkG,IAAMlG,KAAKkC,MAChD,IAAI8F,EAAQhI,KAAKiI,MAAO/F,mCAS3B3F,EAAMuD,cACJoI,EAAMlI,KAAKmI,QAAQ,CACrBC,OAAQ,OACR7L,KAAMA,IAEV2L,EAAIvI,GAAG,UAAWG,GAClBoI,EAAIvI,GAAG,SAAS,SAAC0I,EAAWzF,GACxB0F,EAAKC,QAAQ,iBAAkBF,EAAWzF,kDASxCsF,EAAMlI,KAAKmI,UACjBD,EAAIvI,GAAG,OAAQK,KAAKwI,OAAOnG,KAAKrC,OAChCkI,EAAIvI,GAAG,SAAS,SAAC0I,EAAWzF,GACxB6F,EAAKF,QAAQ,iBAAkBF,EAAWzF,WAEzC8F,QAAUR,qCA5LR,iBAjCcpF,GAgOhBkF,2CAOGC,EAAK/F,0BAEbD,oBAA4BC,KACvBA,KAAOA,IACPkG,OAASlG,EAAKkG,QAAU,QACxBH,IAAMA,IACNU,OAAQ,IAAUzG,EAAKyG,QACvBpM,UAAOqM,IAAc1G,EAAK3F,KAAO2F,EAAK3F,KAAO,OAC7CP,iEAQCkG,EAAOX,EAAKvB,KAAKkC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKiD,UAAYnF,KAAKkC,KAAK8D,GAC3B9D,EAAK2G,UAAY7I,KAAKkC,KAAKgE,OACrB4C,EAAO9I,KAAK8I,IAAM,IAAI/D,EAAe7C,OAEvC4G,EAAIC,KAAK/I,KAAKoI,OAAQpI,KAAKiI,IAAKjI,KAAK2I,cAE7B3I,KAAKkC,KAAK8G,iBAEL,IAAIhL,KADT8K,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzCjJ,KAAKkC,KAAK8G,aAChBhJ,KAAKkC,KAAK8G,aAAapH,eAAe5D,IACtC8K,EAAII,iBAAiBlL,EAAGgC,KAAKkC,KAAK8G,aAAahL,IAK/D,MAAOoH,OACH,SAAWpF,KAAKoI,WAEZU,EAAII,iBAAiB,eAAgB,4BAEzC,MAAO9D,QAGP0D,EAAII,iBAAiB,SAAU,OAEnC,MAAO9D,IAEH,oBAAqB0D,IACrBA,EAAIK,gBAAkBnJ,KAAKkC,KAAKiH,iBAEhCnJ,KAAKkC,KAAKkH,iBACVN,EAAIO,QAAUrJ,KAAKkC,KAAKkH,gBAE5BN,EAAIQ,mBAAqB,WACjB,IAAMR,EAAI7F,aAEV,MAAQ6F,EAAIS,QAAU,OAAST,EAAIS,OACnCC,EAAKC,SAKLD,EAAKpH,cAAa,WACdoH,EAAKjB,QAA8B,iBAAfO,EAAIS,OAAsBT,EAAIS,OAAS,KAC5D,KAGXT,EAAIY,KAAK1J,KAAKzD,MAElB,MAAO6I,oBAIEhD,cAAa,WACdoH,EAAKjB,QAAQnD,KACd,GAGiB,oBAAbuE,gBACFC,MAAQ5B,EAAQ6B,gBACrB7B,EAAQ8B,SAAS9J,KAAK4J,OAAS5J,sCAQ/BgF,QACChE,aAAa,QAASgE,EAAKhF,KAAK8I,UAChCiB,SAAQ,mCAOTC,WACA,IAAuBhK,KAAK8I,KAAO,OAAS9I,KAAK8I,aAGhDA,IAAIQ,mBAAqB/D,EAC1ByE,WAESlB,IAAImB,QAEb,MAAO7E,IAEa,oBAAbuE,iBACA3B,EAAQ8B,SAAS9J,KAAK4J,YAE5Bd,IAAM,2CAQLvM,EAAOyD,KAAK8I,IAAIoB,aACT,OAAT3N,SACKyE,aAAa,OAAQzE,QACrByE,aAAa,gBACb+I,gDASJA,iBAxIgBtK,GAkJ7B,GAPAuI,EAAQ6B,cAAgB,EACxB7B,EAAQ8B,SAAW,GAMK,oBAAbH,YAEoB,mBAAhBQ,YAEPA,YAAY,WAAYC,SAEvB,GAAgC,mBAArBxK,iBAAiC,CAE7CA,iBADyB,eAAgB0C,EAAa,WAAa,SAChC8H,IAAe,GAG1D,SAASA,SACA,IAAIpM,KAAKgK,EAAQ8B,SACd9B,EAAQ8B,SAASlI,eAAe5D,IAChCgK,EAAQ8B,SAAS9L,GAAGiM,QC9YzB,IAAMI,GACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAAA9J,UAAM6J,QAAQC,UAAUC,KAAK/J,IAG7B,SAACA,EAAI2B,UAAiBA,EAAa3B,EAAI,IAGzCgK,GAAYnI,EAAWmI,WAAanI,EAAWoI,aCHtDC,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,4CAOG7I,yCACFA,IACDlF,gBAAkBkF,EAAKkE,4DAgBvBpG,KAAKgL,aAIJ/C,EAAMjI,KAAKiI,MACXgD,EAAYjL,KAAKkC,KAAK+I,UAEtB/I,EAAOyI,GACP,GACApJ,EAAKvB,KAAKkC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMlC,KAAKkC,KAAK8G,eACV9G,EAAKgJ,QAAUlL,KAAKkC,KAAK8G,uBAGpBmC,GACyBR,GAIpB,IAAIF,GAAUxC,EAAKgD,EAAW/I,GAH9B+I,EACI,IAAIR,GAAUxC,EAAKgD,GACnB,IAAIR,GAAUxC,GAGhC,MAAOjD,UACIhF,KAAKgB,aAAa,QAASgE,QAEjCmG,GAAG9M,WAAa2B,KAAKkD,OAAO7E,YD/CR,mBCgDpB+M,iFAQAD,GAAGE,OAAS,WACT7E,EAAKtE,KAAKoJ,WACV9E,EAAK2E,GAAGI,QAAQC,QAEpBhF,EAAKQ,eAEJmE,GAAGM,QAAU,SAAAC,UAAclF,EAAKnD,QAAQ,CACzCV,YAAa,8BACbC,QAAS8I,UAERP,GAAGQ,UAAY,SAAAC,UAAMpF,EAAKgC,OAAOoD,EAAGrP,YACpC4O,GAAGU,QAAU,SAAAzG,UAAKoB,EAAK+B,QAAQ,kBAAmBnD,kCAQrD9B,mBACGP,UAAW,qBAGP/E,OACCwF,EAASF,EAAQtF,GACjB8N,EAAa9N,IAAMsF,EAAQrF,OAAS,EAC1ClB,EAAayG,EAAQuD,EAAK/J,gBAAgB,SAAAT,OAsB9BwK,EAAKoE,GAAGzB,KAAKnN,GAMrB,MAAO6I,IAEH0G,GAGAzB,IAAS,WACLtD,EAAKhE,UAAW,EAChBgE,EAAK/F,aAAa,WACnB+F,EAAK3E,kBAvCXpE,EAAI,EAAGA,EAAIsF,EAAQrF,OAAQD,MAA3BA,0CAkDc,IAAZgC,KAAKmL,UACPA,GAAGlE,aACHkE,GAAK,wCASVnI,EAAQhD,KAAKgD,OAAS,GACpBuE,EAASvH,KAAKkC,KAAKiE,OAAS,MAAQ,KACtCJ,EAAO,GAEP/F,KAAKkC,KAAK6D,OACR,QAAUwB,GAAqC,MAA3BK,OAAO5H,KAAKkC,KAAK6D,OAClC,OAASwB,GAAqC,KAA3BK,OAAO5H,KAAKkC,KAAK6D,SACzCA,EAAO,IAAM/F,KAAKkC,KAAK6D,MAGvB/F,KAAKkC,KAAKsF,oBACVxE,EAAMhD,KAAKkC,KAAKuF,gBAAkBtD,KAGjCnE,KAAKhD,iBACNgG,EAAM2E,IAAM,OAEVE,EAAe/D,EAAOd,UAEpBuE,EACJ,QAF8C,IAArCvH,KAAKkC,KAAK+D,SAAS6B,QAAQ,KAG5B,IAAM9H,KAAKkC,KAAK+D,SAAW,IAAMjG,KAAKkC,KAAK+D,UACnDF,EACA/F,KAAKkC,KAAK6F,MACTF,EAAa5J,OAAS,IAAM4J,EAAe,4CASvC4C,sCA9JF,mBAjBS3H,GCRXiJ,GAAa,CACtBC,UAAWjB,GACXpF,QAASD,GCGPuG,GAAK,0OACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAM7H,OACZ8H,EAAM9H,EAAK+H,EAAI/H,EAAIwD,QAAQ,KAAM1C,EAAId,EAAIwD,QAAQ,MAC7C,GAANuE,IAAiB,GAANjH,IACXd,EAAMA,EAAI7F,UAAU,EAAG4N,GAAK/H,EAAI7F,UAAU4N,EAAGjH,GAAGkH,QAAQ,KAAM,KAAOhI,EAAI7F,UAAU2G,EAAGd,EAAIrG,iBA0B3E+E,EACbzG,EAzBFgQ,EAAIN,GAAGO,KAAKlI,GAAO,IAAK2D,EAAM,GAAIjK,EAAI,GACnCA,KACHiK,EAAIiE,GAAMlO,IAAMuO,EAAEvO,IAAM,UAElB,GAANqO,IAAiB,GAANjH,IACX6C,EAAIwE,OAASL,EACbnE,EAAIyE,KAAOzE,EAAIyE,KAAKjO,UAAU,EAAGwJ,EAAIyE,KAAKzO,OAAS,GAAGqO,QAAQ,KAAM,KACpErE,EAAI0E,UAAY1E,EAAI0E,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9ErE,EAAI2E,SAAU,GAElB3E,EAAI4E,UAIR,SAAmB3P,EAAK6K,OACd+E,EAAO,WAAYC,EAAQhF,EAAKuE,QAAQQ,EAAM,KAAKnP,MAAM,KACtC,KAArBoK,EAAKiF,OAAO,EAAG,IAA6B,IAAhBjF,EAAK9J,QACjC8O,EAAMpM,OAAO,EAAG,GAEmB,KAAnCoH,EAAKiF,OAAOjF,EAAK9J,OAAS,EAAG,IAC7B8O,EAAMpM,OAAOoM,EAAM9O,OAAS,EAAG,UAE5B8O,EAZSF,CAAU5E,EAAKA,EAAG,MAClCA,EAAIgF,UAaejK,EAbUiF,EAAG,MAc1B1L,EAAO,GACbyG,EAAMsJ,QAAQ,6BAA6B,SAAUY,EAAIC,EAAIC,GACrDD,IACA5Q,EAAK4Q,GAAMC,MAGZ7Q,GAnBA0L,MCtBEoF,4CAQGpF,SAAK/F,yDAAO,mCAEhB+F,GAAO,aAAoBA,KAC3B/F,EAAO+F,EACPA,EAAM,MAENA,GACAA,EAAMkE,GAAMlE,GACZ/F,EAAK+D,SAAWgC,EAAIyE,KACpBxK,EAAKiE,OAA0B,UAAjB8B,EAAInC,UAAyC,QAAjBmC,EAAInC,SAC9C5D,EAAK6D,KAAOkC,EAAIlC,KACZkC,EAAIjF,QACJd,EAAKc,MAAQiF,EAAIjF,QAEhBd,EAAKwK,OACVxK,EAAK+D,SAAWkG,GAAMjK,EAAKwK,MAAMA,MAErCzK,OAA4BC,KACvBiE,OACD,MAAQjE,EAAKiE,OACPjE,EAAKiE,OACe,oBAAbP,UAA4B,WAAaA,SAASE,SAC/D5D,EAAK+D,WAAa/D,EAAK6D,OAEvB7D,EAAK6D,KAAOuH,EAAKnH,OAAS,MAAQ,QAEjCF,SACD/D,EAAK+D,WACoB,oBAAbL,SAA2BA,SAASK,SAAW,eAC1DF,KACD7D,EAAK6D,OACoB,oBAAbH,UAA4BA,SAASG,KACvCH,SAASG,KACTuH,EAAKnH,OACD,MACA,QACb4F,WAAa7J,EAAK6J,YAAc,CAAC,UAAW,eAC5C9I,WAAa,KACbsK,YAAc,KACdC,cAAgB,IAChBtL,KAAOuL,EAAc,CACtB1F,KAAM,aACN2F,OAAO,EACPvE,iBAAiB,EACjBwE,SAAS,EACTlG,eAAgB,IAChBmG,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,GAClBC,qBAAqB,GACtB/L,KACEA,KAAK6F,KAAOuF,EAAKpL,KAAK6F,KAAKuE,QAAQ,MAAO,IAAM,IACtB,iBAApBgB,EAAKpL,KAAKc,UACZd,KAAKc,MAAQ3D,EAAOiO,EAAKpL,KAAKc,UAGlCkL,GAAK,OACLC,SAAW,OACXC,aAAe,OACfC,YAAc,OAEdC,iBAAmB,KACQ,mBAArB1O,mBACH0N,EAAKpL,KAAK+L,qBAIVrO,iBAAiB,gBAAgB,WACzB0N,EAAKiB,cAEAA,UAAUhO,uBACVgO,UAAUtH,YAEpB,GAEe,cAAlBqG,EAAKrH,aACAuI,qBAAuB,aACnBnL,QAAQ,kBAAmB,CAC5BV,YAAa,6BAGrB/C,iBAAiB,UAAW0N,EAAKkB,sBAAsB,OAG1DzF,2DASO0F,OACNzL,EAAQyK,EAAc,GAAIzN,KAAKkC,KAAKc,OAE1CA,EAAM0L,IdnFU,EcqFhB1L,EAAMuL,UAAYE,EAEdzO,KAAKkO,KACLlL,EAAM0E,IAAM1H,KAAKkO,QACfhM,EAAOuL,EAAc,GAAIzN,KAAKkC,KAAK8L,iBAAiBS,GAAOzO,KAAKkC,KAAM,CACxEc,MAAAA,EACAE,OAAQlD,KACRiG,SAAUjG,KAAKiG,SACfE,OAAQnG,KAAKmG,OACbJ,KAAM/F,KAAK+F,cAER,IAAIgG,GAAW0C,GAAMvM,sCAQxBqM,YACAvO,KAAKkC,KAAK0L,iBACVP,EAAOsB,wBACmC,IAA1C3O,KAAK+L,WAAWjE,QAAQ,aACxByG,EAAY,gBAEX,CAAA,GAAI,IAAMvO,KAAK+L,WAAW9N,wBAEtBmE,cAAa,WACdoE,EAAKxF,aAAa,QAAS,6BAC5B,GAIHuN,EAAYvO,KAAK+L,WAAW,QAE3B9I,WAAa,cAGdsL,EAAYvO,KAAK4O,gBAAgBL,GAErC,MAAOnJ,eACE2G,WAAW8C,kBACX9F,OAGTwF,EAAUxF,YACL+F,aAAaP,wCAOTA,cACLvO,KAAKuO,gBACAA,UAAUhO,0BAGdgO,UAAYA,EAEjBA,EACK5O,GAAG,QAASK,KAAK+O,QAAQ1M,KAAKrC,OAC9BL,GAAG,SAAUK,KAAKyD,SAASpB,KAAKrC,OAChCL,GAAG,QAASK,KAAKuI,QAAQlG,KAAKrC,OAC9BL,GAAG,SAAS,SAAA+C,UAAUqE,EAAK1D,QAAQ,kBAAmBX,oCAQzD+L,cACEF,EAAYvO,KAAK4O,gBAAgBH,GACjCO,GAAS,EACb3B,EAAOsB,uBAAwB,MACzBM,EAAkB,WAChBD,IAEJT,EAAU7E,KAAK,CAAC,CAAEpN,KAAM,OAAQC,KAAM,WACtCgS,EAAUrO,KAAK,UAAU,SAAAgP,OACjBF,KAEA,SAAWE,EAAI5S,MAAQ,UAAY4S,EAAI3S,KAAM,IAC7C2K,EAAKiI,WAAY,EACjBjI,EAAKlG,aAAa,YAAauN,IAC1BA,EACD,OACJlB,EAAOsB,sBAAwB,cAAgBJ,EAAUE,KACzDvH,EAAKqH,UAAUhI,OAAM,WACbyI,GAEA,WAAa9H,EAAKjE,aAEtB8G,IACA7C,EAAK4H,aAAaP,GAClBA,EAAU7E,KAAK,CAAC,CAAEpN,KAAM,aACxB4K,EAAKlG,aAAa,UAAWuN,GAC7BA,EAAY,KACZrH,EAAKiI,WAAY,EACjBjI,EAAKkI,gBAGR,KACKpK,EAAM,IAAInC,MAAM,eAEtBmC,EAAIuJ,UAAYA,EAAUE,KAC1BvH,EAAKlG,aAAa,eAAgBgE,kBAIrCqK,IACDL,IAGJA,GAAS,EACTjF,IACAwE,EAAUtH,QACVsH,EAAY,UAGV1C,EAAU,SAAA7G,OACNsK,EAAQ,IAAIzM,MAAM,gBAAkBmC,GAE1CsK,EAAMf,UAAYA,EAAUE,KAC5BY,IACAnI,EAAKlG,aAAa,eAAgBsO,aAE7BC,IACL1D,EAAQ,6BAGHJ,IACLI,EAAQ,0BAGH2D,EAAUC,GACXlB,GAAakB,EAAGhB,OAASF,EAAUE,MACnCY,QAIFtF,EAAU,WACZwE,EAAUjO,eAAe,OAAQ2O,GACjCV,EAAUjO,eAAe,QAASuL,GAClC0C,EAAUjO,eAAe,QAASiP,GAClCrI,EAAK/G,IAAI,QAASsL,GAClBvE,EAAK/G,IAAI,YAAaqP,IAE1BjB,EAAUrO,KAAK,OAAQ+O,GACvBV,EAAUrO,KAAK,QAAS2L,GACxB0C,EAAUrO,KAAK,QAASqP,QACnBrP,KAAK,QAASuL,QACdvL,KAAK,YAAasP,GACvBjB,EAAUxF,gDAQL9F,WAAa,OAClBoK,EAAOsB,sBAAwB,cAAgB3O,KAAKuO,UAAUE,UACzDzN,aAAa,aACboO,QAGD,SAAWpP,KAAKiD,YAChBjD,KAAKkC,KAAKyL,SACV3N,KAAKuO,UAAUhI,cACXvI,EAAI,EACF2G,EAAI3E,KAAKmO,SAASlQ,OACjBD,EAAI2G,EAAG3G,SACL0R,MAAM1P,KAAKmO,SAASnQ,qCAS5BwF,MACD,YAAcxD,KAAKiD,YACnB,SAAWjD,KAAKiD,YAChB,YAAcjD,KAAKiD,uBACdjC,aAAa,SAAUwC,QAEvBxC,aAAa,aACVwC,EAAOlH,UACN,YACIqT,YAAYC,KAAKzD,MAAM3I,EAAOjH,iBAElC,YACIsT,wBACAC,WAAW,aACX9O,aAAa,aACbA,aAAa,kBAEjB,YACKgE,EAAM,IAAInC,MAAM,gBAEtBmC,EAAI+K,KAAOvM,EAAOjH,UACbgM,QAAQvD,aAEZ,eACIhE,aAAa,OAAQwC,EAAOjH,WAC5ByE,aAAa,UAAWwC,EAAOjH,2CAaxCA,QACHyE,aAAa,YAAazE,QAC1B2R,GAAK3R,EAAKmL,SACV6G,UAAUvL,MAAM0E,IAAMnL,EAAKmL,SAC3ByG,SAAWnO,KAAKgQ,eAAezT,EAAK4R,eACpCC,aAAe7R,EAAK6R,kBACpBC,YAAc9R,EAAK8R,iBACnB4B,WAAa1T,EAAK0T,gBAClBjJ,SAED,WAAahH,KAAKiD,iBAEjB4M,8EAQAtN,eAAevC,KAAKsO,uBACpBA,iBAAmBtO,KAAKoC,cAAa,WACtCiF,EAAKhE,QAAQ,kBACdrD,KAAKoO,aAAepO,KAAKqO,aACxBrO,KAAKkC,KAAKoJ,gBACLgD,iBAAiB9C,+CASrB+B,YAAY5M,OAAO,EAAGX,KAAKwN,oBAI3BA,cAAgB,EACjB,IAAMxN,KAAKuN,YAAYtP,YAClB+C,aAAa,cAGboO,2CASL,WAAapP,KAAKiD,YAClBjD,KAAKuO,UAAUxL,WACd/C,KAAKmP,WACNnP,KAAKuN,YAAYtP,OAAQ,KACnBqF,EAAUtD,KAAKkQ,0BAChB3B,UAAU7E,KAAKpG,QAGfkK,cAAgBlK,EAAQrF,YACxB+C,aAAa,4DAUShB,KAAKiQ,YACR,YAAxBjQ,KAAKuO,UAAUE,MACfzO,KAAKuN,YAAYtP,OAAS,UAEnB+B,KAAKuN,oBXlYGrQ,EWoYfiT,EAAc,EACTnS,EAAI,EAAGA,EAAIgC,KAAKuN,YAAYtP,OAAQD,IAAK,KACxCzB,EAAOyD,KAAKuN,YAAYvP,GAAGzB,QAC7BA,IACA4T,GXvYO,iBADIjT,EWwYeX,GXjY1C,SAAoB+H,WACZ8L,EAAI,EAAGnS,EAAS,EACXD,EAAI,EAAG2G,EAAIL,EAAIrG,OAAQD,EAAI2G,EAAG3G,KACnCoS,EAAI9L,EAAIpG,WAAWF,IACX,IACJC,GAAU,EAELmS,EAAI,KACTnS,GAAU,EAELmS,EAAI,OAAUA,GAAK,MACxBnS,GAAU,GAGVD,IACAC,GAAU,UAGXA,EAvBIoS,CAAWnT,GAGf+G,KAAKqM,KAPQ,MAOFpT,EAAIqT,YAAcrT,EAAIsT,QWqY5BxS,EAAI,GAAKmS,EAAcnQ,KAAKiQ,kBACrBjQ,KAAKuN,YAAYxM,MAAM,EAAG/C,GAErCmS,GAAe,SAEZnQ,KAAKuN,0CAWV2B,EAAKuB,EAAS3Q,eACXgQ,WAAW,UAAWZ,EAAKuB,EAAS3Q,GAClCE,kCAENkP,EAAKuB,EAAS3Q,eACVgQ,WAAW,UAAWZ,EAAKuB,EAAS3Q,GAClCE,wCAWA1D,EAAMC,EAAMkU,EAAS3Q,MACxB,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAOqM,GAEP,mBAAsB6H,IACtB3Q,EAAK2Q,EACLA,EAAU,MAEV,YAAczQ,KAAKiD,YAAc,WAAajD,KAAKiD,aAGvDwN,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,aAC/BlN,EAAS,CACXlH,KAAMA,EACNC,KAAMA,EACNkU,QAASA,QAERzP,aAAa,eAAgBwC,QAC7B+J,YAAYtN,KAAKuD,GAClB1D,GACAE,KAAKE,KAAK,QAASJ,QAClBsP,oDAQCnI,EAAQ,WACVqB,EAAKjF,QAAQ,gBACbiF,EAAKiG,UAAUtH,SAEb0J,EAAkB,SAAlBA,IACFrI,EAAKnI,IAAI,UAAWwQ,GACpBrI,EAAKnI,IAAI,eAAgBwQ,GACzB1J,KAEE2J,EAAiB,WAEnBtI,EAAKpI,KAAK,UAAWyQ,GACrBrI,EAAKpI,KAAK,eAAgByQ,UAE1B,YAAc3Q,KAAKiD,YAAc,SAAWjD,KAAKiD,kBAC5CA,WAAa,UACdjD,KAAKuN,YAAYtP,YACZiC,KAAK,SAAS,WACXoI,EAAK6G,UACLyB,IAGA3J,OAIHjH,KAAKmP,UACVyB,IAGA3J,KAGDjH,qCAOHgF,GACJqI,EAAOsB,uBAAwB,OAC1B3N,aAAa,QAASgE,QACtB3B,QAAQ,kBAAmB2B,mCAO5BtC,EAAQC,GACR,YAAc3C,KAAKiD,YACnB,SAAWjD,KAAKiD,YAChB,YAAcjD,KAAKiD,kBAEdV,eAAevC,KAAKsO,uBAEpBC,UAAUhO,mBAAmB,cAE7BgO,UAAUtH,aAEVsH,UAAUhO,qBACoB,mBAAxBC,qBACPA,oBAAoB,UAAWR,KAAKwO,sBAAsB,QAGzDvL,WAAa,cAEbiL,GAAK,UAELlN,aAAa,QAAS0B,EAAQC,QAG9B4K,YAAc,QACdC,cAAgB,0CAUdW,WACL0C,EAAmB,GACrB7S,EAAI,EACF8S,EAAI3C,EAASlQ,OACZD,EAAI8S,EAAG9S,KACLgC,KAAK+L,WAAWjE,QAAQqG,EAASnQ,KAClC6S,EAAiB5Q,KAAKkO,EAASnQ,WAEhC6S,SAzjBapR,GA4jB5B4N,GAAOvH,SdpiBiB,kBe7BRmC,EAAK/F,UAAS,IAAImL,GAAOpF,EAAK/F"}