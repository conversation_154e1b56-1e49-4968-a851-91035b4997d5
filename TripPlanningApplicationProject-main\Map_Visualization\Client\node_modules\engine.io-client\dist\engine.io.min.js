/*!
 * Engine.IO v6.2.2
 * (c) 2014-2022 <PERSON>
 * Released under the MIT License.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).eio=e()}(this,(function(){"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function n(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}function o(){return o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},o.apply(this,arguments)}function i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&a(t,e)}function s(t){return s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},s(t)}function a(t,e){return a=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},a(t,e)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function c(t,e,r){return c=u()?Reflect.construct:function(t,e,r){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(t,n));return r&&a(o,r.prototype),o},c.apply(null,arguments)}function p(t){var e="function"==typeof Map?new Map:void 0;return p=function(t){if(null===t||(r=t,-1===Function.toString.call(r).indexOf("[native code]")))return t;var r;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return c(t,arguments,s(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),a(n,t)},p(t)}function h(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function l(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?h(t):e}function f(t){var e=u();return function(){var r,n=s(t);if(e){var o=s(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return l(this,r)}}function d(t,e,r){return d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(r):o.value}},d(t,e,r||t)}var y=Object.create(null);y.open="0",y.close="1",y.ping="2",y.pong="3",y.message="4",y.upgrade="5",y.noop="6";var v=Object.create(null);Object.keys(y).forEach((function(t){v[y[t]]=t}));for(var m={type:"error",data:"parser error"},g="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),b="function"==typeof ArrayBuffer,k=function(t,e,r){var n,o=t.type,i=t.data;return g&&i instanceof Blob?e?r(i):w(i,r):b&&(i instanceof ArrayBuffer||(n=i,"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(n):n&&n.buffer instanceof ArrayBuffer))?e?r(i):w(new Blob([i]),r):r(y[o]+(i||""))},w=function(t,e){var r=new FileReader;return r.onload=function(){var t=r.result.split(",")[1];e("b"+t)},r.readAsDataURL(t)},T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",S="undefined"==typeof Uint8Array?[]:new Uint8Array(256),R=0;R<T.length;R++)S[T.charCodeAt(R)]=R;var x="function"==typeof ArrayBuffer,O=function(t,e){if("string"!=typeof t)return{type:"message",data:P(t,e)};var r=t.charAt(0);return"b"===r?{type:"message",data:E(t.substring(1),e)}:v[r]?t.length>1?{type:v[r],data:t.substring(1)}:{type:v[r]}:m},E=function(t,e){if(x){var r=function(t){var e,r,n,o,i,s=.75*t.length,a=t.length,u=0;"="===t[t.length-1]&&(s--,"="===t[t.length-2]&&s--);var c=new ArrayBuffer(s),p=new Uint8Array(c);for(e=0;e<a;e+=4)r=S[t.charCodeAt(e)],n=S[t.charCodeAt(e+1)],o=S[t.charCodeAt(e+2)],i=S[t.charCodeAt(e+3)],p[u++]=r<<2|n>>4,p[u++]=(15&n)<<4|o>>2,p[u++]=(3&o)<<6|63&i;return c}(t);return P(r,e)}return{base64:!0,data:t}},P=function(t,e){return"blob"===e&&t instanceof ArrayBuffer?new Blob([t]):t},B=String.fromCharCode(30);function C(t){if(t)return function(t){for(var e in C.prototype)t[e]=C.prototype[e];return t}(t)}C.prototype.on=C.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},C.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},C.prototype.off=C.prototype.removeListener=C.prototype.removeAllListeners=C.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+t];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var o=0;o<n.length;o++)if((r=n[o])===e||r.fn===e){n.splice(o,1);break}return 0===n.length&&delete this._callbacks["$"+t],this},C.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),r=this._callbacks["$"+t],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(r){n=0;for(var o=(r=r.slice(0)).length;n<o;++n)r[n].apply(this,e)}return this},C.prototype.emitReserved=C.prototype.emit,C.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},C.prototype.hasListeners=function(t){return!!this.listeners(t).length};var L="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function q(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return r.reduce((function(e,r){return t.hasOwnProperty(r)&&(e[r]=t[r]),e}),{})}var A=setTimeout,j=clearTimeout;function _(t,e){e.useNativeTimers?(t.setTimeoutFn=A.bind(L),t.clearTimeoutFn=j.bind(L)):(t.setTimeoutFn=setTimeout.bind(L),t.clearTimeoutFn=clearTimeout.bind(L))}var U,H=function(t){i(n,t);var r=f(n);function n(t,o,i){var s;return e(this,n),(s=r.call(this,t)).description=o,s.context=i,s.type="TransportError",s}return n}(p(Error)),F=function(t){i(o,t);var r=f(o);function o(t){var n;return e(this,o),(n=r.call(this)).writable=!1,_(h(n),t),n.opts=t,n.query=t.query,n.readyState="",n.socket=t.socket,n}return n(o,[{key:"onError",value:function(t,e,r){return d(s(o.prototype),"emitReserved",this).call(this,"error",new H(t,e,r)),this}},{key:"open",value:function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this}},{key:"close",value:function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}},{key:"send",value:function(t){"open"===this.readyState&&this.write(t)}},{key:"onOpen",value:function(){this.readyState="open",this.writable=!0,d(s(o.prototype),"emitReserved",this).call(this,"open")}},{key:"onData",value:function(t){var e=O(t,this.socket.binaryType);this.onPacket(e)}},{key:"onPacket",value:function(t){d(s(o.prototype),"emitReserved",this).call(this,"packet",t)}},{key:"onClose",value:function(t){this.readyState="closed",d(s(o.prototype),"emitReserved",this).call(this,"close",t)}}]),o}(C),D="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),M={},I=0,W=0;function N(t){var e="";do{e=D[t%64]+e,t=Math.floor(t/64)}while(t>0);return e}function X(){var t=N(+new Date);return t!==U?(I=0,U=t):t+"."+N(I++)}for(;W<64;W++)M[D[W]]=W;function $(t){var e="";for(var r in t)t.hasOwnProperty(r)&&(e.length&&(e+="&"),e+=encodeURIComponent(r)+"="+encodeURIComponent(t[r]));return e}function z(t){for(var e={},r=t.split("&"),n=0,o=r.length;n<o;n++){var i=r[n].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}var V=!1;try{V="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}var G=V;function J(t){var e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||G))return new XMLHttpRequest}catch(t){}if(!e)try{return new(L[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}function K(){}var Q=null!=new J({xdomain:!1}).responseType,Y=function(t){i(s,t);var r=f(s);function s(t){var n;if(e(this,s),(n=r.call(this,t)).polling=!1,"undefined"!=typeof location){var o="https:"===location.protocol,i=location.port;i||(i=o?"443":"80"),n.xd="undefined"!=typeof location&&t.hostname!==location.hostname||i!==t.port,n.xs=t.secure!==o}var a=t&&t.forceBase64;return n.supportsBinary=Q&&!a,n}return n(s,[{key:"doOpen",value:function(){this.poll()}},{key:"pause",value:function(t){var e=this;this.readyState="pausing";var r=function(){e.readyState="paused",t()};if(this.polling||!this.writable){var n=0;this.polling&&(n++,this.once("pollComplete",(function(){--n||r()}))),this.writable||(n++,this.once("drain",(function(){--n||r()})))}else r()}},{key:"poll",value:function(){this.polling=!0,this.doPoll(),this.emitReserved("poll")}},{key:"onData",value:function(t){var e=this;(function(t,e){for(var r=t.split(B),n=[],o=0;o<r.length;o++){var i=O(r[o],e);if(n.push(i),"error"===i.type)break}return n})(t,this.socket.binaryType).forEach((function(t){if("opening"===e.readyState&&"open"===t.type&&e.onOpen(),"close"===t.type)return e.onClose({description:"transport closed by the server"}),!1;e.onPacket(t)})),"closed"!==this.readyState&&(this.polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.poll())}},{key:"doClose",value:function(){var t=this,e=function(){t.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}},{key:"write",value:function(t){var e=this;this.writable=!1,function(t,e){var r=t.length,n=new Array(r),o=0;t.forEach((function(t,i){k(t,!1,(function(t){n[i]=t,++o===r&&e(n.join(B))}))}))}(t,(function(t){e.doWrite(t,(function(){e.writable=!0,e.emitReserved("drain")}))}))}},{key:"uri",value:function(){var t=this.query||{},e=this.opts.secure?"https":"http",r="";!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=X()),this.supportsBinary||t.sid||(t.b64=1),this.opts.port&&("https"===e&&443!==Number(this.opts.port)||"http"===e&&80!==Number(this.opts.port))&&(r=":"+this.opts.port);var n=$(t);return e+"://"+(-1!==this.opts.hostname.indexOf(":")?"["+this.opts.hostname+"]":this.opts.hostname)+r+this.opts.path+(n.length?"?"+n:"")}},{key:"request",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(t,{xd:this.xd,xs:this.xs},this.opts),new Z(this.uri(),t)}},{key:"doWrite",value:function(t,e){var r=this,n=this.request({method:"POST",data:t});n.on("success",e),n.on("error",(function(t,e){r.onError("xhr post error",t,e)}))}},{key:"doPoll",value:function(){var t=this,e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(function(e,r){t.onError("xhr poll error",e,r)})),this.pollXhr=e}},{key:"name",get:function(){return"polling"}}]),s}(F),Z=function(t){i(o,t);var r=f(o);function o(t,n){var i;return e(this,o),_(h(i=r.call(this)),n),i.opts=n,i.method=n.method||"GET",i.uri=t,i.async=!1!==n.async,i.data=void 0!==n.data?n.data:null,i.create(),i}return n(o,[{key:"create",value:function(){var t=this,e=q(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this.opts.xd,e.xscheme=!!this.opts.xs;var r=this.xhr=new J(e);try{r.open(this.method,this.uri,this.async);try{if(this.opts.extraHeaders)for(var n in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(n)&&r.setRequestHeader(n,this.opts.extraHeaders[n])}catch(t){}if("POST"===this.method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{r.setRequestHeader("Accept","*/*")}catch(t){}"withCredentials"in r&&(r.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(r.timeout=this.opts.requestTimeout),r.onreadystatechange=function(){4===r.readyState&&(200===r.status||1223===r.status?t.onLoad():t.setTimeoutFn((function(){t.onError("number"==typeof r.status?r.status:0)}),0))},r.send(this.data)}catch(e){return void this.setTimeoutFn((function(){t.onError(e)}),0)}"undefined"!=typeof document&&(this.index=o.requestsCount++,o.requests[this.index]=this)}},{key:"onError",value:function(t){this.emitReserved("error",t,this.xhr),this.cleanup(!0)}},{key:"cleanup",value:function(t){if(void 0!==this.xhr&&null!==this.xhr){if(this.xhr.onreadystatechange=K,t)try{this.xhr.abort()}catch(t){}"undefined"!=typeof document&&delete o.requests[this.index],this.xhr=null}}},{key:"onLoad",value:function(){var t=this.xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this.cleanup())}},{key:"abort",value:function(){this.cleanup()}}]),o}(C);if(Z.requestsCount=0,Z.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",tt);else if("function"==typeof addEventListener){addEventListener("onpagehide"in L?"pagehide":"unload",tt,!1)}function tt(){for(var t in Z.requests)Z.requests.hasOwnProperty(t)&&Z.requests[t].abort()}var et="function"==typeof Promise&&"function"==typeof Promise.resolve?function(t){return Promise.resolve().then(t)}:function(t,e){return e(t,0)},rt=L.WebSocket||L.MozWebSocket,nt="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),ot=function(t){i(o,t);var r=f(o);function o(t){var n;return e(this,o),(n=r.call(this,t)).supportsBinary=!t.forceBase64,n}return n(o,[{key:"doOpen",value:function(){if(this.check()){var t=this.uri(),e=this.opts.protocols,r=nt?{}:q(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=nt?new rt(t,e,r):e?new rt(t,e):new rt(t)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType||"arraybuffer",this.addEventListeners()}}},{key:"addEventListeners",value:function(){var t=this;this.ws.onopen=function(){t.opts.autoUnref&&t.ws._socket.unref(),t.onOpen()},this.ws.onclose=function(e){return t.onClose({description:"websocket connection closed",context:e})},this.ws.onmessage=function(e){return t.onData(e.data)},this.ws.onerror=function(e){return t.onError("websocket error",e)}}},{key:"write",value:function(t){var e=this;this.writable=!1;for(var r=function(r){var n=t[r],o=r===t.length-1;k(n,e.supportsBinary,(function(t){try{e.ws.send(t)}catch(t){}o&&et((function(){e.writable=!0,e.emitReserved("drain")}),e.setTimeoutFn)}))},n=0;n<t.length;n++)r(n)}},{key:"doClose",value:function(){void 0!==this.ws&&(this.ws.close(),this.ws=null)}},{key:"uri",value:function(){var t=this.query||{},e=this.opts.secure?"wss":"ws",r="";this.opts.port&&("wss"===e&&443!==Number(this.opts.port)||"ws"===e&&80!==Number(this.opts.port))&&(r=":"+this.opts.port),this.opts.timestampRequests&&(t[this.opts.timestampParam]=X()),this.supportsBinary||(t.b64=1);var n=$(t);return e+"://"+(-1!==this.opts.hostname.indexOf(":")?"["+this.opts.hostname+"]":this.opts.hostname)+r+this.opts.path+(n.length?"?"+n:"")}},{key:"check",value:function(){return!!rt}},{key:"name",get:function(){return"websocket"}}]),o}(F),it={websocket:ot,polling:Y},st=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,at=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ut(t){var e=t,r=t.indexOf("["),n=t.indexOf("]");-1!=r&&-1!=n&&(t=t.substring(0,r)+t.substring(r,n).replace(/:/g,";")+t.substring(n,t.length));for(var o,i,s=st.exec(t||""),a={},u=14;u--;)a[at[u]]=s[u]||"";return-1!=r&&-1!=n&&(a.source=e,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=function(t,e){var r=/\/{2,9}/g,n=e.replace(r,"/").split("/");"/"!=e.substr(0,1)&&0!==e.length||n.splice(0,1);"/"==e.substr(e.length-1,1)&&n.splice(n.length-1,1);return n}(0,a.path),a.queryKey=(o=a.query,i={},o.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(t,e,r){e&&(i[e]=r)})),i),a}var ct=function(r){i(a,r);var s=f(a);function a(r){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e(this,a),n=s.call(this),r&&"object"===t(r)&&(i=r,r=null),r?(r=ut(r),i.hostname=r.host,i.secure="https"===r.protocol||"wss"===r.protocol,i.port=r.port,r.query&&(i.query=r.query)):i.host&&(i.hostname=ut(i.host).host),_(h(n),i),n.secure=null!=i.secure?i.secure:"undefined"!=typeof location&&"https:"===location.protocol,i.hostname&&!i.port&&(i.port=n.secure?"443":"80"),n.hostname=i.hostname||("undefined"!=typeof location?location.hostname:"localhost"),n.port=i.port||("undefined"!=typeof location&&location.port?location.port:n.secure?"443":"80"),n.transports=i.transports||["polling","websocket"],n.readyState="",n.writeBuffer=[],n.prevBufferLen=0,n.opts=o({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!0},i),n.opts.path=n.opts.path.replace(/\/$/,"")+"/","string"==typeof n.opts.query&&(n.opts.query=z(n.opts.query)),n.id=null,n.upgrades=null,n.pingInterval=null,n.pingTimeout=null,n.pingTimeoutTimer=null,"function"==typeof addEventListener&&(n.opts.closeOnBeforeunload&&addEventListener("beforeunload",(function(){n.transport&&(n.transport.removeAllListeners(),n.transport.close())}),!1),"localhost"!==n.hostname&&(n.offlineEventListener=function(){n.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",n.offlineEventListener,!1))),n.open(),n}return n(a,[{key:"createTransport",value:function(t){var e=o({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);var r=o({},this.opts.transportOptions[t],this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port});return new it[t](r)}},{key:"open",value:function(){var t,e=this;if(this.opts.rememberUpgrade&&a.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))t="websocket";else{if(0===this.transports.length)return void this.setTimeoutFn((function(){e.emitReserved("error","No transports available")}),0);t=this.transports[0]}this.readyState="opening";try{t=this.createTransport(t)}catch(t){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)}},{key:"setTransport",value:function(t){var e=this;this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",(function(t){return e.onClose("transport close",t)}))}},{key:"probe",value:function(t){var e=this,r=this.createTransport(t),n=!1;a.priorWebsocketSuccess=!1;var o=function(){n||(r.send([{type:"ping",data:"probe"}]),r.once("packet",(function(t){if(!n)if("pong"===t.type&&"probe"===t.data){if(e.upgrading=!0,e.emitReserved("upgrading",r),!r)return;a.priorWebsocketSuccess="websocket"===r.name,e.transport.pause((function(){n||"closed"!==e.readyState&&(h(),e.setTransport(r),r.send([{type:"upgrade"}]),e.emitReserved("upgrade",r),r=null,e.upgrading=!1,e.flush())}))}else{var o=new Error("probe error");o.transport=r.name,e.emitReserved("upgradeError",o)}})))};function i(){n||(n=!0,h(),r.close(),r=null)}var s=function(t){var n=new Error("probe error: "+t);n.transport=r.name,i(),e.emitReserved("upgradeError",n)};function u(){s("transport closed")}function c(){s("socket closed")}function p(t){r&&t.name!==r.name&&i()}var h=function(){r.removeListener("open",o),r.removeListener("error",s),r.removeListener("close",u),e.off("close",c),e.off("upgrading",p)};r.once("open",o),r.once("error",s),r.once("close",u),this.once("close",c),this.once("upgrading",p),r.open()}},{key:"onOpen",value:function(){if(this.readyState="open",a.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush(),"open"===this.readyState&&this.opts.upgrade&&this.transport.pause)for(var t=0,e=this.upgrades.length;t<e;t++)this.probe(this.upgrades[t])}},{key:"onPacket",value:function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.resetPingTimeout(),this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":var e=new Error("server error");e.code=t.data,this.onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}},{key:"onHandshake",value:function(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.resetPingTimeout()}},{key:"resetPingTimeout",value:function(){var t=this;this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn((function(){t.onClose("ping timeout")}),this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}},{key:"onDrain",value:function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}},{key:"flush",value:function(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){var t=this.getWritablePackets();this.transport.send(t),this.prevBufferLen=t.length,this.emitReserved("flush")}}},{key:"getWritablePackets",value:function(){if(!(this.maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;for(var t,e=1,r=0;r<this.writeBuffer.length;r++){var n=this.writeBuffer[r].data;if(n&&(e+="string"==typeof(t=n)?function(t){for(var e=0,r=0,n=0,o=t.length;n<o;n++)(e=t.charCodeAt(n))<128?r+=1:e<2048?r+=2:e<55296||e>=57344?r+=3:(n++,r+=4);return r}(t):Math.ceil(1.33*(t.byteLength||t.size))),r>0&&e>this.maxPayload)return this.writeBuffer.slice(0,r);e+=2}return this.writeBuffer}},{key:"write",value:function(t,e,r){return this.sendPacket("message",t,e,r),this}},{key:"send",value:function(t,e,r){return this.sendPacket("message",t,e,r),this}},{key:"sendPacket",value:function(t,e,r,n){if("function"==typeof e&&(n=e,e=void 0),"function"==typeof r&&(n=r,r=null),"closing"!==this.readyState&&"closed"!==this.readyState){(r=r||{}).compress=!1!==r.compress;var o={type:t,data:e,options:r};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),n&&this.once("flush",n),this.flush()}}},{key:"close",value:function(){var t=this,e=function(){t.onClose("forced close"),t.transport.close()},r=function r(){t.off("upgrade",r),t.off("upgradeError",r),e()},n=function(){t.once("upgrade",r),t.once("upgradeError",r)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(function(){t.upgrading?n():e()})):this.upgrading?n():e()),this}},{key:"onError",value:function(t){a.priorWebsocketSuccess=!1,this.emitReserved("error",t),this.onClose("transport error",t)}},{key:"onClose",value:function(t,e){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),"function"==typeof removeEventListener&&removeEventListener("offline",this.offlineEventListener,!1),this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this.prevBufferLen=0)}},{key:"filterUpgrades",value:function(t){for(var e=[],r=0,n=t.length;r<n;r++)~this.transports.indexOf(t[r])&&e.push(t[r]);return e}}]),a}(C);ct.protocol=4;return function(t,e){return new ct(t,e)}}));
//# sourceMappingURL=engine.io.min.js.map
