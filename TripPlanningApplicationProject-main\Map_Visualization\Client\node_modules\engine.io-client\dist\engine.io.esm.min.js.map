{"version": 3, "file": "engine.io.esm.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/transport.js", "../build/esm/contrib/yeast.js", "../build/esm/contrib/parseqs.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} options.\n     * @api private\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.readyState = \"\";\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @api protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     *\n     * @api public\n     */\n    open() {\n        if (\"closed\" === this.readyState || \"\" === this.readyState) {\n            this.readyState = \"opening\";\n            this.doOpen();\n        }\n        return this;\n    }\n    /**\n     * Closes the transport.\n     *\n     * @api public\n     */\n    close() {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     * @api public\n     */\n    send(packets) {\n        if (\"open\" === this.readyState) {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @api protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @api protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @api protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @api protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    /**\n     * Transport name.\n     */\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @api private\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} callback upon buffers are flushed and transport is paused\n     * @api private\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @api public\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @api private\n     */\n    onData(data) {\n        const callback = packet => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @api private\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} data packets\n     * @param {Function} drain callback\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, data => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @api private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @api private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @api private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @api public\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @api private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @api private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @api private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @api private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @api public\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return cb => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @api {Object} connection options\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Transport name.\n     *\n     * @api public\n     */\n    get name() {\n        return \"websocket\";\n    }\n    /**\n     * Opens socket.\n     *\n     * @api private\n     */\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @api private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = closeEvent => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent\n        });\n        this.ws.onmessage = ev => this.onData(ev.data);\n        this.ws.onerror = e => this.onError(\"websocket error\", e);\n    }\n    /**\n     * Writes data to socket.\n     *\n     * @param {Array} array of packets.\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, data => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    /**\n     * Closes socket.\n     *\n     * @api private\n     */\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @api public\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri or options\n     * @param {Object} opts - options\n     * @api public\n     */\n    constructor(uri, opts = {}) {\n        super();\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.readyState = \"\";\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                addEventListener(\"beforeunload\", () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                }, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} transport name\n     * @return {Transport}\n     * @api private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @api private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @api private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", reason => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} transport name\n     * @api private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", msg => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = err => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @api private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState &&\n            this.opts.upgrade &&\n            this.transport.pause) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @api private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @api private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @api private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @api private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @api private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} message.\n     * @param {Function} callback function.\n     * @param {Object} options.\n     * @return {Socket} for chaining.\n     * @api public\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @api private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     *\n     * @api public\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @api private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @api private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} server upgrades\n     * @api private\n     *\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "obj", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "globalThis", "clearTimeoutFn", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "readyState", "socket", "onError", "open", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "packet", "onPacket", "details", "alphabet", "map", "prev", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Request", "uri", "method", "async", "undefined", "xd", "xscheme", "xs", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "transports", "websocket", "forceBase64", "name", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "schema", "secure", "port", "Number", "timestampRequests", "timestampParam", "b64", "<PERSON><PERSON><PERSON><PERSON>", "hostname", "indexOf", "path", "polling", "location", "isSSL", "protocol", "poll", "pause", "onPause", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "encodePayload", "doWrite", "sid", "request", "assign", "req", "xhrStatus", "pollXhr", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "substr", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "transport", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "shift", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQC,IAC9BH,EAAqBH,EAAaM,IAAQA,KAE9C,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,EAAGT,KAAAA,EAAMC,KAAAA,GAAQS,EAAgBC,KAClD,OAAIT,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTW,EAAmBX,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVK,EAegCZ,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOD,GACnBA,GAAOA,EAAIE,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTW,EAAmB,IAAIT,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,KAxBnCY,IAAAA,GA0BTD,EAAqB,CAACX,EAAMU,KAC9B,MAAMK,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,IC9BpC,IAHA,IAAIsB,EAAQ,mEAERC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC5DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,ECPlC,MAAMnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,CAACC,EAAeC,KACjC,GAA6B,iBAAlBD,EACP,MAAO,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,IAGvC,MAAM/B,EAAO8B,EAAcG,OAAO,GAClC,GAAa,MAATjC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,GAWTmC,EAAqB,CAACjC,EAAM8B,KAC9B,GAAIxB,EAAuB,CACvB,MAAM6B,EDLD,SAAUC,GACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,IAAIG,EAAc,IAAIrC,YAAYkC,GAAeI,EAAQ,IAAIrB,WAAWoB,GACxE,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,ECbaE,CAAO9C,GACvB,OAAO+B,EAAUI,EAASL,GAG1B,MAAO,CAAEM,QAAQ,EAAMpC,KAAAA,IAGzB+B,EAAY,CAAC/B,EAAM8B,IAEZ,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,EC3Cb+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQtC,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIf,KAAOqD,EAAQ/C,UACtBS,EAAIf,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOe,EAfSuC,CAAMvC,GA2BxBsC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,MAaTN,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,WAKjB,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,MAaTN,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKK,UAAUpC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUpC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,IAEpC,IADAyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,GACpB,MAUJ,OAJyB,IAArB0C,EAAUzC,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,MAWTN,EAAQ/C,UAAUkE,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYX,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,GAG1B,GAAI0C,EAEG,CAAI1C,EAAI,EAAb,IAAK,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAML,KAAMc,GAI7B,OAAOd,MAITN,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,IAWzCJ,EAAQ/C,UAAUwE,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAO5B,QCvK3B,MAAMkD,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKpE,KAAQqE,GACzB,OAAOA,EAAKC,QAAO,CAACC,EAAKC,KACjBxE,EAAIyE,eAAeD,KACnBD,EAAIC,GAAKxE,EAAIwE,IAEVD,IACR,IAGP,MAAMG,EAAqBC,WACrBC,EAAuBC,aACtB,SAASC,EAAsB9E,EAAK+E,GACnCA,EAAKC,iBACLhF,EAAIiF,aAAeP,EAAmBQ,KAAKC,GAC3CnF,EAAIoF,eAAiBR,EAAqBM,KAAKC,KAG/CnF,EAAIiF,aAAeN,WAAWO,KAAKC,GACnCnF,EAAIoF,eAAiBP,aAAaK,KAAKC,IChB/C,MAAME,UAAuBC,MACzBC,YAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACN5C,KAAK6C,YAAcA,EACnB7C,KAAK8C,QAAUA,EACf9C,KAAKzD,KAAO,kBAGb,MAAMyG,UAAkBtD,EAO3BiD,YAAYR,GACRY,QACA/C,KAAKiD,UAAW,EAChBf,EAAsBlC,KAAMmC,GAC5BnC,KAAKmC,KAAOA,EACZnC,KAAKkD,MAAQf,EAAKe,MAClBlD,KAAKmD,WAAa,GAClBnD,KAAKoD,OAASjB,EAAKiB,OAWvBC,QAAQT,EAAQC,EAAaC,GAEzB,OADAC,MAAM9B,aAAa,QAAS,IAAIwB,EAAeG,EAAQC,EAAaC,IAC7D9C,KAOXsD,OAKI,MAJI,WAAatD,KAAKmD,YAAc,KAAOnD,KAAKmD,aAC5CnD,KAAKmD,WAAa,UAClBnD,KAAKuD,UAEFvD,KAOXwD,QAKI,MAJI,YAAcxD,KAAKmD,YAAc,SAAWnD,KAAKmD,aACjDnD,KAAKyD,UACLzD,KAAK0D,WAEF1D,KAQX2D,KAAKC,GACG,SAAW5D,KAAKmD,YAChBnD,KAAK6D,MAAMD,GAWnBE,SACI9D,KAAKmD,WAAa,OAClBnD,KAAKiD,UAAW,EAChBF,MAAM9B,aAAa,QAQvB8C,OAAOvH,GACH,MAAMwH,EAAS5F,EAAa5B,EAAMwD,KAAKoD,OAAO9E,YAC9C0B,KAAKiE,SAASD,GAOlBC,SAASD,GACLjB,MAAM9B,aAAa,SAAU+C,GAOjCN,QAAQQ,GACJlE,KAAKmD,WAAa,SAClBJ,MAAM9B,aAAa,QAASiD,IC/GpC,MAAMC,EAAW,mEAAmEvG,MAAM,IAAkBwG,EAAM,GAClH,IAAqBC,EAAjBC,EAAO,EAAGrG,EAAI,EAQX,SAASsG,EAAOC,GACnB,IAAIC,EAAU,GACd,GACIA,EAAUN,EAASK,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,GACf,OAAOC,EAsBJ,SAASG,IACZ,MAAMC,EAAMN,GAAQ,IAAIO,MACxB,OAAID,IAAQR,GACDC,EAAO,EAAGD,EAAOQ,GACrBA,EAAM,IAAMN,EAAOD,KAK9B,KAAOrG,EA9CiG,GA8CrFA,IACfmG,EAAID,EAASlG,IAAMA,ECzChB,SAASsG,EAAOnH,GACnB,IAAI2H,EAAM,GACV,IAAK,IAAI9G,KAAKb,EACNA,EAAIyE,eAAe5D,KACf8G,EAAI7G,SACJ6G,GAAO,KACXA,GAAOC,mBAAmB/G,GAAK,IAAM+G,mBAAmB5H,EAAIa,KAGpE,OAAO8G,EChBX,IAAIE,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,eAEjC,MAAOC,IAIA,MAAMC,EAAUH,ECPhB,SAASI,EAAIlD,GAChB,MAAMmD,EAAUnD,EAAKmD,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,eAGnB,MAAOK,IACP,IAAKD,EACD,IACI,OAAO,IAAI/C,EAAW,CAAC,UAAUiD,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,KCRf,SAASG,KACT,MAAMC,EAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aAkOhB,MAAMC,UAAgBnG,EAOzBiD,YAAYmD,EAAK3D,GACbY,QACAb,EAAsBlC,KAAMmC,GAC5BnC,KAAKmC,KAAOA,EACZnC,KAAK+F,OAAS5D,EAAK4D,QAAU,MAC7B/F,KAAK8F,IAAMA,EACX9F,KAAKgG,OAAQ,IAAU7D,EAAK6D,MAC5BhG,KAAKxD,UAAOyJ,IAAc9D,EAAK3F,KAAO2F,EAAK3F,KAAO,KAClDwD,KAAK/D,SAOTA,SACI,MAAMkG,EAAOX,EAAKxB,KAAKmC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKmD,UAAYtF,KAAKmC,KAAK+D,GAC3B/D,EAAKgE,UAAYnG,KAAKmC,KAAKiE,GAC3B,MAAMC,EAAOrG,KAAKqG,IAAM,IAAInB,EAAe/C,GAC3C,IACIkE,EAAI/C,KAAKtD,KAAK+F,OAAQ/F,KAAK8F,IAAK9F,KAAKgG,OACrC,IACI,GAAIhG,KAAKmC,KAAKmE,aAAc,CACxBD,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACvD,IAAK,IAAItI,KAAK+B,KAAKmC,KAAKmE,aAChBtG,KAAKmC,KAAKmE,aAAazE,eAAe5D,IACtCoI,EAAIG,iBAAiBvI,EAAG+B,KAAKmC,KAAKmE,aAAarI,KAK/D,MAAOsH,IACP,GAAI,SAAWvF,KAAK+F,OAChB,IACIM,EAAIG,iBAAiB,eAAgB,4BAEzC,MAAOjB,IAEX,IACIc,EAAIG,iBAAiB,SAAU,OAEnC,MAAOjB,IAEH,oBAAqBc,IACrBA,EAAII,gBAAkBzG,KAAKmC,KAAKsE,iBAEhCzG,KAAKmC,KAAKuE,iBACVL,EAAIM,QAAU3G,KAAKmC,KAAKuE,gBAE5BL,EAAIO,mBAAqB,KACjB,IAAMP,EAAIlD,aAEV,MAAQkD,EAAIQ,QAAU,OAASR,EAAIQ,OACnC7G,KAAK8G,SAKL9G,KAAKqC,cAAa,KACdrC,KAAKqD,QAA8B,iBAAfgD,EAAIQ,OAAsBR,EAAIQ,OAAS,KAC5D,KAGXR,EAAI1C,KAAK3D,KAAKxD,MAElB,MAAO+I,GAOH,YAHAvF,KAAKqC,cAAa,KACdrC,KAAKqD,QAAQkC,KACd,GAGiB,oBAAbwB,WACP/G,KAAKgH,MAAQnB,EAAQoB,gBACrBpB,EAAQqB,SAASlH,KAAKgH,OAAShH,MAQvCqD,QAAQ8B,GACJnF,KAAKiB,aAAa,QAASkE,EAAKnF,KAAKqG,KACrCrG,KAAKmH,SAAQ,GAOjBA,QAAQC,GACJ,QAAI,IAAuBpH,KAAKqG,KAAO,OAASrG,KAAKqG,IAArD,CAIA,GADArG,KAAKqG,IAAIO,mBAAqBlB,EAC1B0B,EACA,IACIpH,KAAKqG,IAAIgB,QAEb,MAAO9B,IAEa,oBAAbwB,iBACAlB,EAAQqB,SAASlH,KAAKgH,OAEjChH,KAAKqG,IAAM,MAOfS,SACI,MAAMtK,EAAOwD,KAAKqG,IAAIiB,aACT,OAAT9K,IACAwD,KAAKiB,aAAa,OAAQzE,GAC1BwD,KAAKiB,aAAa,WAClBjB,KAAKmH,WAQbE,QACIrH,KAAKmH,WAUb,GAPAtB,EAAQoB,cAAgB,EACxBpB,EAAQqB,SAAW,GAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArB3H,iBAAiC,CAE7CA,iBADyB,eAAgB0C,EAAa,WAAa,SAChCiF,GAAe,GAG1D,SAASA,IACL,IAAK,IAAIvJ,KAAK4H,EAAQqB,SACdrB,EAAQqB,SAASrF,eAAe5D,IAChC4H,EAAQqB,SAASjJ,GAAGoJ,QC9YzB,MAAMI,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhEjH,GAAMgH,QAAQC,UAAUC,KAAKlH,GAG7B,CAACA,EAAI2B,IAAiBA,EAAa3B,EAAI,GAGzCmH,EAAYtF,EAAWsF,WAAatF,EAAWuF,aCHtDC,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cCPV,MAACC,EAAa,CACtBC,UDOG,cAAiBpF,EAOpBL,YAAYR,GACRY,MAAMZ,GACNnC,KAAK/C,gBAAkBkF,EAAKkG,YAO5BC,WACA,MAAO,YAOX/E,SACI,IAAKvD,KAAKuI,QAEN,OAEJ,MAAMzC,EAAM9F,KAAK8F,MACX0C,EAAYxI,KAAKmC,KAAKqG,UAEtBrG,EAAO4F,EACP,GACAvG,EAAKxB,KAAKmC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMnC,KAAKmC,KAAKmE,eACVnE,EAAKsG,QAAUzI,KAAKmC,KAAKmE,cAE7B,IACItG,KAAK0I,GACyBX,EAIpB,IAAIF,EAAU/B,EAAK0C,EAAWrG,GAH9BqG,EACI,IAAIX,EAAU/B,EAAK0C,GACnB,IAAIX,EAAU/B,GAGhC,MAAOX,GACH,OAAOnF,KAAKiB,aAAa,QAASkE,GAEtCnF,KAAK0I,GAAGpK,WAAa0B,KAAKoD,OAAO9E,YD/CR,cCgDzB0B,KAAK2I,oBAOTA,oBACI3I,KAAK0I,GAAGE,OAAS,KACT5I,KAAKmC,KAAK0G,WACV7I,KAAK0I,GAAGI,QAAQC,QAEpB/I,KAAK8D,UAET9D,KAAK0I,GAAGM,QAAUC,GAAcjJ,KAAK0D,QAAQ,CACzCb,YAAa,8BACbC,QAASmG,IAEbjJ,KAAK0I,GAAGQ,UAAYC,GAAMnJ,KAAK+D,OAAOoF,EAAG3M,MACzCwD,KAAK0I,GAAGU,QAAU7D,GAAKvF,KAAKqD,QAAQ,kBAAmBkC,GAQ3D1B,MAAMD,GACF5D,KAAKiD,UAAW,EAGhB,IAAK,IAAIhF,EAAI,EAAGA,EAAI2F,EAAQ1F,OAAQD,IAAK,CACrC,MAAM+F,EAASJ,EAAQ3F,GACjBoL,EAAapL,IAAM2F,EAAQ1F,OAAS,EAC1ClB,EAAagH,EAAQhE,KAAK/C,gBAAgBT,IAmBtC,IAGQwD,KAAK0I,GAAG/E,KAAKnH,GAMrB,MAAO+I,IAEH8D,GAGA5B,GAAS,KACLzH,KAAKiD,UAAW,EAChBjD,KAAKiB,aAAa,WACnBjB,KAAKqC,kBAUxBoB,eAC2B,IAAZzD,KAAK0I,KACZ1I,KAAK0I,GAAGlF,QACRxD,KAAK0I,GAAK,MAQlB5C,MACI,IAAI5C,EAAQlD,KAAKkD,OAAS,GAC1B,MAAMoG,EAAStJ,KAAKmC,KAAKoH,OAAS,MAAQ,KAC1C,IAAIC,EAAO,GAEPxJ,KAAKmC,KAAKqH,OACR,QAAUF,GAAqC,MAA3BG,OAAOzJ,KAAKmC,KAAKqH,OAClC,OAASF,GAAqC,KAA3BG,OAAOzJ,KAAKmC,KAAKqH,SACzCA,EAAO,IAAMxJ,KAAKmC,KAAKqH,MAGvBxJ,KAAKmC,KAAKuH,oBACVxG,EAAMlD,KAAKmC,KAAKwH,gBAAkB/E,KAGjC5E,KAAK/C,iBACNiG,EAAM0G,IAAM,GAEhB,MAAMC,EAAetF,EAAOrB,GAE5B,OAAQoG,EACJ,QAF8C,IAArCtJ,KAAKmC,KAAK2H,SAASC,QAAQ,KAG5B,IAAM/J,KAAKmC,KAAK2H,SAAW,IAAM9J,KAAKmC,KAAK2H,UACnDN,EACAxJ,KAAKmC,KAAK6H,MACTH,EAAa3L,OAAS,IAAM2L,EAAe,IAQpDtB,QACI,QAASV,ICrLboC,QHWG,cAAsBjH,EAOzBL,YAAYR,GAGR,GAFAY,MAAMZ,GACNnC,KAAKiK,SAAU,EACS,oBAAbC,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAIZ,EAAOU,SAASV,KAEfA,IACDA,EAAOW,EAAQ,MAAQ,MAE3BnK,KAAKkG,GACoB,oBAAbgE,UACJ/H,EAAK2H,WAAaI,SAASJ,UAC3BN,IAASrH,EAAKqH,KACtBxJ,KAAKoG,GAAKjE,EAAKoH,SAAWY,EAK9B,MAAM9B,EAAclG,GAAQA,EAAKkG,YACjCrI,KAAK/C,eAAiB0I,IAAY0C,EAKlCC,WACA,MAAO,UAQX/E,SACIvD,KAAKqK,OAQTC,MAAMC,GACFvK,KAAKmD,WAAa,UAClB,MAAMmH,EAAQ,KACVtK,KAAKmD,WAAa,SAClBoH,KAEJ,GAAIvK,KAAKiK,UAAYjK,KAAKiD,SAAU,CAChC,IAAIuH,EAAQ,EACRxK,KAAKiK,UACLO,IACAxK,KAAKG,KAAK,gBAAgB,aACpBqK,GAASF,QAGdtK,KAAKiD,WACNuH,IACAxK,KAAKG,KAAK,SAAS,aACbqK,GAASF,aAKnBA,IAQRD,OACIrK,KAAKiK,SAAU,EACfjK,KAAKyK,SACLzK,KAAKiB,aAAa,QAOtB8C,OAAOvH,GTvFW,EAACkO,EAAgBpM,KACnC,MAAMqM,EAAiBD,EAAe9M,MAAM2B,GACtCqE,EAAU,GAChB,IAAK,IAAI3F,EAAI,EAAGA,EAAI0M,EAAezM,OAAQD,IAAK,CAC5C,MAAM2M,EAAgBxM,EAAauM,EAAe1M,GAAIK,GAEtD,GADAsF,EAAQ1D,KAAK0K,GACc,UAAvBA,EAAcrO,KACd,MAGR,OAAOqH,GS4FHiH,CAAcrO,EAAMwD,KAAKoD,OAAO9E,YAAYlC,SAd3B4H,IAMb,GAJI,YAAchE,KAAKmD,YAA8B,SAAhBa,EAAOzH,MACxCyD,KAAK8D,SAGL,UAAYE,EAAOzH,KAEnB,OADAyD,KAAK0D,QAAQ,CAAEb,YAAa,oCACrB,EAGX7C,KAAKiE,SAASD,MAKd,WAAahE,KAAKmD,aAElBnD,KAAKiK,SAAU,EACfjK,KAAKiB,aAAa,gBACd,SAAWjB,KAAKmD,YAChBnD,KAAKqK,QAWjB5G,UACI,MAAMD,EAAQ,KACVxD,KAAK6D,MAAM,CAAC,CAAEtH,KAAM,YAEpB,SAAWyD,KAAKmD,WAChBK,IAKAxD,KAAKG,KAAK,OAAQqD,GAU1BK,MAAMD,GACF5D,KAAKiD,UAAW,ET5JF,EAACW,EAAS1G,KAE5B,MAAMgB,EAAS0F,EAAQ1F,OACjByM,EAAiB,IAAI5J,MAAM7C,GACjC,IAAI4M,EAAQ,EACZlH,EAAQxH,SAAQ,CAAC4H,EAAQ/F,KAErBjB,EAAagH,GAAQ,GAAO3F,IACxBsM,EAAe1M,GAAKI,IACdyM,IAAU5M,GACZhB,EAASyN,EAAelF,KAAKlG,WSmJrCwL,CAAcnH,GAASpH,IACnBwD,KAAKgL,QAAQxO,GAAM,KACfwD,KAAKiD,UAAW,EAChBjD,KAAKiB,aAAa,eAS9B6E,MACI,IAAI5C,EAAQlD,KAAKkD,OAAS,GAC1B,MAAMoG,EAAStJ,KAAKmC,KAAKoH,OAAS,QAAU,OAC5C,IAAIC,EAAO,IAEP,IAAUxJ,KAAKmC,KAAKuH,oBACpBxG,EAAMlD,KAAKmC,KAAKwH,gBAAkB/E,KAEjC5E,KAAK/C,gBAAmBiG,EAAM+H,MAC/B/H,EAAM0G,IAAM,GAGZ5J,KAAKmC,KAAKqH,OACR,UAAYF,GAAqC,MAA3BG,OAAOzJ,KAAKmC,KAAKqH,OACpC,SAAWF,GAAqC,KAA3BG,OAAOzJ,KAAKmC,KAAKqH,SAC3CA,EAAO,IAAMxJ,KAAKmC,KAAKqH,MAE3B,MAAMK,EAAetF,EAAOrB,GAE5B,OAAQoG,EACJ,QAF8C,IAArCtJ,KAAKmC,KAAK2H,SAASC,QAAQ,KAG5B,IAAM/J,KAAKmC,KAAK2H,SAAW,IAAM9J,KAAKmC,KAAK2H,UACnDN,EACAxJ,KAAKmC,KAAK6H,MACTH,EAAa3L,OAAS,IAAM2L,EAAe,IAQpDqB,QAAQ/I,EAAO,IAEX,OADAnG,OAAOmP,OAAOhJ,EAAM,CAAE+D,GAAIlG,KAAKkG,GAAIE,GAAIpG,KAAKoG,IAAMpG,KAAKmC,MAChD,IAAI0D,EAAQ7F,KAAK8F,MAAO3D,GASnC6I,QAAQxO,EAAMuD,GACV,MAAMqL,EAAMpL,KAAKkL,QAAQ,CACrBnF,OAAQ,OACRvJ,KAAMA,IAEV4O,EAAIxL,GAAG,UAAWG,GAClBqL,EAAIxL,GAAG,SAAS,CAACyL,EAAWvI,KACxB9C,KAAKqD,QAAQ,iBAAkBgI,EAAWvI,MAQlD2H,SACI,MAAMW,EAAMpL,KAAKkL,UACjBE,EAAIxL,GAAG,OAAQI,KAAK+D,OAAOzB,KAAKtC,OAChCoL,EAAIxL,GAAG,SAAS,CAACyL,EAAWvI,KACxB9C,KAAKqD,QAAQ,iBAAkBgI,EAAWvI,MAE9C9C,KAAKsL,QAAUF,KIrOjBG,EAAK,0OACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM1G,GAClB,MAAM2G,EAAM3G,EAAK4G,EAAI5G,EAAIgF,QAAQ,KAAMxE,EAAIR,EAAIgF,QAAQ,MAC7C,GAAN4B,IAAiB,GAANpG,IACXR,EAAMA,EAAIrG,UAAU,EAAGiN,GAAK5G,EAAIrG,UAAUiN,EAAGpG,GAAGqG,QAAQ,KAAM,KAAO7G,EAAIrG,UAAU6G,EAAGR,EAAI7G,SAE9F,IAAI2N,EAAIN,EAAGO,KAAK/G,GAAO,IAAKe,EAAM,GAAI7H,EAAI,GAC1C,KAAOA,KACH6H,EAAI0F,EAAMvN,IAAM4N,EAAE5N,IAAM,GAU5B,OARU,GAAN0N,IAAiB,GAANpG,IACXO,EAAIiG,OAASL,EACb5F,EAAIkG,KAAOlG,EAAIkG,KAAKtN,UAAU,EAAGoH,EAAIkG,KAAK9N,OAAS,GAAG0N,QAAQ,KAAM,KACpE9F,EAAImG,UAAYnG,EAAImG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9E9F,EAAIoG,SAAU,GAElBpG,EAAIqG,UAIR,SAAmB/O,EAAK4M,GACpB,MAAMoC,EAAO,WAAYC,EAAQrC,EAAK4B,QAAQQ,EAAM,KAAKxO,MAAM,KACtC,KAArBoM,EAAKsC,OAAO,EAAG,IAA6B,IAAhBtC,EAAK9L,QACjCmO,EAAMzL,OAAO,EAAG,GAEmB,KAAnCoJ,EAAKsC,OAAOtC,EAAK9L,OAAS,EAAG,IAC7BmO,EAAMzL,OAAOyL,EAAMnO,OAAS,EAAG,GAEnC,OAAOmO,EAZSF,CAAUrG,EAAKA,EAAU,MACzCA,EAAIyG,SAaR,SAAkBzG,EAAK5C,GACnB,MAAM1G,EAAO,GAMb,OALA0G,EAAM0I,QAAQ,6BAA6B,SAAUY,EAAIC,EAAIC,GACrDD,IACAjQ,EAAKiQ,GAAMC,MAGZlQ,EApBQ+P,CAASzG,EAAKA,EAAW,OACjCA,ECtBJ,MAAM6G,UAAejN,EAQxBiD,YAAYmD,EAAK3D,EAAO,IACpBY,QACI+C,GAAO,iBAAoBA,IAC3B3D,EAAO2D,EACPA,EAAM,MAENA,GACAA,EAAM2F,EAAM3F,GACZ3D,EAAK2H,SAAWhE,EAAIkG,KACpB7J,EAAKoH,OAA0B,UAAjBzD,EAAIsE,UAAyC,QAAjBtE,EAAIsE,SAC9CjI,EAAKqH,KAAO1D,EAAI0D,KACZ1D,EAAI5C,QACJf,EAAKe,MAAQ4C,EAAI5C,QAEhBf,EAAK6J,OACV7J,EAAK2H,SAAW2B,EAAMtJ,EAAK6J,MAAMA,MAErC9J,EAAsBlC,KAAMmC,GAC5BnC,KAAKuJ,OACD,MAAQpH,EAAKoH,OACPpH,EAAKoH,OACe,oBAAbW,UAA4B,WAAaA,SAASE,SAC/DjI,EAAK2H,WAAa3H,EAAKqH,OAEvBrH,EAAKqH,KAAOxJ,KAAKuJ,OAAS,MAAQ,MAEtCvJ,KAAK8J,SACD3H,EAAK2H,WACoB,oBAAbI,SAA2BA,SAASJ,SAAW,aAC/D9J,KAAKwJ,KACDrH,EAAKqH,OACoB,oBAAbU,UAA4BA,SAASV,KACvCU,SAASV,KACTxJ,KAAKuJ,OACD,MACA,MAClBvJ,KAAKmI,WAAahG,EAAKgG,YAAc,CAAC,UAAW,aACjDnI,KAAKmD,WAAa,GAClBnD,KAAK4M,YAAc,GACnB5M,KAAK6M,cAAgB,EACrB7M,KAAKmC,KAAOnG,OAAOmP,OAAO,CACtBnB,KAAM,aACN8C,OAAO,EACPrG,iBAAiB,EACjBsG,SAAS,EACTpD,eAAgB,IAChBqD,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,GAClBC,qBAAqB,GACtBlL,GACHnC,KAAKmC,KAAK6H,KAAOhK,KAAKmC,KAAK6H,KAAK4B,QAAQ,MAAO,IAAM,IACtB,iBAApB5L,KAAKmC,KAAKe,QACjBlD,KAAKmC,KAAKe,MR7Cf,SAAgBoK,GACnB,IAAIC,EAAM,GACNC,EAAQF,EAAG1P,MAAM,KACrB,IAAK,IAAIK,EAAI,EAAGwP,EAAID,EAAMtP,OAAQD,EAAIwP,EAAGxP,IAAK,CAC1C,IAAIyP,EAAOF,EAAMvP,GAAGL,MAAM,KAC1B2P,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE/D,OAAOH,EQsCmBjO,CAAOU,KAAKmC,KAAKe,QAGvClD,KAAK4N,GAAK,KACV5N,KAAK6N,SAAW,KAChB7N,KAAK8N,aAAe,KACpB9N,KAAK+N,YAAc,KAEnB/N,KAAKgO,iBAAmB,KACQ,mBAArBnO,mBACHG,KAAKmC,KAAKkL,qBAIVxN,iBAAiB,gBAAgB,KACzBG,KAAKiO,YAELjO,KAAKiO,UAAUzN,qBACfR,KAAKiO,UAAUzK,YAEpB,GAEe,cAAlBxD,KAAK8J,WACL9J,KAAKkO,qBAAuB,KACxBlO,KAAK0D,QAAQ,kBAAmB,CAC5Bb,YAAa,6BAGrBhD,iBAAiB,UAAWG,KAAKkO,sBAAsB,KAG/DlO,KAAKsD,OAST6K,gBAAgB7F,GACZ,MAAMpF,EAAQlH,OAAOmP,OAAO,GAAInL,KAAKmC,KAAKe,OAE1CA,EAAMkL,IdnFU,EcqFhBlL,EAAM+K,UAAY3F,EAEdtI,KAAK4N,KACL1K,EAAM+H,IAAMjL,KAAK4N,IACrB,MAAMzL,EAAOnG,OAAOmP,OAAO,GAAInL,KAAKmC,KAAKiL,iBAAiB9E,GAAOtI,KAAKmC,KAAM,CACxEe,MAAAA,EACAE,OAAQpD,KACR8J,SAAU9J,KAAK8J,SACfP,OAAQvJ,KAAKuJ,OACbC,KAAMxJ,KAAKwJ,OAEf,OAAO,IAAIrB,EAAWG,GAAMnG,GAOhCmB,OACI,IAAI2K,EACJ,GAAIjO,KAAKmC,KAAK6K,iBACVL,EAAO0B,wBACmC,IAA1CrO,KAAKmI,WAAW4B,QAAQ,aACxBkE,EAAY,gBAEX,CAAA,GAAI,IAAMjO,KAAKmI,WAAWjK,OAK3B,YAHA8B,KAAKqC,cAAa,KACdrC,KAAKiB,aAAa,QAAS,6BAC5B,GAIHgN,EAAYjO,KAAKmI,WAAW,GAEhCnI,KAAKmD,WAAa,UAElB,IACI8K,EAAYjO,KAAKmO,gBAAgBF,GAErC,MAAO1I,GAGH,OAFAvF,KAAKmI,WAAWmG,aAChBtO,KAAKsD,OAGT2K,EAAU3K,OACVtD,KAAKuO,aAAaN,GAOtBM,aAAaN,GACLjO,KAAKiO,WACLjO,KAAKiO,UAAUzN,qBAGnBR,KAAKiO,UAAYA,EAEjBA,EACKrO,GAAG,QAASI,KAAKwO,QAAQlM,KAAKtC,OAC9BJ,GAAG,SAAUI,KAAKiE,SAAS3B,KAAKtC,OAChCJ,GAAG,QAASI,KAAKqD,QAAQf,KAAKtC,OAC9BJ,GAAG,SAASgD,GAAU5C,KAAK0D,QAAQ,kBAAmBd,KAQ/D6L,MAAMnG,GACF,IAAI2F,EAAYjO,KAAKmO,gBAAgB7F,GACjCoG,GAAS,EACb/B,EAAO0B,uBAAwB,EAC/B,MAAMM,EAAkB,KAChBD,IAEJT,EAAUtK,KAAK,CAAC,CAAEpH,KAAM,OAAQC,KAAM,WACtCyR,EAAU9N,KAAK,UAAUyO,IACrB,IAAIF,EAEJ,GAAI,SAAWE,EAAIrS,MAAQ,UAAYqS,EAAIpS,KAAM,CAG7C,GAFAwD,KAAK6O,WAAY,EACjB7O,KAAKiB,aAAa,YAAagN,IAC1BA,EACD,OACJtB,EAAO0B,sBAAwB,cAAgBJ,EAAU3F,KACzDtI,KAAKiO,UAAU3D,OAAM,KACboE,GAEA,WAAa1O,KAAKmD,aAEtBgE,IACAnH,KAAKuO,aAAaN,GAClBA,EAAUtK,KAAK,CAAC,CAAEpH,KAAM,aACxByD,KAAKiB,aAAa,UAAWgN,GAC7BA,EAAY,KACZjO,KAAK6O,WAAY,EACjB7O,KAAK8O,gBAGR,CACD,MAAM3J,EAAM,IAAIzC,MAAM,eAEtByC,EAAI8I,UAAYA,EAAU3F,KAC1BtI,KAAKiB,aAAa,eAAgBkE,SAI9C,SAAS4J,IACDL,IAGJA,GAAS,EACTvH,IACA8G,EAAUzK,QACVyK,EAAY,MAGhB,MAAM7E,EAAUjE,IACZ,MAAM6J,EAAQ,IAAItM,MAAM,gBAAkByC,GAE1C6J,EAAMf,UAAYA,EAAU3F,KAC5ByG,IACA/O,KAAKiB,aAAa,eAAgB+N,IAEtC,SAASC,IACL7F,EAAQ,oBAGZ,SAASJ,IACLI,EAAQ,iBAGZ,SAAS8F,EAAUC,GACXlB,GAAakB,EAAG7G,OAAS2F,EAAU3F,MACnCyG,IAIR,MAAM5H,EAAU,KACZ8G,EAAU1N,eAAe,OAAQoO,GACjCV,EAAU1N,eAAe,QAAS6I,GAClC6E,EAAU1N,eAAe,QAAS0O,GAClCjP,KAAKI,IAAI,QAAS4I,GAClBhJ,KAAKI,IAAI,YAAa8O,IAE1BjB,EAAU9N,KAAK,OAAQwO,GACvBV,EAAU9N,KAAK,QAASiJ,GACxB6E,EAAU9N,KAAK,QAAS8O,GACxBjP,KAAKG,KAAK,QAAS6I,GACnBhJ,KAAKG,KAAK,YAAa+O,GACvBjB,EAAU3K,OAOdQ,SAOI,GANA9D,KAAKmD,WAAa,OAClBwJ,EAAO0B,sBAAwB,cAAgBrO,KAAKiO,UAAU3F,KAC9DtI,KAAKiB,aAAa,QAClBjB,KAAK8O,QAGD,SAAW9O,KAAKmD,YAChBnD,KAAKmC,KAAK4K,SACV/M,KAAKiO,UAAU3D,MAAO,CACtB,IAAIrM,EAAI,EACR,MAAMwP,EAAIzN,KAAK6N,SAAS3P,OACxB,KAAOD,EAAIwP,EAAGxP,IACV+B,KAAKyO,MAAMzO,KAAK6N,SAAS5P,KASrCgG,SAASD,GACL,GAAI,YAAchE,KAAKmD,YACnB,SAAWnD,KAAKmD,YAChB,YAAcnD,KAAKmD,WAInB,OAHAnD,KAAKiB,aAAa,SAAU+C,GAE5BhE,KAAKiB,aAAa,aACV+C,EAAOzH,MACX,IAAK,OACDyD,KAAKoP,YAAYC,KAAK5D,MAAMzH,EAAOxH,OACnC,MACJ,IAAK,OACDwD,KAAKsP,mBACLtP,KAAKuP,WAAW,QAChBvP,KAAKiB,aAAa,QAClBjB,KAAKiB,aAAa,QAClB,MACJ,IAAK,QACD,MAAMkE,EAAM,IAAIzC,MAAM,gBAEtByC,EAAIqK,KAAOxL,EAAOxH,KAClBwD,KAAKqD,QAAQ8B,GACb,MACJ,IAAK,UACDnF,KAAKiB,aAAa,OAAQ+C,EAAOxH,MACjCwD,KAAKiB,aAAa,UAAW+C,EAAOxH,OAapD4S,YAAY5S,GACRwD,KAAKiB,aAAa,YAAazE,GAC/BwD,KAAK4N,GAAKpR,EAAKyO,IACfjL,KAAKiO,UAAU/K,MAAM+H,IAAMzO,EAAKyO,IAChCjL,KAAK6N,SAAW7N,KAAKyP,eAAejT,EAAKqR,UACzC7N,KAAK8N,aAAetR,EAAKsR,aACzB9N,KAAK+N,YAAcvR,EAAKuR,YACxB/N,KAAK0P,WAAalT,EAAKkT,WACvB1P,KAAK8D,SAED,WAAa9D,KAAKmD,YAEtBnD,KAAKsP,mBAOTA,mBACItP,KAAKwC,eAAexC,KAAKgO,kBACzBhO,KAAKgO,iBAAmBhO,KAAKqC,cAAa,KACtCrC,KAAK0D,QAAQ,kBACd1D,KAAK8N,aAAe9N,KAAK+N,aACxB/N,KAAKmC,KAAK0G,WACV7I,KAAKgO,iBAAiBjF,QAQ9ByF,UACIxO,KAAK4M,YAAYhM,OAAO,EAAGZ,KAAK6M,eAIhC7M,KAAK6M,cAAgB,EACjB,IAAM7M,KAAK4M,YAAY1O,OACvB8B,KAAKiB,aAAa,SAGlBjB,KAAK8O,QAQbA,QACI,GAAI,WAAa9O,KAAKmD,YAClBnD,KAAKiO,UAAUhL,WACdjD,KAAK6O,WACN7O,KAAK4M,YAAY1O,OAAQ,CACzB,MAAM0F,EAAU5D,KAAK2P,qBACrB3P,KAAKiO,UAAUtK,KAAKC,GAGpB5D,KAAK6M,cAAgBjJ,EAAQ1F,OAC7B8B,KAAKiB,aAAa,UAS1B0O,qBAII,KAH+B3P,KAAK0P,YACR,YAAxB1P,KAAKiO,UAAU3F,MACftI,KAAK4M,YAAY1O,OAAS,GAE1B,OAAO8B,KAAK4M,YAEhB,IAAIgD,EAAc,EAClB,IAAK,IAAI3R,EAAI,EAAGA,EAAI+B,KAAK4M,YAAY1O,OAAQD,IAAK,CAC9C,MAAMzB,EAAOwD,KAAK4M,YAAY3O,GAAGzB,KAIjC,GAHIA,IACAoT,GXvYO,iBADIxS,EWwYeZ,GXjY1C,SAAoBuI,GAChB,IAAI8K,EAAI,EAAG3R,EAAS,EACpB,IAAK,IAAID,EAAI,EAAGwP,EAAI1I,EAAI7G,OAAQD,EAAIwP,EAAGxP,IACnC4R,EAAI9K,EAAI5G,WAAWF,GACf4R,EAAI,IACJ3R,GAAU,EAEL2R,EAAI,KACT3R,GAAU,EAEL2R,EAAI,OAAUA,GAAK,MACxB3R,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,EAvBI4R,CAAW1S,GAGfsH,KAAKqL,KAPQ,MAOF3S,EAAI4S,YAAc5S,EAAI6S,QWqY5BhS,EAAI,GAAK2R,EAAc5P,KAAK0P,WAC5B,OAAO1P,KAAK4M,YAAY5L,MAAM,EAAG/C,GAErC2R,GAAe,EX7YpB,IAAoBxS,EW+YnB,OAAO4C,KAAK4M,YAWhB/I,MAAM+K,EAAKsB,EAASnQ,GAEhB,OADAC,KAAKuP,WAAW,UAAWX,EAAKsB,EAASnQ,GAClCC,KAEX2D,KAAKiL,EAAKsB,EAASnQ,GAEf,OADAC,KAAKuP,WAAW,UAAWX,EAAKsB,EAASnQ,GAClCC,KAWXuP,WAAWhT,EAAMC,EAAM0T,EAASnQ,GAS5B,GARI,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAOyJ,GAEP,mBAAsBiK,IACtBnQ,EAAKmQ,EACLA,EAAU,MAEV,YAAclQ,KAAKmD,YAAc,WAAanD,KAAKmD,WACnD,QAEJ+M,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAMnM,EAAS,CACXzH,KAAMA,EACNC,KAAMA,EACN0T,QAASA,GAEblQ,KAAKiB,aAAa,eAAgB+C,GAClChE,KAAK4M,YAAY1M,KAAK8D,GAClBjE,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK8O,QAOTtL,QACI,MAAMA,EAAQ,KACVxD,KAAK0D,QAAQ,gBACb1D,KAAKiO,UAAUzK,SAEb4M,EAAkB,KACpBpQ,KAAKI,IAAI,UAAWgQ,GACpBpQ,KAAKI,IAAI,eAAgBgQ,GACzB5M,KAEE6M,EAAiB,KAEnBrQ,KAAKG,KAAK,UAAWiQ,GACrBpQ,KAAKG,KAAK,eAAgBiQ,IAqB9B,MAnBI,YAAcpQ,KAAKmD,YAAc,SAAWnD,KAAKmD,aACjDnD,KAAKmD,WAAa,UACdnD,KAAK4M,YAAY1O,OACjB8B,KAAKG,KAAK,SAAS,KACXH,KAAK6O,UACLwB,IAGA7M,OAIHxD,KAAK6O,UACVwB,IAGA7M,KAGDxD,KAOXqD,QAAQ8B,GACJwH,EAAO0B,uBAAwB,EAC/BrO,KAAKiB,aAAa,QAASkE,GAC3BnF,KAAK0D,QAAQ,kBAAmByB,GAOpCzB,QAAQd,EAAQC,GACR,YAAc7C,KAAKmD,YACnB,SAAWnD,KAAKmD,YAChB,YAAcnD,KAAKmD,aAEnBnD,KAAKwC,eAAexC,KAAKgO,kBAEzBhO,KAAKiO,UAAUzN,mBAAmB,SAElCR,KAAKiO,UAAUzK,QAEfxD,KAAKiO,UAAUzN,qBACoB,mBAAxBC,qBACPA,oBAAoB,UAAWT,KAAKkO,sBAAsB,GAG9DlO,KAAKmD,WAAa,SAElBnD,KAAK4N,GAAK,KAEV5N,KAAKiB,aAAa,QAAS2B,EAAQC,GAGnC7C,KAAK4M,YAAc,GACnB5M,KAAK6M,cAAgB,GAU7B4C,eAAe5B,GACX,MAAMyC,EAAmB,GACzB,IAAIrS,EAAI,EACR,MAAMsS,EAAI1C,EAAS3P,OACnB,KAAOD,EAAIsS,EAAGtS,KACL+B,KAAKmI,WAAW4B,QAAQ8D,EAAS5P,KAClCqS,EAAiBpQ,KAAK2N,EAAS5P,IAEvC,OAAOqS,GAGf3D,EAAOvC,SdpiBiB,Ee5BZ,MAACA,EAAWuC,EAAOvC"}