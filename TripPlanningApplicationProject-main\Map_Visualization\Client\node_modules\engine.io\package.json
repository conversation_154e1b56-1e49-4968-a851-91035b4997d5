{"name": "engine.io", "version": "6.2.0", "description": "The realtime engine behind Socket.IO. Provides the foundation of a bidirectional connection between client and server", "type": "commonjs", "main": "./build/engine.io.js", "types": "./build/engine.io.d.ts", "exports": {"import": "./wrapper.mjs", "require": "./build/engine.io.js"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/socketio/engine.io", "contributors": [{"name": "<PERSON><PERSON>", "web": "https://github.com/EugenD<PERSON>ck"}, {"name": "<PERSON><PERSON><PERSON>", "web": "https://github.com/afshinm"}, {"name": "<PERSON>", "web": "https://github.com/cadorn"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"@types/cookie": "^0.4.1", "@types/cors": "^2.8.12", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.4.1", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~5.0.3", "ws": "~8.2.3"}, "devDependencies": {"babel-eslint": "^8.0.2", "eiows": "^3.8.0", "engine.io-client": "6.2.0", "engine.io-client-v3": "npm:engine.io-client@3.5.2", "expect.js": "^0.3.1", "mocha": "^9.1.3", "prettier": "^1.19.1", "rimraf": "^3.0.2", "superagent": "^3.8.1", "typescript": "^4.4.3", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.0.0"}, "scripts": {"compile": "rimraf ./build && tsc", "test": "npm run compile && npm run format:check && npm run test:default && npm run test:compat-v3 && npm run test:eiows && npm run test:uws", "test:default": "mocha --bail --exit", "test:compat-v3": "EIO_CLIENT=3 mocha --exit", "test:eiows": "EIO_WS_ENGINE=eiows mocha --exit", "test:uws": "EIO_WS_ENGINE=uws mocha --exit", "format:check": "prettier --check 'lib/**/*.ts' 'test/**/*.js'", "format:fix": "prettier --write 'lib/**/*.ts' 'test/**/*.js'", "prepack": "npm run compile"}, "repository": {"type": "git", "url": "**************:socketio/engine.io.git"}, "files": ["build/", "wrapper.mjs"], "engines": {"node": ">=10.0.0"}}