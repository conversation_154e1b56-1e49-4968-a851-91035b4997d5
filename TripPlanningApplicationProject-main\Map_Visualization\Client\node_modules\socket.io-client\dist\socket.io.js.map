{"version": 3, "file": "socket.io.js", "sources": ["../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../build/esm/url.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export default (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import globalThis from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} options.\n     * @api private\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.readyState = \"\";\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @api protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     *\n     * @api public\n     */\n    open() {\n        if (\"closed\" === this.readyState || \"\" === this.readyState) {\n            this.readyState = \"opening\";\n            this.doOpen();\n        }\n        return this;\n    }\n    /**\n     * Closes the transport.\n     *\n     * @api public\n     */\n    close() {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     * @api public\n     */\n    send(packets) {\n        if (\"open\" === this.readyState) {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @api protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @api protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @api protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @api protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport globalThis from \"../globalThis.js\";\nexport default function (opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport XMLHttpRequest from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport globalThis from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    /**\n     * Transport name.\n     */\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @api private\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} callback upon buffers are flushed and transport is paused\n     * @api private\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @api public\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @api private\n     */\n    onData(data) {\n        const callback = packet => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @api private\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} data packets\n     * @param {Function} drain callback\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, data => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @api private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @api private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @api private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @api public\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @api private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @api private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @api private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @api private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @api public\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import globalThis from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return cb => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @api {Object} connection options\n     * @api public\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Transport name.\n     *\n     * @api public\n     */\n    get name() {\n        return \"websocket\";\n    }\n    /**\n     * Opens socket.\n     *\n     * @api private\n     */\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @api private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = closeEvent => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent\n        });\n        this.ws.onmessage = ev => this.onData(ev.data);\n        this.ws.onerror = e => this.onError(\"websocket error\", e);\n    }\n    /**\n     * Writes data to socket.\n     *\n     * @param {Array} array of packets.\n     * @api private\n     */\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, data => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    /**\n     * Closes socket.\n     *\n     * @api private\n     */\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @api private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @api public\n     */\n    check() {\n        return (!!WebSocket &&\n            !(\"__initialize\" in WebSocket && this.name === WS.prototype.name));\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri or options\n     * @param {Object} opts - options\n     * @api public\n     */\n    constructor(uri, opts = {}) {\n        super();\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.readyState = \"\";\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                addEventListener(\"beforeunload\", () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                }, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} transport name\n     * @return {Transport}\n     * @api private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @api private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @api private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", reason => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} transport name\n     * @api private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", msg => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = err => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @api private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState &&\n            this.opts.upgrade &&\n            this.transport.pause) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @api private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @api private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @api private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @api private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @api private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} message.\n     * @param {Function} callback function.\n     * @param {Object} options.\n     * @return {Socket} for chaining.\n     * @api public\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @api private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     *\n     * @api public\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @api private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @api private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} server upgrades\n     * @api private\n     *\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.connected = false;\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        const timeout = this.flags.timeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n        this.id = id;\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * ```\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     * ```\n     *\n     * @returns self\n     * @public\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(event);\n     * });\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(event);\n     * });\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     *\n     * <pre><code>\n     *\n     * const handler = (event, ...args) => {\n     *   console.log(event);\n     * }\n     *\n     * socket.onAnyOutgoing(handler);\n     *\n     * // then later\n     * socket.offAnyOutgoing(handler);\n     *\n     * </pre></code>\n     *\n     * @public\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        this.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "lookup", "i", "decode", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "length", "decoded", "base64", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "packet", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "slice", "len", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "globalThis", "clearTimeoutFn", "BASE64_OVERHEAD", "byteLength", "utf8Length", "Math", "ceil", "size", "str", "c", "l", "charCodeAt", "TransportError", "reason", "description", "context", "Error", "Transport", "writable", "query", "readyState", "socket", "doOpen", "doClose", "onClose", "write", "onPacket", "details", "alphabet", "map", "seed", "prev", "encode", "num", "encoded", "floor", "yeast", "now", "Date", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "xdomain", "e", "concat", "empty", "hasXHR2", "xhr", "responseType", "Polling", "polling", "location", "isSSL", "port", "xd", "hostname", "xs", "secure", "forceBase64", "poll", "onPause", "pause", "total", "doPoll", "onOpen", "close", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "ipv6", "indexOf", "path", "Request", "uri", "req", "request", "method", "xhrStatus", "onError", "onData", "pollXhr", "async", "undefined", "xscheme", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "nextTick", "isPromiseAvailable", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "name", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "names", "substr", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "transport", "offlineEventListener", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "url", "loc", "test", "href", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "BINARY_EVENT", "BINARY_ACK", "encodeAsBinary", "encodeAsString", "nsp", "stringify", "deconstruction", "unshift", "Decoder", "reviver", "decodeString", "reconstructor", "BinaryReconstructor", "takeBinaryData", "p", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "reconPack", "binData", "subDestroy", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "auth", "_autoConnect", "subs", "onpacket", "subEvents", "_readyState", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "discardPacket", "notifyOutgoingListeners", "timer", "_packet", "sameNamespace", "onconnect", "onevent", "onack", "ondisconnect", "destroy", "message", "emitEvent", "_anyListeners", "listener", "sent", "emitBuffered", "_anyOutgoingListeners", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "pow", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "_a", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "maybeReconnectOnOpen", "onping", "ondata", "ondecoded", "add", "active", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "newConnection", "forceNew", "multiplex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAArB;;EACAF,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,GAAxB;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;EACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACA,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAP,CAAc,IAAd,CAA7B;EACAD,MAAM,CAACG,IAAP,CAAYJ,YAAZ,EAA0BK,OAA1B,CAAkC,UAAAC,GAAG,EAAI;EACrCH,EAAAA,oBAAoB,CAACH,YAAY,CAACM,GAAD,CAAb,CAApB,GAA0CA,GAA1C;EACH,CAFD;EAGA,IAAMC,YAAY,GAAG;EAAEC,EAAAA,IAAI,EAAE,OAAR;EAAiBC,EAAAA,IAAI,EAAE;EAAvB,CAArB;;ECXA,IAAMC,gBAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGV,MAAM,CAACW,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BH,IAA/B,MAAyC,0BAFjD;EAGA,IAAMI,uBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;EAEA,IAAMC,QAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;EAClB,SAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,IAAIA,GAAG,CAACC,MAAJ,YAAsBH,WAFnC;EAGH,CAJD;;EAKA,IAAMI,YAAY,GAAG,SAAfA,YAAe,OAAiBC,cAAjB,EAAiCC,QAAjC,EAA8C;EAAA,MAA3Cd,IAA2C,QAA3CA,IAA2C;EAAA,MAArCC,IAAqC,QAArCA,IAAqC;;EAC/D,MAAIC,gBAAc,IAAID,IAAI,YAAYE,IAAtC,EAA4C;EACxC,QAAIU,cAAJ,EAAoB;EAChB,aAAOC,QAAQ,CAACb,IAAD,CAAf;EACH,KAFD,MAGK;EACD,aAAOc,kBAAkB,CAACd,IAAD,EAAOa,QAAP,CAAzB;EACH;EACJ,GAPD,MAQK,IAAIP,uBAAqB,KACzBN,IAAI,YAAYO,WAAhB,IAA+BC,QAAM,CAACR,IAAD,CADZ,CAAzB,EAC8C;EAC/C,QAAIY,cAAJ,EAAoB;EAChB,aAAOC,QAAQ,CAACb,IAAD,CAAf;EACH,KAFD,MAGK;EACD,aAAOc,kBAAkB,CAAC,IAAIZ,IAAJ,CAAS,CAACF,IAAD,CAAT,CAAD,EAAmBa,QAAnB,CAAzB;EACH;EACJ,GAjB8D;;;EAmB/D,SAAOA,QAAQ,CAACtB,YAAY,CAACQ,IAAD,CAAZ,IAAsBC,IAAI,IAAI,EAA9B,CAAD,CAAf;EACH,CApBD;;EAqBA,IAAMc,kBAAkB,GAAG,SAArBA,kBAAqB,CAACd,IAAD,EAAOa,QAAP,EAAoB;EAC3C,MAAME,UAAU,GAAG,IAAIC,UAAJ,EAAnB;;EACAD,EAAAA,UAAU,CAACE,MAAX,GAAoB,YAAY;EAC5B,QAAMC,OAAO,GAAGH,UAAU,CAACI,MAAX,CAAkBC,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAAhB;EACAP,IAAAA,QAAQ,CAAC,MAAMK,OAAP,CAAR;EACH,GAHD;;EAIA,SAAOH,UAAU,CAACM,aAAX,CAAyBrB,IAAzB,CAAP;EACH,CAPD;;;;;;;EChCA,IAAM,KAAK,GAAG,kEAAd;;EAGA,IAAMsB,QAAM,GAAG,OAAO,UAAP,KAAsB,WAAtB,GAAoC,EAApC,GAAyC,IAAI,UAAJ,CAAe,GAAf,CAAxD;;EACA,KAAK,IAAIC,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAK,CAAC,MAA1B,EAAkCA,GAAC,EAAnC,EAAuC;EACnC,EAAAD,QAAM,CAAC,KAAK,CAAC,UAAN,CAAiBC,GAAjB,CAAD,CAAN,GAA8BA,GAA9B;EACH;;MAwBYC,QAAM,GAAG,SAAT,MAAS,CAAC,MAAD,EAAe;EACjC,MAAI,YAAY,GAAG,MAAM,CAAC,MAAP,GAAgB,IAAnC;EAAA,MACI,GAAG,GAAG,MAAM,CAAC,MADjB;EAAA,MAEI,CAFJ;EAAA,MAGI,CAAC,GAAG,CAHR;EAAA,MAII,QAJJ;EAAA,MAKI,QALJ;EAAA,MAMI,QANJ;EAAA,MAOI,QAPJ;;EASA,MAAI,MAAM,CAAC,MAAM,CAAC,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;EACnC,IAAA,YAAY;;EACZ,QAAI,MAAM,CAAC,MAAM,CAAC,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;EACnC,MAAA,YAAY;EACf;EACJ;;EAED,MAAM,WAAW,GAAG,IAAI,WAAJ,CAAgB,YAAhB,CAApB;EAAA,MACI,KAAK,GAAG,IAAI,UAAJ,CAAe,WAAf,CADZ;;EAGA,OAAK,CAAC,GAAG,CAAT,EAAY,CAAC,GAAG,GAAhB,EAAqB,CAAC,IAAI,CAA1B,EAA6B;EACzB,IAAA,QAAQ,GAAGF,QAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAlB,CAAD,CAAjB;EACA,IAAA,QAAQ,GAAGA,QAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAC,GAAG,CAAtB,CAAD,CAAjB;EACA,IAAA,QAAQ,GAAGA,QAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAC,GAAG,CAAtB,CAAD,CAAjB;EACA,IAAA,QAAQ,GAAGA,QAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAC,GAAG,CAAtB,CAAD,CAAjB;EAEA,IAAA,KAAK,CAAC,CAAC,EAAF,CAAL,GAAc,QAAQ,IAAI,CAAb,GAAmB,QAAQ,IAAI,CAA5C;EACA,IAAA,KAAK,CAAC,CAAC,EAAF,CAAL,GAAc,CAAC,QAAQ,GAAG,EAAZ,KAAmB,CAApB,GAA0B,QAAQ,IAAI,CAAnD;EACA,IAAA,KAAK,CAAC,CAAC,EAAF,CAAL,GAAc,CAAC,QAAQ,GAAG,CAAZ,KAAkB,CAAnB,GAAyB,QAAQ,GAAG,EAAjD;EACH;;EAED,SAAO,WAAP;EACJ;;EC5DA,IAAMhB,uBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;EACA,IAAMkB,YAAY,GAAG,SAAfA,YAAe,CAACC,aAAD,EAAgBC,UAAhB,EAA+B;EAChD,MAAI,OAAOD,aAAP,KAAyB,QAA7B,EAAuC;EACnC,WAAO;EACH3B,MAAAA,IAAI,EAAE,SADH;EAEHC,MAAAA,IAAI,EAAE4B,SAAS,CAACF,aAAD,EAAgBC,UAAhB;EAFZ,KAAP;EAIH;;EACD,MAAM5B,IAAI,GAAG2B,aAAa,CAACG,MAAd,CAAqB,CAArB,CAAb;;EACA,MAAI9B,IAAI,KAAK,GAAb,EAAkB;EACd,WAAO;EACHA,MAAAA,IAAI,EAAE,SADH;EAEHC,MAAAA,IAAI,EAAE8B,kBAAkB,CAACJ,aAAa,CAACK,SAAd,CAAwB,CAAxB,CAAD,EAA6BJ,UAA7B;EAFrB,KAAP;EAIH;;EACD,MAAMK,UAAU,GAAGtC,oBAAoB,CAACK,IAAD,CAAvC;;EACA,MAAI,CAACiC,UAAL,EAAiB;EACb,WAAOlC,YAAP;EACH;;EACD,SAAO4B,aAAa,CAACO,MAAd,GAAuB,CAAvB,GACD;EACElC,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAD,CAD5B;EAEEC,IAAAA,IAAI,EAAE0B,aAAa,CAACK,SAAd,CAAwB,CAAxB;EAFR,GADC,GAKD;EACEhC,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAD;EAD5B,GALN;EAQH,CA1BD;;EA2BA,IAAM+B,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC9B,IAAD,EAAO2B,UAAP,EAAsB;EAC7C,MAAIrB,uBAAJ,EAA2B;EACvB,QAAM4B,OAAO,GAAGV,QAAM,CAACxB,IAAD,CAAtB;EACA,WAAO4B,SAAS,CAACM,OAAD,EAAUP,UAAV,CAAhB;EACH,GAHD,MAIK;EACD,WAAO;EAAEQ,MAAAA,MAAM,EAAE,IAAV;EAAgBnC,MAAAA,IAAI,EAAJA;EAAhB,KAAP,CADC;EAEJ;EACJ,CARD;;EASA,IAAM4B,SAAS,GAAG,SAAZA,SAAY,CAAC5B,IAAD,EAAO2B,UAAP,EAAsB;EACpC,UAAQA,UAAR;EACI,SAAK,MAAL;EACI,aAAO3B,IAAI,YAAYO,WAAhB,GAA8B,IAAIL,IAAJ,CAAS,CAACF,IAAD,CAAT,CAA9B,GAAiDA,IAAxD;;EACJ,SAAK,aAAL;EACA;EACI,aAAOA,IAAP;EAAa;EALrB;EAOH,CARD;;ECrCA,IAAMoC,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,EAApB,CAAlB;;EACA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACC,OAAD,EAAU3B,QAAV,EAAuB;EACzC;EACA,MAAMoB,MAAM,GAAGO,OAAO,CAACP,MAAvB;EACA,MAAMQ,cAAc,GAAG,IAAIC,KAAJ,CAAUT,MAAV,CAAvB;EACA,MAAIU,KAAK,GAAG,CAAZ;EACAH,EAAAA,OAAO,CAAC5C,OAAR,CAAgB,UAACgD,MAAD,EAASrB,CAAT,EAAe;EAC3B;EACAZ,IAAAA,YAAY,CAACiC,MAAD,EAAS,KAAT,EAAgB,UAAAlB,aAAa,EAAI;EACzCe,MAAAA,cAAc,CAAClB,CAAD,CAAd,GAAoBG,aAApB;;EACA,UAAI,EAAEiB,KAAF,KAAYV,MAAhB,EAAwB;EACpBpB,QAAAA,QAAQ,CAAC4B,cAAc,CAACI,IAAf,CAAoBT,SAApB,CAAD,CAAR;EACH;EACJ,KALW,CAAZ;EAMH,GARD;EASH,CAdD;;EAeA,IAAMU,aAAa,GAAG,SAAhBA,aAAgB,CAACC,cAAD,EAAiBpB,UAAjB,EAAgC;EAClD,MAAMc,cAAc,GAAGM,cAAc,CAAC3B,KAAf,CAAqBgB,SAArB,CAAvB;EACA,MAAMI,OAAO,GAAG,EAAhB;;EACA,OAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,cAAc,CAACR,MAAnC,EAA2CV,CAAC,EAA5C,EAAgD;EAC5C,QAAMyB,aAAa,GAAGvB,YAAY,CAACgB,cAAc,CAAClB,CAAD,CAAf,EAAoBI,UAApB,CAAlC;EACAa,IAAAA,OAAO,CAACS,IAAR,CAAaD,aAAb;;EACA,QAAIA,aAAa,CAACjD,IAAd,KAAuB,OAA3B,EAAoC;EAChC;EACH;EACJ;;EACD,SAAOyC,OAAP;EACH,CAXD;;EAYO,IAAMU,UAAQ,GAAG,CAAjB;;EC9BP;EACA;EACA;EACA;EACA;EAEO,SAASC,OAAT,CAAiB1C,GAAjB,EAAsB;EAC3B,MAAIA,GAAJ,EAAS,OAAO2C,KAAK,CAAC3C,GAAD,CAAZ;EACV;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAAS2C,KAAT,CAAe3C,GAAf,EAAoB;EAClB,OAAK,IAAIZ,GAAT,IAAgBsD,OAAO,CAAChD,SAAxB,EAAmC;EACjCM,IAAAA,GAAG,CAACZ,GAAD,CAAH,GAAWsD,OAAO,CAAChD,SAAR,CAAkBN,GAAlB,CAAX;EACD;;EACD,SAAOY,GAAP;EACD;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEA0C,OAAO,CAAChD,SAAR,CAAkBkD,EAAlB,GACAF,OAAO,CAAChD,SAAR,CAAkBmD,gBAAlB,GAAqC,UAASC,KAAT,EAAgBC,EAAhB,EAAmB;EACtD,OAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;EACA,GAAC,KAAKA,UAAL,CAAgB,MAAMF,KAAtB,IAA+B,KAAKE,UAAL,CAAgB,MAAMF,KAAtB,KAAgC,EAAhE,EACGN,IADH,CACQO,EADR;EAEA,SAAO,IAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAL,OAAO,CAAChD,SAAR,CAAkBuD,IAAlB,GAAyB,UAASH,KAAT,EAAgBC,EAAhB,EAAmB;EAC1C,WAASH,EAAT,GAAc;EACZ,SAAKM,GAAL,CAASJ,KAAT,EAAgBF,EAAhB;EACAG,IAAAA,EAAE,CAACI,KAAH,CAAS,IAAT,EAAeC,SAAf;EACD;;EAEDR,EAAAA,EAAE,CAACG,EAAH,GAAQA,EAAR;EACA,OAAKH,EAAL,CAAQE,KAAR,EAAeF,EAAf;EACA,SAAO,IAAP;EACD,CATD;EAWA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAF,OAAO,CAAChD,SAAR,CAAkBwD,GAAlB,GACAR,OAAO,CAAChD,SAAR,CAAkB2D,cAAlB,GACAX,OAAO,CAAChD,SAAR,CAAkB4D,kBAAlB,GACAZ,OAAO,CAAChD,SAAR,CAAkB6D,mBAAlB,GAAwC,UAAST,KAAT,EAAgBC,EAAhB,EAAmB;EACzD,OAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC,CADyD;;EAIzD,MAAI,KAAKI,SAAS,CAAC5B,MAAnB,EAA2B;EACzB,SAAKwB,UAAL,GAAkB,EAAlB;EACA,WAAO,IAAP;EACD,GAPwD;;;EAUzD,MAAIQ,SAAS,GAAG,KAAKR,UAAL,CAAgB,MAAMF,KAAtB,CAAhB;EACA,MAAI,CAACU,SAAL,EAAgB,OAAO,IAAP,CAXyC;;EAczD,MAAI,KAAKJ,SAAS,CAAC5B,MAAnB,EAA2B;EACzB,WAAO,KAAKwB,UAAL,CAAgB,MAAMF,KAAtB,CAAP;EACA,WAAO,IAAP;EACD,GAjBwD;;;EAoBzD,MAAIW,EAAJ;;EACA,OAAK,IAAI3C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0C,SAAS,CAAChC,MAA9B,EAAsCV,CAAC,EAAvC,EAA2C;EACzC2C,IAAAA,EAAE,GAAGD,SAAS,CAAC1C,CAAD,CAAd;;EACA,QAAI2C,EAAE,KAAKV,EAAP,IAAaU,EAAE,CAACV,EAAH,KAAUA,EAA3B,EAA+B;EAC7BS,MAAAA,SAAS,CAACE,MAAV,CAAiB5C,CAAjB,EAAoB,CAApB;EACA;EACD;EACF,GA3BwD;EA8BzD;;;EACA,MAAI0C,SAAS,CAAChC,MAAV,KAAqB,CAAzB,EAA4B;EAC1B,WAAO,KAAKwB,UAAL,CAAgB,MAAMF,KAAtB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAvCD;EAyCA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAJ,OAAO,CAAChD,SAAR,CAAkBiE,IAAlB,GAAyB,UAASb,KAAT,EAAe;EACtC,OAAKE,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;EAEA,MAAIY,IAAI,GAAG,IAAI3B,KAAJ,CAAUmB,SAAS,CAAC5B,MAAV,GAAmB,CAA7B,CAAX;EAAA,MACIgC,SAAS,GAAG,KAAKR,UAAL,CAAgB,MAAMF,KAAtB,CADhB;;EAGA,OAAK,IAAIhC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsC,SAAS,CAAC5B,MAA9B,EAAsCV,CAAC,EAAvC,EAA2C;EACzC8C,IAAAA,IAAI,CAAC9C,CAAC,GAAG,CAAL,CAAJ,GAAcsC,SAAS,CAACtC,CAAD,CAAvB;EACD;;EAED,MAAI0C,SAAJ,EAAe;EACbA,IAAAA,SAAS,GAAGA,SAAS,CAACK,KAAV,CAAgB,CAAhB,CAAZ;;EACA,SAAK,IAAI/C,CAAC,GAAG,CAAR,EAAWgD,GAAG,GAAGN,SAAS,CAAChC,MAAhC,EAAwCV,CAAC,GAAGgD,GAA5C,EAAiD,EAAEhD,CAAnD,EAAsD;EACpD0C,MAAAA,SAAS,CAAC1C,CAAD,CAAT,CAAaqC,KAAb,CAAmB,IAAnB,EAAyBS,IAAzB;EACD;EACF;;EAED,SAAO,IAAP;EACD,CAlBD;;;EAqBAlB,OAAO,CAAChD,SAAR,CAAkBqE,YAAlB,GAAiCrB,OAAO,CAAChD,SAAR,CAAkBiE,IAAnD;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAChD,SAAR,CAAkBsE,SAAlB,GAA8B,UAASlB,KAAT,EAAe;EAC3C,OAAKE,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;EACA,SAAO,KAAKA,UAAL,CAAgB,MAAMF,KAAtB,KAAgC,EAAvC;EACD,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAJ,OAAO,CAAChD,SAAR,CAAkBuE,YAAlB,GAAiC,UAASnB,KAAT,EAAe;EAC9C,SAAO,CAAC,CAAE,KAAKkB,SAAL,CAAelB,KAAf,EAAsBtB,MAAhC;EACD,CAFD;;ACtKA,mBAAe,CAAC,YAAM;EAClB,MAAI,OAAO0C,IAAP,KAAgB,WAApB,EAAiC;EAC7B,WAAOA,IAAP;EACH,GAFD,MAGK,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EACpC,WAAOA,MAAP;EACH,GAFI,MAGA;EACD,WAAOC,QAAQ,CAAC,aAAD,CAAR,EAAP;EACH;EACJ,CAVc,GAAf;;ECCO,SAASC,IAAT,CAAcrE,GAAd,EAA4B;EAAA,oCAANsE,IAAM;EAANA,IAAAA,IAAM;EAAA;;EAC/B,SAAOA,IAAI,CAACC,MAAL,CAAY,UAACC,GAAD,EAAMC,CAAN,EAAY;EAC3B,QAAIzE,GAAG,CAAC0E,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;EACvBD,MAAAA,GAAG,CAACC,CAAD,CAAH,GAASzE,GAAG,CAACyE,CAAD,CAAZ;EACH;;EACD,WAAOD,GAAP;EACH,GALM,EAKJ,EALI,CAAP;EAMH;;EAED,IAAMG,kBAAkB,GAAGC,UAA3B;EACA,IAAMC,oBAAoB,GAAGC,YAA7B;EACO,SAASC,qBAAT,CAA+B/E,GAA/B,EAAoCgF,IAApC,EAA0C;EAC7C,MAAIA,IAAI,CAACC,eAAT,EAA0B;EACtBjF,IAAAA,GAAG,CAACkF,YAAJ,GAAmBP,kBAAkB,CAACQ,IAAnB,CAAwBC,UAAxB,CAAnB;EACApF,IAAAA,GAAG,CAACqF,cAAJ,GAAqBR,oBAAoB,CAACM,IAArB,CAA0BC,UAA1B,CAArB;EACH,GAHD,MAIK;EACDpF,IAAAA,GAAG,CAACkF,YAAJ,GAAmBN,UAAU,CAACO,IAAX,CAAgBC,UAAhB,CAAnB;EACApF,IAAAA,GAAG,CAACqF,cAAJ,GAAqBP,YAAY,CAACK,IAAb,CAAkBC,UAAlB,CAArB;EACH;EACJ;;EAED,IAAME,eAAe,GAAG,IAAxB;;EAEO,SAASC,UAAT,CAAoBvF,GAApB,EAAyB;EAC5B,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;EACzB,WAAOwF,UAAU,CAACxF,GAAD,CAAjB;EACH,GAH2B;;;EAK5B,SAAOyF,IAAI,CAACC,IAAL,CAAU,CAAC1F,GAAG,CAACuF,UAAJ,IAAkBvF,GAAG,CAAC2F,IAAvB,IAA+BL,eAAzC,CAAP;EACH;;EACD,SAASE,UAAT,CAAoBI,GAApB,EAAyB;EACrB,MAAIC,CAAC,GAAG,CAAR;EAAA,MAAWrE,MAAM,GAAG,CAApB;;EACA,OAAK,IAAIV,CAAC,GAAG,CAAR,EAAWgF,CAAC,GAAGF,GAAG,CAACpE,MAAxB,EAAgCV,CAAC,GAAGgF,CAApC,EAAuChF,CAAC,EAAxC,EAA4C;EACxC+E,IAAAA,CAAC,GAAGD,GAAG,CAACG,UAAJ,CAAejF,CAAf,CAAJ;;EACA,QAAI+E,CAAC,GAAG,IAAR,EAAc;EACVrE,MAAAA,MAAM,IAAI,CAAV;EACH,KAFD,MAGK,IAAIqE,CAAC,GAAG,KAAR,EAAe;EAChBrE,MAAAA,MAAM,IAAI,CAAV;EACH,KAFI,MAGA,IAAIqE,CAAC,GAAG,MAAJ,IAAcA,CAAC,IAAI,MAAvB,EAA+B;EAChCrE,MAAAA,MAAM,IAAI,CAAV;EACH,KAFI,MAGA;EACDV,MAAAA,CAAC;EACDU,MAAAA,MAAM,IAAI,CAAV;EACH;EACJ;;EACD,SAAOA,MAAP;EACH;;MChDKwE;;;;;EACF,0BAAYC,MAAZ,EAAoBC,WAApB,EAAiCC,OAAjC,EAA0C;EAAA;;EAAA;;EACtC,8BAAMF,MAAN;EACA,UAAKC,WAAL,GAAmBA,WAAnB;EACA,UAAKC,OAAL,GAAeA,OAAf;EACA,UAAK7G,IAAL,GAAY,gBAAZ;EAJsC;EAKzC;;;mCANwB8G;;MAQhBC,SAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,qBAAYrB,IAAZ,EAAkB;EAAA;;EAAA;;EACd;EACA,WAAKsB,QAAL,GAAgB,KAAhB;EACAvB,IAAAA,qBAAqB,iCAAOC,IAAP,CAArB;EACA,WAAKA,IAAL,GAAYA,IAAZ;EACA,WAAKuB,KAAL,GAAavB,IAAI,CAACuB,KAAlB;EACA,WAAKC,UAAL,GAAkB,EAAlB;EACA,WAAKC,MAAL,GAAczB,IAAI,CAACyB,MAAnB;EAPc;EAQjB;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAxBA;EAAA;EAAA,WAyBI,iBAAQR,MAAR,EAAgBC,WAAhB,EAA6BC,OAA7B,EAAsC;EAClC,kFAAmB,OAAnB,EAA4B,IAAIH,cAAJ,CAAmBC,MAAnB,EAA2BC,WAA3B,EAAwCC,OAAxC,CAA5B;;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAjCA;EAAA;EAAA,WAkCI,gBAAO;EACH,UAAI,aAAa,KAAKK,UAAlB,IAAgC,OAAO,KAAKA,UAAhD,EAA4D;EACxD,aAAKA,UAAL,GAAkB,SAAlB;EACA,aAAKE,MAAL;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EA7CA;EAAA;EAAA,WA8CI,iBAAQ;EACJ,UAAI,cAAc,KAAKF,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;EAC7D,aAAKG,OAAL;EACA,aAAKC,OAAL;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1DA;EAAA;EAAA,WA2DI,cAAK7E,OAAL,EAAc;EACV,UAAI,WAAW,KAAKyE,UAApB,EAAgC;EAC5B,aAAKK,KAAL,CAAW9E,OAAX;EACH;EAIJ;EACD;EACJ;EACA;EACA;EACA;;EAvEA;EAAA;EAAA,WAwEI,kBAAS;EACL,WAAKyE,UAAL,GAAkB,MAAlB;EACA,WAAKF,QAAL,GAAgB,IAAhB;;EACA,kFAAmB,MAAnB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAlFA;EAAA;EAAA,WAmFI,gBAAO/G,IAAP,EAAa;EACT,UAAM4C,MAAM,GAAGnB,YAAY,CAACzB,IAAD,EAAO,KAAKkH,MAAL,CAAYvF,UAAnB,CAA3B;EACA,WAAK4F,QAAL,CAAc3E,MAAd;EACH;EACD;EACJ;EACA;EACA;EACA;;EA3FA;EAAA;EAAA,WA4FI,kBAASA,MAAT,EAAiB;EACb,kFAAmB,QAAnB,EAA6BA,MAA7B;EACH;EACD;EACJ;EACA;EACA;EACA;;EAnGA;EAAA;EAAA,WAoGI,iBAAQ4E,OAAR,EAAiB;EACb,WAAKP,UAAL,GAAkB,QAAlB;;EACA,kFAAmB,OAAnB,EAA4BO,OAA5B;EACH;EAvGL;;EAAA;EAAA,EAA+BrE,OAA/B;;ECXA;;EAEA,IAAMsE,QAAQ,GAAG,mEAAmErG,KAAnE,CAAyE,EAAzE,CAAjB;EAAA,IAA+Fa,MAAM,GAAG,EAAxG;EAAA,IAA4GyF,GAAG,GAAG,EAAlH;EACA,IAAIC,IAAI,GAAG,CAAX;EAAA,IAAcpG,CAAC,GAAG,CAAlB;EAAA,IAAqBqG,IAArB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAAgBC,GAAhB,EAAqB;EACxB,MAAIC,OAAO,GAAG,EAAd;;EACA,KAAG;EACCA,IAAAA,OAAO,GAAGN,QAAQ,CAACK,GAAG,GAAG7F,MAAP,CAAR,GAAyB8F,OAAnC;EACAD,IAAAA,GAAG,GAAG5B,IAAI,CAAC8B,KAAL,CAAWF,GAAG,GAAG7F,MAAjB,CAAN;EACH,GAHD,QAGS6F,GAAG,GAAG,CAHf;;EAIA,SAAOC,OAAP;EACH;EAeD;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,KAAT,GAAiB;EACpB,MAAMC,GAAG,GAAGL,QAAM,CAAC,CAAC,IAAIM,IAAJ,EAAF,CAAlB;EACA,MAAID,GAAG,KAAKN,IAAZ,EACI,OAAOD,IAAI,GAAG,CAAP,EAAUC,IAAI,GAAGM,GAAxB;EACJ,SAAOA,GAAG,GAAG,GAAN,GAAYL,QAAM,CAACF,IAAI,EAAL,CAAzB;EACH;EAED;EACA;;EACA,OAAOpG,CAAC,GAAGU,MAAX,EAAmBV,CAAC,EAApB;EACImG,EAAAA,GAAG,CAACD,QAAQ,CAAClG,CAAD,CAAT,CAAH,GAAmBA,CAAnB;EADJ;;EChDA;;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASsG,MAAT,CAAgBpH,GAAhB,EAAqB;EACxB,MAAI4F,GAAG,GAAG,EAAV;;EACA,OAAK,IAAI9E,CAAT,IAAcd,GAAd,EAAmB;EACf,QAAIA,GAAG,CAAC0E,cAAJ,CAAmB5D,CAAnB,CAAJ,EAA2B;EACvB,UAAI8E,GAAG,CAACpE,MAAR,EACIoE,GAAG,IAAI,GAAP;EACJA,MAAAA,GAAG,IAAI+B,kBAAkB,CAAC7G,CAAD,CAAlB,GAAwB,GAAxB,GAA8B6G,kBAAkB,CAAC3H,GAAG,CAACc,CAAD,CAAJ,CAAvD;EACH;EACJ;;EACD,SAAO8E,GAAP;EACH;EACD;EACA;EACA;EACA;EACA;EACA;;EACO,SAAS7E,MAAT,CAAgB6G,EAAhB,EAAoB;EACvB,MAAIC,GAAG,GAAG,EAAV;EACA,MAAIC,KAAK,GAAGF,EAAE,CAACjH,KAAH,CAAS,GAAT,CAAZ;;EACA,OAAK,IAAIG,CAAC,GAAG,CAAR,EAAWgF,CAAC,GAAGgC,KAAK,CAACtG,MAA1B,EAAkCV,CAAC,GAAGgF,CAAtC,EAAyChF,CAAC,EAA1C,EAA8C;EAC1C,QAAIiH,IAAI,GAAGD,KAAK,CAAChH,CAAD,CAAL,CAASH,KAAT,CAAe,GAAf,CAAX;EACAkH,IAAAA,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAAnB,CAAH,GAAmCC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArD;EACH;;EACD,SAAOF,GAAP;EACH;;ECjCD;EACA,IAAII,KAAK,GAAG,KAAZ;;EACA,IAAI;EACAA,EAAAA,KAAK,GAAG,OAAOC,cAAP,KAA0B,WAA1B,IACJ,qBAAqB,IAAIA,cAAJ,EADzB;EAEH,CAHD,CAIA,OAAOC,GAAP,EAAY;EAER;EACH;;EACM,IAAMC,OAAO,GAAGH,KAAhB;;ECVP;EAGe,2BAAUjD,IAAV,EAAgB;EAC3B,MAAMqD,OAAO,GAAGrD,IAAI,CAACqD,OAArB,CAD2B;;EAG3B,MAAI;EACA,QAAI,gBAAgB,OAAOH,cAAvB,KAA0C,CAACG,OAAD,IAAYD,OAAtD,CAAJ,EAAoE;EAChE,aAAO,IAAIF,cAAJ,EAAP;EACH;EACJ,GAJD,CAKA,OAAOI,CAAP,EAAU;;EACV,MAAI,CAACD,OAAL,EAAc;EACV,QAAI;EACA,aAAO,IAAIjD,UAAU,CAAC,CAAC,QAAD,EAAWmD,MAAX,CAAkB,QAAlB,EAA4BnG,IAA5B,CAAiC,GAAjC,CAAD,CAAd,CAAsD,mBAAtD,CAAP;EACH,KAFD,CAGA,OAAOkG,CAAP,EAAU;EACb;EACJ;;ECVD,SAASE,KAAT,GAAiB;;EACjB,IAAMC,OAAO,GAAI,YAAY;EACzB,MAAMC,GAAG,GAAG,IAAIR,gBAAJ,CAAmB;EAC3BG,IAAAA,OAAO,EAAE;EADkB,GAAnB,CAAZ;EAGA,SAAO,QAAQK,GAAG,CAACC,YAAnB;EACH,CALe,EAAhB;;MAMaC,OAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,mBAAY5D,IAAZ,EAAkB;EAAA;;EAAA;;EACd,8BAAMA,IAAN;EACA,UAAK6D,OAAL,GAAe,KAAf;;EACA,QAAI,OAAOC,QAAP,KAAoB,WAAxB,EAAqC;EACjC,UAAMC,KAAK,GAAG,aAAaD,QAAQ,CAACrG,QAApC;EACA,UAAIuG,IAAI,GAAGF,QAAQ,CAACE,IAApB,CAFiC;;EAIjC,UAAI,CAACA,IAAL,EAAW;EACPA,QAAAA,IAAI,GAAGD,KAAK,GAAG,KAAH,GAAW,IAAvB;EACH;;EACD,YAAKE,EAAL,GACK,OAAOH,QAAP,KAAoB,WAApB,IACG9D,IAAI,CAACkE,QAAL,KAAkBJ,QAAQ,CAACI,QAD/B,IAEIF,IAAI,KAAKhE,IAAI,CAACgE,IAHtB;EAIA,YAAKG,EAAL,GAAUnE,IAAI,CAACoE,MAAL,KAAgBL,KAA1B;EACH;EACD;EACR;EACA;;;EACQ,QAAMM,WAAW,GAAGrE,IAAI,IAAIA,IAAI,CAACqE,WAAjC;EACA,UAAKlJ,cAAL,GAAsBsI,OAAO,IAAI,CAACY,WAAlC;EApBc;EAqBjB;EACD;EACJ;EACA;;;EA/BA;EAAA;EAAA,SAgCI,eAAW;EACP,aAAO,SAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxCA;EAAA;EAAA,WAyCI,kBAAS;EACL,WAAKC,IAAL;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAjDA;EAAA;EAAA,WAkDI,eAAMC,OAAN,EAAe;EAAA;;EACX,WAAK/C,UAAL,GAAkB,SAAlB;;EACA,UAAMgD,KAAK,GAAG,SAARA,KAAQ,GAAM;EAChB,QAAA,MAAI,CAAChD,UAAL,GAAkB,QAAlB;EACA+C,QAAAA,OAAO;EACV,OAHD;;EAIA,UAAI,KAAKV,OAAL,IAAgB,CAAC,KAAKvC,QAA1B,EAAoC;EAChC,YAAImD,KAAK,GAAG,CAAZ;;EACA,YAAI,KAAKZ,OAAT,EAAkB;EACdY,UAAAA,KAAK;EACL,eAAKxG,IAAL,CAAU,cAAV,EAA0B,YAAY;EAClC,cAAEwG,KAAF,IAAWD,KAAK,EAAhB;EACH,WAFD;EAGH;;EACD,YAAI,CAAC,KAAKlD,QAAV,EAAoB;EAChBmD,UAAAA,KAAK;EACL,eAAKxG,IAAL,CAAU,OAAV,EAAmB,YAAY;EAC3B,cAAEwG,KAAF,IAAWD,KAAK,EAAhB;EACH,WAFD;EAGH;EACJ,OAdD,MAeK;EACDA,QAAAA,KAAK;EACR;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA/EA;EAAA;EAAA,WAgFI,gBAAO;EACH,WAAKX,OAAL,GAAe,IAAf;EACA,WAAKa,MAAL;EACA,WAAK3F,YAAL,CAAkB,MAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAzFA;EAAA;EAAA,WA0FI,gBAAOxE,IAAP,EAAa;EAAA;;EACT,UAAMa,QAAQ,GAAG,SAAXA,QAAW,CAAA+B,MAAM,EAAI;EACvB;EACA,YAAI,cAAc,MAAI,CAACqE,UAAnB,IAAiCrE,MAAM,CAAC7C,IAAP,KAAgB,MAArD,EAA6D;EACzD,UAAA,MAAI,CAACqK,MAAL;EACH,SAJsB;;;EAMvB,YAAI,YAAYxH,MAAM,CAAC7C,IAAvB,EAA6B;EACzB,UAAA,MAAI,CAACsH,OAAL,CAAa;EAAEV,YAAAA,WAAW,EAAE;EAAf,WAAb;;EACA,iBAAO,KAAP;EACH,SATsB;;;EAWvB,QAAA,MAAI,CAACY,QAAL,CAAc3E,MAAd;EACH,OAZD,CADS;;;EAeTE,MAAAA,aAAa,CAAC9C,IAAD,EAAO,KAAKkH,MAAL,CAAYvF,UAAnB,CAAb,CAA4C/B,OAA5C,CAAoDiB,QAApD,EAfS;;EAiBT,UAAI,aAAa,KAAKoG,UAAtB,EAAkC;EAC9B;EACA,aAAKqC,OAAL,GAAe,KAAf;EACA,aAAK9E,YAAL,CAAkB,cAAlB;;EACA,YAAI,WAAW,KAAKyC,UAApB,EAAgC;EAC5B,eAAK8C,IAAL;EACH;EAGJ;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA1HA;EAAA;EAAA,WA2HI,mBAAU;EAAA;;EACN,UAAMM,KAAK,GAAG,SAARA,KAAQ,GAAM;EAChB,QAAA,MAAI,CAAC/C,KAAL,CAAW,CAAC;EAAEvH,UAAAA,IAAI,EAAE;EAAR,SAAD,CAAX;EACH,OAFD;;EAGA,UAAI,WAAW,KAAKkH,UAApB,EAAgC;EAC5BoD,QAAAA,KAAK;EACR,OAFD,MAGK;EACD;EACA;EACA,aAAK3G,IAAL,CAAU,MAAV,EAAkB2G,KAAlB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EA9IA;EAAA;EAAA,WA+II,eAAM7H,OAAN,EAAe;EAAA;;EACX,WAAKuE,QAAL,GAAgB,KAAhB;EACAxE,MAAAA,aAAa,CAACC,OAAD,EAAU,UAAAxC,IAAI,EAAI;EAC3B,QAAA,MAAI,CAACsK,OAAL,CAAatK,IAAb,EAAmB,YAAM;EACrB,UAAA,MAAI,CAAC+G,QAAL,GAAgB,IAAhB;;EACA,UAAA,MAAI,CAACvC,YAAL,CAAkB,OAAlB;EACH,SAHD;EAIH,OALY,CAAb;EAMH;EACD;EACJ;EACA;EACA;EACA;;EA5JA;EAAA;EAAA,WA6JI,eAAM;EACF,UAAIwC,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;EACA,UAAMuD,MAAM,GAAG,KAAK9E,IAAL,CAAUoE,MAAV,GAAmB,OAAnB,GAA6B,MAA5C;EACA,UAAIJ,IAAI,GAAG,EAAX,CAHE;;EAKF,UAAI,UAAU,KAAKhE,IAAL,CAAU+E,iBAAxB,EAA2C;EACvCxD,QAAAA,KAAK,CAAC,KAAKvB,IAAL,CAAUgF,cAAX,CAAL,GAAkCxC,KAAK,EAAvC;EACH;;EACD,UAAI,CAAC,KAAKrH,cAAN,IAAwB,CAACoG,KAAK,CAAC0D,GAAnC,EAAwC;EACpC1D,QAAAA,KAAK,CAAC2D,GAAN,GAAY,CAAZ;EACH,OAVC;;;EAYF,UAAI,KAAKlF,IAAL,CAAUgE,IAAV,KACE,YAAYc,MAAZ,IAAsBK,MAAM,CAAC,KAAKnF,IAAL,CAAUgE,IAAX,CAAN,KAA2B,GAAlD,IACI,WAAWc,MAAX,IAAqBK,MAAM,CAAC,KAAKnF,IAAL,CAAUgE,IAAX,CAAN,KAA2B,EAFrD,CAAJ,EAE+D;EAC3DA,QAAAA,IAAI,GAAG,MAAM,KAAKhE,IAAL,CAAUgE,IAAvB;EACH;;EACD,UAAMoB,YAAY,GAAGhD,MAAM,CAACb,KAAD,CAA3B;EACA,UAAM8D,IAAI,GAAG,KAAKrF,IAAL,CAAUkE,QAAV,CAAmBoB,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;EACA,aAAQR,MAAM,GACV,KADI,IAEHO,IAAI,GAAG,MAAM,KAAKrF,IAAL,CAAUkE,QAAhB,GAA2B,GAA9B,GAAoC,KAAKlE,IAAL,CAAUkE,QAF/C,IAGJF,IAHI,GAIJ,KAAKhE,IAAL,CAAUuF,IAJN,IAKHH,YAAY,CAAC5I,MAAb,GAAsB,MAAM4I,YAA5B,GAA2C,EALxC,CAAR;EAMH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA5LA;EAAA;EAAA,WA6LI,mBAAmB;EAAA,UAAXpF,IAAW,uEAAJ,EAAI;;EACf,eAAcA,IAAd,EAAoB;EAAEiE,QAAAA,EAAE,EAAE,KAAKA,EAAX;EAAeE,QAAAA,EAAE,EAAE,KAAKA;EAAxB,OAApB,EAAkD,KAAKnE,IAAvD;;EACA,aAAO,IAAIwF,OAAJ,CAAY,KAAKC,GAAL,EAAZ,EAAwBzF,IAAxB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAvMA;EAAA;EAAA,WAwMI,iBAAQzF,IAAR,EAAcwD,EAAd,EAAkB;EAAA;;EACd,UAAM2H,GAAG,GAAG,KAAKC,OAAL,CAAa;EACrBC,QAAAA,MAAM,EAAE,MADa;EAErBrL,QAAAA,IAAI,EAAEA;EAFe,OAAb,CAAZ;EAIAmL,MAAAA,GAAG,CAAC9H,EAAJ,CAAO,SAAP,EAAkBG,EAAlB;EACA2H,MAAAA,GAAG,CAAC9H,EAAJ,CAAO,OAAP,EAAgB,UAACiI,SAAD,EAAY1E,OAAZ,EAAwB;EACpC,QAAA,MAAI,CAAC2E,OAAL,CAAa,gBAAb,EAA+BD,SAA/B,EAA0C1E,OAA1C;EACH,OAFD;EAGH;EACD;EACJ;EACA;EACA;EACA;;EAtNA;EAAA;EAAA,WAuNI,kBAAS;EAAA;;EACL,UAAMuE,GAAG,GAAG,KAAKC,OAAL,EAAZ;EACAD,MAAAA,GAAG,CAAC9H,EAAJ,CAAO,MAAP,EAAe,KAAKmI,MAAL,CAAY5F,IAAZ,CAAiB,IAAjB,CAAf;EACAuF,MAAAA,GAAG,CAAC9H,EAAJ,CAAO,OAAP,EAAgB,UAACiI,SAAD,EAAY1E,OAAZ,EAAwB;EACpC,QAAA,MAAI,CAAC2E,OAAL,CAAa,gBAAb,EAA+BD,SAA/B,EAA0C1E,OAA1C;EACH,OAFD;EAGA,WAAK6E,OAAL,GAAeN,GAAf;EACH;EA9NL;;EAAA;EAAA,EAA6BrE,SAA7B;MAgOamE,OAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,mBAAYC,GAAZ,EAAiBzF,IAAjB,EAAuB;EAAA;;EAAA;;EACnB;EACAD,IAAAA,qBAAqB,iCAAOC,IAAP,CAArB;EACA,WAAKA,IAAL,GAAYA,IAAZ;EACA,WAAK4F,MAAL,GAAc5F,IAAI,CAAC4F,MAAL,IAAe,KAA7B;EACA,WAAKH,GAAL,GAAWA,GAAX;EACA,WAAKQ,KAAL,GAAa,UAAUjG,IAAI,CAACiG,KAA5B;EACA,WAAK1L,IAAL,GAAY2L,SAAS,KAAKlG,IAAI,CAACzF,IAAnB,GAA0ByF,IAAI,CAACzF,IAA/B,GAAsC,IAAlD;;EACA,WAAKP,MAAL;;EARmB;EAStB;EACD;EACJ;EACA;EACA;EACA;;;EArBA;EAAA;EAAA,WAsBI,kBAAS;EAAA;;EACL,UAAMgG,IAAI,GAAGX,IAAI,CAAC,KAAKW,IAAN,EAAY,OAAZ,EAAqB,KAArB,EAA4B,KAA5B,EAAmC,YAAnC,EAAiD,MAAjD,EAAyD,IAAzD,EAA+D,SAA/D,EAA0E,oBAA1E,EAAgG,WAAhG,CAAjB;EACAA,MAAAA,IAAI,CAACqD,OAAL,GAAe,CAAC,CAAC,KAAKrD,IAAL,CAAUiE,EAA3B;EACAjE,MAAAA,IAAI,CAACmG,OAAL,GAAe,CAAC,CAAC,KAAKnG,IAAL,CAAUmE,EAA3B;EACA,UAAMT,GAAG,GAAI,KAAKA,GAAL,GAAW,IAAIR,gBAAJ,CAAmBlD,IAAnB,CAAxB;;EACA,UAAI;EACA0D,QAAAA,GAAG,CAAC0C,IAAJ,CAAS,KAAKR,MAAd,EAAsB,KAAKH,GAA3B,EAAgC,KAAKQ,KAArC;;EACA,YAAI;EACA,cAAI,KAAKjG,IAAL,CAAUqG,YAAd,EAA4B;EACxB3C,YAAAA,GAAG,CAAC4C,qBAAJ,IAA6B5C,GAAG,CAAC4C,qBAAJ,CAA0B,IAA1B,CAA7B;;EACA,iBAAK,IAAIxK,CAAT,IAAc,KAAKkE,IAAL,CAAUqG,YAAxB,EAAsC;EAClC,kBAAI,KAAKrG,IAAL,CAAUqG,YAAV,CAAuB3G,cAAvB,CAAsC5D,CAAtC,CAAJ,EAA8C;EAC1C4H,gBAAAA,GAAG,CAAC6C,gBAAJ,CAAqBzK,CAArB,EAAwB,KAAKkE,IAAL,CAAUqG,YAAV,CAAuBvK,CAAvB,CAAxB;EACH;EACJ;EACJ;EACJ,SATD,CAUA,OAAOwH,CAAP,EAAU;;EACV,YAAI,WAAW,KAAKsC,MAApB,EAA4B;EACxB,cAAI;EACAlC,YAAAA,GAAG,CAAC6C,gBAAJ,CAAqB,cAArB,EAAqC,0BAArC;EACH,WAFD,CAGA,OAAOjD,CAAP,EAAU;EACb;;EACD,YAAI;EACAI,UAAAA,GAAG,CAAC6C,gBAAJ,CAAqB,QAArB,EAA+B,KAA/B;EACH,SAFD,CAGA,OAAOjD,CAAP,EAAU,EAtBV;;;EAwBA,YAAI,qBAAqBI,GAAzB,EAA8B;EAC1BA,UAAAA,GAAG,CAAC8C,eAAJ,GAAsB,KAAKxG,IAAL,CAAUwG,eAAhC;EACH;;EACD,YAAI,KAAKxG,IAAL,CAAUyG,cAAd,EAA8B;EAC1B/C,UAAAA,GAAG,CAACgD,OAAJ,GAAc,KAAK1G,IAAL,CAAUyG,cAAxB;EACH;;EACD/C,QAAAA,GAAG,CAACiD,kBAAJ,GAAyB,YAAM;EAC3B,cAAI,MAAMjD,GAAG,CAAClC,UAAd,EACI;;EACJ,cAAI,QAAQkC,GAAG,CAACkD,MAAZ,IAAsB,SAASlD,GAAG,CAACkD,MAAvC,EAA+C;EAC3C,YAAA,MAAI,CAACC,MAAL;EACH,WAFD,MAGK;EACD;EACA;EACA,YAAA,MAAI,CAAC3G,YAAL,CAAkB,YAAM;EACpB,cAAA,MAAI,CAAC4F,OAAL,CAAa,OAAOpC,GAAG,CAACkD,MAAX,KAAsB,QAAtB,GAAiClD,GAAG,CAACkD,MAArC,GAA8C,CAA3D;EACH,aAFD,EAEG,CAFH;EAGH;EACJ,SAbD;;EAcAlD,QAAAA,GAAG,CAACoD,IAAJ,CAAS,KAAKvM,IAAd;EACH,OA7CD,CA8CA,OAAO+I,CAAP,EAAU;EACN;EACA;EACA;EACA,aAAKpD,YAAL,CAAkB,YAAM;EACpB,UAAA,MAAI,CAAC4F,OAAL,CAAaxC,CAAb;EACH,SAFD,EAEG,CAFH;EAGA;EACH;;EACD,UAAI,OAAOyD,QAAP,KAAoB,WAAxB,EAAqC;EACjC,aAAKC,KAAL,GAAaxB,OAAO,CAACyB,aAAR,EAAb;EACAzB,QAAAA,OAAO,CAAC0B,QAAR,CAAiB,KAAKF,KAAtB,IAA+B,IAA/B;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA3FA;EAAA;EAAA,WA4FI,iBAAQ7D,GAAR,EAAa;EACT,WAAKpE,YAAL,CAAkB,OAAlB,EAA2BoE,GAA3B,EAAgC,KAAKO,GAArC;EACA,WAAKyD,OAAL,CAAa,IAAb;EACH;EACD;EACJ;EACA;EACA;EACA;;EApGA;EAAA;EAAA,WAqGI,iBAAQC,SAAR,EAAmB;EACf,UAAI,gBAAgB,OAAO,KAAK1D,GAA5B,IAAmC,SAAS,KAAKA,GAArD,EAA0D;EACtD;EACH;;EACD,WAAKA,GAAL,CAASiD,kBAAT,GAA8BnD,KAA9B;;EACA,UAAI4D,SAAJ,EAAe;EACX,YAAI;EACA,eAAK1D,GAAL,CAAS2D,KAAT;EACH,SAFD,CAGA,OAAO/D,CAAP,EAAU;EACb;;EACD,UAAI,OAAOyD,QAAP,KAAoB,WAAxB,EAAqC;EACjC,eAAOvB,OAAO,CAAC0B,QAAR,CAAiB,KAAKF,KAAtB,CAAP;EACH;;EACD,WAAKtD,GAAL,GAAW,IAAX;EACH;EACD;EACJ;EACA;EACA;EACA;;EAzHA;EAAA;EAAA,WA0HI,kBAAS;EACL,UAAMnJ,IAAI,GAAG,KAAKmJ,GAAL,CAAS4D,YAAtB;;EACA,UAAI/M,IAAI,KAAK,IAAb,EAAmB;EACf,aAAKwE,YAAL,CAAkB,MAAlB,EAA0BxE,IAA1B;EACA,aAAKwE,YAAL,CAAkB,SAAlB;EACA,aAAKoI,OAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAtIA;EAAA;EAAA,WAuII,iBAAQ;EACJ,WAAKA,OAAL;EACH;EAzIL;;EAAA;EAAA,EAA6BzJ,OAA7B;EA2IA8H,OAAO,CAACyB,aAAR,GAAwB,CAAxB;EACAzB,OAAO,CAAC0B,QAAR,GAAmB,EAAnB;EACA;EACA;EACA;EACA;EACA;;EACA,IAAI,OAAOH,QAAP,KAAoB,WAAxB,EAAqC;EACjC;EACA,MAAI,OAAOQ,WAAP,KAAuB,UAA3B,EAAuC;EACnC;EACAA,IAAAA,WAAW,CAAC,UAAD,EAAaC,aAAb,CAAX;EACH,GAHD,MAIK,IAAI,OAAO3J,gBAAP,KAA4B,UAAhC,EAA4C;EAC7C,QAAM4J,gBAAgB,GAAG,gBAAgBrH,UAAhB,GAA6B,UAA7B,GAA0C,QAAnE;EACAvC,IAAAA,gBAAgB,CAAC4J,gBAAD,EAAmBD,aAAnB,EAAkC,KAAlC,CAAhB;EACH;EACJ;;EACD,SAASA,aAAT,GAAyB;EACrB,OAAK,IAAI1L,CAAT,IAAc0J,OAAO,CAAC0B,QAAtB,EAAgC;EAC5B,QAAI1B,OAAO,CAAC0B,QAAR,CAAiBxH,cAAjB,CAAgC5D,CAAhC,CAAJ,EAAwC;EACpC0J,MAAAA,OAAO,CAAC0B,QAAR,CAAiBpL,CAAjB,EAAoBuL,KAApB;EACH;EACJ;EACJ;;ECjZM,IAAMK,QAAQ,GAAI,YAAM;EAC3B,MAAMC,kBAAkB,GAAG,OAAOC,OAAP,KAAmB,UAAnB,IAAiC,OAAOA,OAAO,CAACC,OAAf,KAA2B,UAAvF;;EACA,MAAIF,kBAAJ,EAAwB;EACpB,WAAO,UAAAlJ,EAAE;EAAA,aAAImJ,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuBrJ,EAAvB,CAAJ;EAAA,KAAT;EACH,GAFD,MAGK;EACD,WAAO,UAACA,EAAD,EAAKyB,YAAL;EAAA,aAAsBA,YAAY,CAACzB,EAAD,EAAK,CAAL,CAAlC;EAAA,KAAP;EACH;EACJ,CARuB,EAAjB;EASA,IAAMsJ,SAAS,GAAG3H,UAAU,CAAC2H,SAAX,IAAwB3H,UAAU,CAAC4H,YAArD;EACA,IAAMC,qBAAqB,GAAG,IAA9B;EACA,IAAMC,iBAAiB,GAAG,aAA1B;;ECLP,IAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAClB,OAAOA,SAAS,CAACC,OAAjB,KAA6B,QADX,IAElBD,SAAS,CAACC,OAAV,CAAkBC,WAAlB,OAAoC,aAFxC;MAGaC,EAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,cAAYvI,IAAZ,EAAkB;EAAA;;EAAA;;EACd,8BAAMA,IAAN;EACA,UAAK7E,cAAL,GAAsB,CAAC6E,IAAI,CAACqE,WAA5B;EAFc;EAGjB;EACD;EACJ;EACA;EACA;EACA;;;EAfA;EAAA;EAAA,SAgBI,eAAW;EACP,aAAO,WAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAvBA;EAAA;EAAA,WAwBI,kBAAS;EACL,UAAI,CAAC,KAAKmE,KAAL,EAAL,EAAmB;EACf;EACA;EACH;;EACD,UAAM/C,GAAG,GAAG,KAAKA,GAAL,EAAZ;EACA,UAAMgD,SAAS,GAAG,KAAKzI,IAAL,CAAUyI,SAA5B,CANK;;EAQL,UAAMzI,IAAI,GAAGmI,aAAa,GACpB,EADoB,GAEpB9I,IAAI,CAAC,KAAKW,IAAN,EAAY,OAAZ,EAAqB,mBAArB,EAA0C,KAA1C,EAAiD,KAAjD,EAAwD,YAAxD,EAAsE,MAAtE,EAA8E,IAA9E,EAAoF,SAApF,EAA+F,oBAA/F,EAAqH,cAArH,EAAqI,iBAArI,EAAwJ,QAAxJ,EAAkK,YAAlK,EAAgL,QAAhL,EAA0L,qBAA1L,CAFV;;EAGA,UAAI,KAAKA,IAAL,CAAUqG,YAAd,EAA4B;EACxBrG,QAAAA,IAAI,CAAC0I,OAAL,GAAe,KAAK1I,IAAL,CAAUqG,YAAzB;EACH;;EACD,UAAI;EACA,aAAKsC,EAAL,GACIV,qBAAqB,IAAI,CAACE,aAA1B,GACMM,SAAS,GACL,IAAIV,SAAJ,CAActC,GAAd,EAAmBgD,SAAnB,CADK,GAEL,IAAIV,SAAJ,CAActC,GAAd,CAHV,GAIM,IAAIsC,SAAJ,CAActC,GAAd,EAAmBgD,SAAnB,EAA8BzI,IAA9B,CALV;EAMH,OAPD,CAQA,OAAOmD,GAAP,EAAY;EACR,eAAO,KAAKpE,YAAL,CAAkB,OAAlB,EAA2BoE,GAA3B,CAAP;EACH;;EACD,WAAKwF,EAAL,CAAQzM,UAAR,GAAqB,KAAKuF,MAAL,CAAYvF,UAAZ,IAA0BgM,iBAA/C;EACA,WAAKU,iBAAL;EACH;EACD;EACJ;EACA;EACA;EACA;;EAxDA;EAAA;EAAA,WAyDI,6BAAoB;EAAA;;EAChB,WAAKD,EAAL,CAAQE,MAAR,GAAiB,YAAM;EACnB,YAAI,MAAI,CAAC7I,IAAL,CAAU8I,SAAd,EAAyB;EACrB,UAAA,MAAI,CAACH,EAAL,CAAQI,OAAR,CAAgBC,KAAhB;EACH;;EACD,QAAA,MAAI,CAACrE,MAAL;EACH,OALD;;EAMA,WAAKgE,EAAL,CAAQM,OAAR,GAAkB,UAAAC,UAAU;EAAA,eAAI,MAAI,CAACtH,OAAL,CAAa;EACzCV,UAAAA,WAAW,EAAE,6BAD4B;EAEzCC,UAAAA,OAAO,EAAE+H;EAFgC,SAAb,CAAJ;EAAA,OAA5B;;EAIA,WAAKP,EAAL,CAAQQ,SAAR,GAAoB,UAAAC,EAAE;EAAA,eAAI,MAAI,CAACrD,MAAL,CAAYqD,EAAE,CAAC7O,IAAf,CAAJ;EAAA,OAAtB;;EACA,WAAKoO,EAAL,CAAQU,OAAR,GAAkB,UAAA/F,CAAC;EAAA,eAAI,MAAI,CAACwC,OAAL,CAAa,iBAAb,EAAgCxC,CAAhC,CAAJ;EAAA,OAAnB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA5EA;EAAA;EAAA,WA6EI,eAAMvG,OAAN,EAAe;EAAA;;EACX,WAAKuE,QAAL,GAAgB,KAAhB,CADW;EAGX;;EAHW,iCAIFxF,CAJE;EAKP,YAAMqB,MAAM,GAAGJ,OAAO,CAACjB,CAAD,CAAtB;EACA,YAAMwN,UAAU,GAAGxN,CAAC,KAAKiB,OAAO,CAACP,MAAR,GAAiB,CAA1C;EACAtB,QAAAA,YAAY,CAACiC,MAAD,EAAS,MAAI,CAAChC,cAAd,EAA8B,UAAAZ,IAAI,EAAI;EAC9C;EACA,cAAMyF,IAAI,GAAG,EAAb;EAeA;EACA;;;EACA,cAAI;EACA,gBAAIiI,qBAAJ,EAA2B;EACvB;EACA,cAAA,MAAI,CAACU,EAAL,CAAQ7B,IAAR,CAAavM,IAAb;EACH;EAIJ,WARD,CASA,OAAO+I,CAAP,EAAU;;EAEV,cAAIgG,UAAJ,EAAgB;EACZ;EACA;EACA5B,YAAAA,QAAQ,CAAC,YAAM;EACX,cAAA,MAAI,CAACpG,QAAL,GAAgB,IAAhB;;EACA,cAAA,MAAI,CAACvC,YAAL,CAAkB,OAAlB;EACH,aAHO,EAGL,MAAI,CAACmB,YAHA,CAAR;EAIH;EACJ,SAtCW,CAAZ;EAPO;;EAIX,WAAK,IAAIpE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,OAAO,CAACP,MAA5B,EAAoCV,CAAC,EAArC,EAAyC;EAAA,cAAhCA,CAAgC;EA0CxC;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAjIA;EAAA;EAAA,WAkII,mBAAU;EACN,UAAI,OAAO,KAAK6M,EAAZ,KAAmB,WAAvB,EAAoC;EAChC,aAAKA,EAAL,CAAQ/D,KAAR;EACA,aAAK+D,EAAL,GAAU,IAAV;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA5IA;EAAA;EAAA,WA6II,eAAM;EACF,UAAIpH,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;EACA,UAAMuD,MAAM,GAAG,KAAK9E,IAAL,CAAUoE,MAAV,GAAmB,KAAnB,GAA2B,IAA1C;EACA,UAAIJ,IAAI,GAAG,EAAX,CAHE;;EAKF,UAAI,KAAKhE,IAAL,CAAUgE,IAAV,KACE,UAAUc,MAAV,IAAoBK,MAAM,CAAC,KAAKnF,IAAL,CAAUgE,IAAX,CAAN,KAA2B,GAAhD,IACI,SAASc,MAAT,IAAmBK,MAAM,CAAC,KAAKnF,IAAL,CAAUgE,IAAX,CAAN,KAA2B,EAFnD,CAAJ,EAE6D;EACzDA,QAAAA,IAAI,GAAG,MAAM,KAAKhE,IAAL,CAAUgE,IAAvB;EACH,OATC;;;EAWF,UAAI,KAAKhE,IAAL,CAAU+E,iBAAd,EAAiC;EAC7BxD,QAAAA,KAAK,CAAC,KAAKvB,IAAL,CAAUgF,cAAX,CAAL,GAAkCxC,KAAK,EAAvC;EACH,OAbC;;;EAeF,UAAI,CAAC,KAAKrH,cAAV,EAA0B;EACtBoG,QAAAA,KAAK,CAAC2D,GAAN,GAAY,CAAZ;EACH;;EACD,UAAME,YAAY,GAAGhD,MAAM,CAACb,KAAD,CAA3B;EACA,UAAM8D,IAAI,GAAG,KAAKrF,IAAL,CAAUkE,QAAV,CAAmBoB,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;EACA,aAAQR,MAAM,GACV,KADI,IAEHO,IAAI,GAAG,MAAM,KAAKrF,IAAL,CAAUkE,QAAhB,GAA2B,GAA9B,GAAoC,KAAKlE,IAAL,CAAUkE,QAF/C,IAGJF,IAHI,GAIJ,KAAKhE,IAAL,CAAUuF,IAJN,IAKHH,YAAY,CAAC5I,MAAb,GAAsB,MAAM4I,YAA5B,GAA2C,EALxC,CAAR;EAMH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA7KA;EAAA;EAAA,WA8KI,iBAAQ;EACJ,aAAQ,CAAC,CAAC2C,SAAF,IACJ,EAAE,kBAAkBA,SAAlB,IAA+B,KAAKwB,IAAL,KAAchB,EAAE,CAAC7N,SAAH,CAAa6O,IAA5D,CADJ;EAEH;EAjLL;;EAAA;EAAA,EAAwBlI,SAAxB;;ECRO,IAAMmI,UAAU,GAAG;EACtBC,EAAAA,SAAS,EAAElB,EADW;EAEtB1E,EAAAA,OAAO,EAAED;EAFa,CAAnB;;ECFP;;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAM8F,EAAE,GAAG,yOAAX;EACA,IAAMC,KAAK,GAAG,CACV,QADU,EACA,UADA,EACY,WADZ,EACyB,UADzB,EACqC,MADrC,EAC6C,UAD7C,EACyD,MADzD,EACiE,MADjE,EACyE,UADzE,EACqF,MADrF,EAC6F,WAD7F,EAC0G,MAD1G,EACkH,OADlH,EAC2H,QAD3H,CAAd;EAGO,SAASC,KAAT,CAAehJ,GAAf,EAAoB;EACvB,MAAMiJ,GAAG,GAAGjJ,GAAZ;EAAA,MAAiBkJ,CAAC,GAAGlJ,GAAG,CAAC0E,OAAJ,CAAY,GAAZ,CAArB;EAAA,MAAuChC,CAAC,GAAG1C,GAAG,CAAC0E,OAAJ,CAAY,GAAZ,CAA3C;;EACA,MAAIwE,CAAC,IAAI,CAAC,CAAN,IAAWxG,CAAC,IAAI,CAAC,CAArB,EAAwB;EACpB1C,IAAAA,GAAG,GAAGA,GAAG,CAACtE,SAAJ,CAAc,CAAd,EAAiBwN,CAAjB,IAAsBlJ,GAAG,CAACtE,SAAJ,CAAcwN,CAAd,EAAiBxG,CAAjB,EAAoByG,OAApB,CAA4B,IAA5B,EAAkC,GAAlC,CAAtB,GAA+DnJ,GAAG,CAACtE,SAAJ,CAAcgH,CAAd,EAAiB1C,GAAG,CAACpE,MAArB,CAArE;EACH;;EACD,MAAIwN,CAAC,GAAGN,EAAE,CAACO,IAAH,CAAQrJ,GAAG,IAAI,EAAf,CAAR;EAAA,MAA4B6E,GAAG,GAAG,EAAlC;EAAA,MAAsC3J,CAAC,GAAG,EAA1C;;EACA,SAAOA,CAAC,EAAR,EAAY;EACR2J,IAAAA,GAAG,CAACkE,KAAK,CAAC7N,CAAD,CAAN,CAAH,GAAgBkO,CAAC,CAAClO,CAAD,CAAD,IAAQ,EAAxB;EACH;;EACD,MAAIgO,CAAC,IAAI,CAAC,CAAN,IAAWxG,CAAC,IAAI,CAAC,CAArB,EAAwB;EACpBmC,IAAAA,GAAG,CAACyE,MAAJ,GAAaL,GAAb;EACApE,IAAAA,GAAG,CAAC0E,IAAJ,GAAW1E,GAAG,CAAC0E,IAAJ,CAAS7N,SAAT,CAAmB,CAAnB,EAAsBmJ,GAAG,CAAC0E,IAAJ,CAAS3N,MAAT,GAAkB,CAAxC,EAA2CuN,OAA3C,CAAmD,IAAnD,EAAyD,GAAzD,CAAX;EACAtE,IAAAA,GAAG,CAAC2E,SAAJ,GAAgB3E,GAAG,CAAC2E,SAAJ,CAAcL,OAAd,CAAsB,GAAtB,EAA2B,EAA3B,EAA+BA,OAA/B,CAAuC,GAAvC,EAA4C,EAA5C,EAAgDA,OAAhD,CAAwD,IAAxD,EAA8D,GAA9D,CAAhB;EACAtE,IAAAA,GAAG,CAAC4E,OAAJ,GAAc,IAAd;EACH;;EACD5E,EAAAA,GAAG,CAAC6E,SAAJ,GAAgBA,SAAS,CAAC7E,GAAD,EAAMA,GAAG,CAAC,MAAD,CAAT,CAAzB;EACAA,EAAAA,GAAG,CAAC8E,QAAJ,GAAeA,QAAQ,CAAC9E,GAAD,EAAMA,GAAG,CAAC,OAAD,CAAT,CAAvB;EACA,SAAOA,GAAP;EACH;;EACD,SAAS6E,SAAT,CAAmBtP,GAAnB,EAAwBuK,IAAxB,EAA8B;EAC1B,MAAMiF,IAAI,GAAG,UAAb;EAAA,MAAyBC,KAAK,GAAGlF,IAAI,CAACwE,OAAL,CAAaS,IAAb,EAAmB,GAAnB,EAAwB7O,KAAxB,CAA8B,GAA9B,CAAjC;;EACA,MAAI4J,IAAI,CAACmF,MAAL,CAAY,CAAZ,EAAe,CAAf,KAAqB,GAArB,IAA4BnF,IAAI,CAAC/I,MAAL,KAAgB,CAAhD,EAAmD;EAC/CiO,IAAAA,KAAK,CAAC/L,MAAN,CAAa,CAAb,EAAgB,CAAhB;EACH;;EACD,MAAI6G,IAAI,CAACmF,MAAL,CAAYnF,IAAI,CAAC/I,MAAL,GAAc,CAA1B,EAA6B,CAA7B,KAAmC,GAAvC,EAA4C;EACxCiO,IAAAA,KAAK,CAAC/L,MAAN,CAAa+L,KAAK,CAACjO,MAAN,GAAe,CAA5B,EAA+B,CAA/B;EACH;;EACD,SAAOiO,KAAP;EACH;;EACD,SAASF,QAAT,CAAkB9E,GAAlB,EAAuBlE,KAAvB,EAA8B;EAC1B,MAAMhH,IAAI,GAAG,EAAb;EACAgH,EAAAA,KAAK,CAACwI,OAAN,CAAc,2BAAd,EAA2C,UAAUY,EAAV,EAAcC,EAAd,EAAkBC,EAAlB,EAAsB;EAC7D,QAAID,EAAJ,EAAQ;EACJrQ,MAAAA,IAAI,CAACqQ,EAAD,CAAJ,GAAWC,EAAX;EACH;EACJ,GAJD;EAKA,SAAOtQ,IAAP;EACH;;MC1CYuQ,QAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACA;EACI,kBAAYrF,GAAZ,EAA4B;EAAA;;EAAA,QAAXzF,IAAW,uEAAJ,EAAI;;EAAA;;EACxB;;EACA,QAAIyF,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;EAChCzF,MAAAA,IAAI,GAAGyF,GAAP;EACAA,MAAAA,GAAG,GAAG,IAAN;EACH;;EACD,QAAIA,GAAJ,EAAS;EACLA,MAAAA,GAAG,GAAGmE,KAAK,CAACnE,GAAD,CAAX;EACAzF,MAAAA,IAAI,CAACkE,QAAL,GAAgBuB,GAAG,CAAC0E,IAApB;EACAnK,MAAAA,IAAI,CAACoE,MAAL,GAAcqB,GAAG,CAAChI,QAAJ,KAAiB,OAAjB,IAA4BgI,GAAG,CAAChI,QAAJ,KAAiB,KAA3D;EACAuC,MAAAA,IAAI,CAACgE,IAAL,GAAYyB,GAAG,CAACzB,IAAhB;EACA,UAAIyB,GAAG,CAAClE,KAAR,EACIvB,IAAI,CAACuB,KAAL,GAAakE,GAAG,CAAClE,KAAjB;EACP,KAPD,MAQK,IAAIvB,IAAI,CAACmK,IAAT,EAAe;EAChBnK,MAAAA,IAAI,CAACkE,QAAL,GAAgB0F,KAAK,CAAC5J,IAAI,CAACmK,IAAN,CAAL,CAAiBA,IAAjC;EACH;;EACDpK,IAAAA,qBAAqB,gCAAOC,IAAP,CAArB;EACA,UAAKoE,MAAL,GACI,QAAQpE,IAAI,CAACoE,MAAb,GACMpE,IAAI,CAACoE,MADX,GAEM,OAAON,QAAP,KAAoB,WAApB,IAAmC,aAAaA,QAAQ,CAACrG,QAHnE;;EAIA,QAAIuC,IAAI,CAACkE,QAAL,IAAiB,CAAClE,IAAI,CAACgE,IAA3B,EAAiC;EAC7B;EACAhE,MAAAA,IAAI,CAACgE,IAAL,GAAY,MAAKI,MAAL,GAAc,KAAd,GAAsB,IAAlC;EACH;;EACD,UAAKF,QAAL,GACIlE,IAAI,CAACkE,QAAL,KACK,OAAOJ,QAAP,KAAoB,WAApB,GAAkCA,QAAQ,CAACI,QAA3C,GAAsD,WAD3D,CADJ;EAGA,UAAKF,IAAL,GACIhE,IAAI,CAACgE,IAAL,KACK,OAAOF,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACE,IAA5C,GACKF,QAAQ,CAACE,IADd,GAEK,MAAKI,MAAL,GACI,KADJ,GAEI,IALd,CADJ;EAOA,UAAKoF,UAAL,GAAkBxJ,IAAI,CAACwJ,UAAL,IAAmB,CAAC,SAAD,EAAY,WAAZ,CAArC;EACA,UAAKhI,UAAL,GAAkB,EAAlB;EACA,UAAKuJ,WAAL,GAAmB,EAAnB;EACA,UAAKC,aAAL,GAAqB,CAArB;EACA,UAAKhL,IAAL,GAAY,SAAc;EACtBuF,MAAAA,IAAI,EAAE,YADgB;EAEtB0F,MAAAA,KAAK,EAAE,KAFe;EAGtBzE,MAAAA,eAAe,EAAE,KAHK;EAItB0E,MAAAA,OAAO,EAAE,IAJa;EAKtBlG,MAAAA,cAAc,EAAE,GALM;EAMtBmG,MAAAA,eAAe,EAAE,KANK;EAOtBC,MAAAA,kBAAkB,EAAE,IAPE;EAQtBC,MAAAA,iBAAiB,EAAE;EACfC,QAAAA,SAAS,EAAE;EADI,OARG;EAWtBC,MAAAA,gBAAgB,EAAE,EAXI;EAYtBC,MAAAA,mBAAmB,EAAE;EAZC,KAAd,EAaTxL,IAbS,CAAZ;EAcA,UAAKA,IAAL,CAAUuF,IAAV,GAAiB,MAAKvF,IAAL,CAAUuF,IAAV,CAAewE,OAAf,CAAuB,KAAvB,EAA8B,EAA9B,IAAoC,GAArD;;EACA,QAAI,OAAO,MAAK/J,IAAL,CAAUuB,KAAjB,KAA2B,QAA/B,EAAyC;EACrC,YAAKvB,IAAL,CAAUuB,KAAV,GAAkBxF,MAAM,CAAC,MAAKiE,IAAL,CAAUuB,KAAX,CAAxB;EACH,KAzDuB;;;EA2DxB,UAAKkK,EAAL,GAAU,IAAV;EACA,UAAKC,QAAL,GAAgB,IAAhB;EACA,UAAKC,YAAL,GAAoB,IAApB;EACA,UAAKC,WAAL,GAAmB,IAAnB,CA9DwB;;EAgExB,UAAKC,gBAAL,GAAwB,IAAxB;;EACA,QAAI,OAAOhO,gBAAP,KAA4B,UAAhC,EAA4C;EACxC,UAAI,MAAKmC,IAAL,CAAUwL,mBAAd,EAAmC;EAC/B;EACA;EACA;EACA3N,QAAAA,gBAAgB,CAAC,cAAD,EAAiB,YAAM;EACnC,cAAI,MAAKiO,SAAT,EAAoB;EAChB;EACA,kBAAKA,SAAL,CAAexN,kBAAf;;EACA,kBAAKwN,SAAL,CAAelH,KAAf;EACH;EACJ,SANe,EAMb,KANa,CAAhB;EAOH;;EACD,UAAI,MAAKV,QAAL,KAAkB,WAAtB,EAAmC;EAC/B,cAAK6H,oBAAL,GAA4B,YAAM;EAC9B,gBAAKnK,OAAL,CAAa,iBAAb,EAAgC;EAC5BV,YAAAA,WAAW,EAAE;EADe,WAAhC;EAGH,SAJD;;EAKArD,QAAAA,gBAAgB,CAAC,SAAD,EAAY,MAAKkO,oBAAjB,EAAuC,KAAvC,CAAhB;EACH;EACJ;;EACD,UAAK3F,IAAL;;EAvFwB;EAwF3B;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EAvGA;EAAA;EAAA,WAwGI,yBAAgBmD,IAAhB,EAAsB;EAClB,UAAMhI,KAAK,GAAG,SAAc,EAAd,EAAkB,KAAKvB,IAAL,CAAUuB,KAA5B,CAAd,CADkB;;;EAGlBA,MAAAA,KAAK,CAACyK,GAAN,GAAYvO,UAAZ,CAHkB;;EAKlB8D,MAAAA,KAAK,CAACuK,SAAN,GAAkBvC,IAAlB,CALkB;;EAOlB,UAAI,KAAKkC,EAAT,EACIlK,KAAK,CAAC0D,GAAN,GAAY,KAAKwG,EAAjB;;EACJ,UAAMzL,IAAI,GAAG,SAAc,EAAd,EAAkB,KAAKA,IAAL,CAAUuL,gBAAV,CAA2BhC,IAA3B,CAAlB,EAAoD,KAAKvJ,IAAzD,EAA+D;EACxEuB,QAAAA,KAAK,EAALA,KADwE;EAExEE,QAAAA,MAAM,EAAE,IAFgE;EAGxEyC,QAAAA,QAAQ,EAAE,KAAKA,QAHyD;EAIxEE,QAAAA,MAAM,EAAE,KAAKA,MAJ2D;EAKxEJ,QAAAA,IAAI,EAAE,KAAKA;EAL6D,OAA/D,CAAb;;EAOA,aAAO,IAAIwF,UAAU,CAACD,IAAD,CAAd,CAAqBvJ,IAArB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EA9HA;EAAA;EAAA,WA+HI,gBAAO;EAAA;;EACH,UAAI8L,SAAJ;;EACA,UAAI,KAAK9L,IAAL,CAAUmL,eAAV,IACAL,MAAM,CAACmB,qBADP,IAEA,KAAKzC,UAAL,CAAgBlE,OAAhB,CAAwB,WAAxB,MAAyC,CAAC,CAF9C,EAEiD;EAC7CwG,QAAAA,SAAS,GAAG,WAAZ;EACH,OAJD,MAKK,IAAI,MAAM,KAAKtC,UAAL,CAAgBhN,MAA1B,EAAkC;EACnC;EACA,aAAK0D,YAAL,CAAkB,YAAM;EACpB,UAAA,MAAI,CAACnB,YAAL,CAAkB,OAAlB,EAA2B,yBAA3B;EACH,SAFD,EAEG,CAFH;EAGA;EACH,OANI,MAOA;EACD+M,QAAAA,SAAS,GAAG,KAAKtC,UAAL,CAAgB,CAAhB,CAAZ;EACH;;EACD,WAAKhI,UAAL,GAAkB,SAAlB,CAjBG;;EAmBH,UAAI;EACAsK,QAAAA,SAAS,GAAG,KAAKI,eAAL,CAAqBJ,SAArB,CAAZ;EACH,OAFD,CAGA,OAAOxI,CAAP,EAAU;EACN,aAAKkG,UAAL,CAAgB2C,KAAhB;EACA,aAAK/F,IAAL;EACA;EACH;;EACD0F,MAAAA,SAAS,CAAC1F,IAAV;EACA,WAAKgG,YAAL,CAAkBN,SAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAjKA;EAAA;EAAA,WAkKI,sBAAaA,SAAb,EAAwB;EAAA;;EACpB,UAAI,KAAKA,SAAT,EAAoB;EAChB,aAAKA,SAAL,CAAexN,kBAAf;EACH,OAHmB;;;EAKpB,WAAKwN,SAAL,GAAiBA,SAAjB,CALoB;;EAOpBA,MAAAA,SAAS,CACJlO,EADL,CACQ,OADR,EACiB,KAAKyO,OAAL,CAAalM,IAAb,CAAkB,IAAlB,CADjB,EAEKvC,EAFL,CAEQ,QAFR,EAEkB,KAAKkE,QAAL,CAAc3B,IAAd,CAAmB,IAAnB,CAFlB,EAGKvC,EAHL,CAGQ,OAHR,EAGiB,KAAKkI,OAAL,CAAa3F,IAAb,CAAkB,IAAlB,CAHjB,EAIKvC,EAJL,CAIQ,OAJR,EAIiB,UAAAqD,MAAM;EAAA,eAAI,MAAI,CAACW,OAAL,CAAa,iBAAb,EAAgCX,MAAhC,CAAJ;EAAA,OAJvB;EAKH;EACD;EACJ;EACA;EACA;EACA;EACA;;EApLA;EAAA;EAAA,WAqLI,eAAMsI,IAAN,EAAY;EAAA;;EACR,UAAIuC,SAAS,GAAG,KAAKI,eAAL,CAAqB3C,IAArB,CAAhB;EACA,UAAI+C,MAAM,GAAG,KAAb;EACAxB,MAAAA,MAAM,CAACmB,qBAAP,GAA+B,KAA/B;;EACA,UAAMM,eAAe,GAAG,SAAlBA,eAAkB,GAAM;EAC1B,YAAID,MAAJ,EACI;EACJR,QAAAA,SAAS,CAAChF,IAAV,CAAe,CAAC;EAAExM,UAAAA,IAAI,EAAE,MAAR;EAAgBC,UAAAA,IAAI,EAAE;EAAtB,SAAD,CAAf;EACAuR,QAAAA,SAAS,CAAC7N,IAAV,CAAe,QAAf,EAAyB,UAAAuO,GAAG,EAAI;EAC5B,cAAIF,MAAJ,EACI;;EACJ,cAAI,WAAWE,GAAG,CAAClS,IAAf,IAAuB,YAAYkS,GAAG,CAACjS,IAA3C,EAAiD;EAC7C,YAAA,MAAI,CAACkS,SAAL,GAAiB,IAAjB;;EACA,YAAA,MAAI,CAAC1N,YAAL,CAAkB,WAAlB,EAA+B+M,SAA/B;;EACA,gBAAI,CAACA,SAAL,EACI;EACJhB,YAAAA,MAAM,CAACmB,qBAAP,GAA+B,gBAAgBH,SAAS,CAACvC,IAAzD;;EACA,YAAA,MAAI,CAACuC,SAAL,CAAetH,KAAf,CAAqB,YAAM;EACvB,kBAAI8H,MAAJ,EACI;EACJ,kBAAI,aAAa,MAAI,CAAC9K,UAAtB,EACI;EACJ2F,cAAAA,OAAO;;EACP,cAAA,MAAI,CAACiF,YAAL,CAAkBN,SAAlB;;EACAA,cAAAA,SAAS,CAAChF,IAAV,CAAe,CAAC;EAAExM,gBAAAA,IAAI,EAAE;EAAR,eAAD,CAAf;;EACA,cAAA,MAAI,CAACyE,YAAL,CAAkB,SAAlB,EAA6B+M,SAA7B;;EACAA,cAAAA,SAAS,GAAG,IAAZ;EACA,cAAA,MAAI,CAACW,SAAL,GAAiB,KAAjB;;EACA,cAAA,MAAI,CAACC,KAAL;EACH,aAZD;EAaH,WAnBD,MAoBK;EACD,gBAAMvJ,GAAG,GAAG,IAAI/B,KAAJ,CAAU,aAAV,CAAZ,CADC;;EAGD+B,YAAAA,GAAG,CAAC2I,SAAJ,GAAgBA,SAAS,CAACvC,IAA1B;;EACA,YAAA,MAAI,CAACxK,YAAL,CAAkB,cAAlB,EAAkCoE,GAAlC;EACH;EACJ,SA7BD;EA8BH,OAlCD;;EAmCA,eAASwJ,eAAT,GAA2B;EACvB,YAAIL,MAAJ,EACI,OAFmB;;EAIvBA,QAAAA,MAAM,GAAG,IAAT;EACAnF,QAAAA,OAAO;EACP2E,QAAAA,SAAS,CAAClH,KAAV;EACAkH,QAAAA,SAAS,GAAG,IAAZ;EACH,OA/CO;;;EAiDR,UAAMzC,OAAO,GAAG,SAAVA,OAAU,CAAAlG,GAAG,EAAI;EACnB,YAAMyJ,KAAK,GAAG,IAAIxL,KAAJ,CAAU,kBAAkB+B,GAA5B,CAAd,CADmB;;EAGnByJ,QAAAA,KAAK,CAACd,SAAN,GAAkBA,SAAS,CAACvC,IAA5B;EACAoD,QAAAA,eAAe;;EACf,QAAA,MAAI,CAAC5N,YAAL,CAAkB,cAAlB,EAAkC6N,KAAlC;EACH,OAND;;EAOA,eAASC,gBAAT,GAA4B;EACxBxD,QAAAA,OAAO,CAAC,kBAAD,CAAP;EACH,OA1DO;;;EA4DR,eAASJ,OAAT,GAAmB;EACfI,QAAAA,OAAO,CAAC,eAAD,CAAP;EACH,OA9DO;;;EAgER,eAASyD,SAAT,CAAmBC,EAAnB,EAAuB;EACnB,YAAIjB,SAAS,IAAIiB,EAAE,CAACxD,IAAH,KAAYuC,SAAS,CAACvC,IAAvC,EAA6C;EACzCoD,UAAAA,eAAe;EAClB;EACJ,OApEO;;;EAsER,UAAMxF,OAAO,GAAG,SAAVA,OAAU,GAAM;EAClB2E,QAAAA,SAAS,CAACzN,cAAV,CAAyB,MAAzB,EAAiCkO,eAAjC;EACAT,QAAAA,SAAS,CAACzN,cAAV,CAAyB,OAAzB,EAAkCgL,OAAlC;EACAyC,QAAAA,SAAS,CAACzN,cAAV,CAAyB,OAAzB,EAAkCwO,gBAAlC;;EACA,QAAA,MAAI,CAAC3O,GAAL,CAAS,OAAT,EAAkB+K,OAAlB;;EACA,QAAA,MAAI,CAAC/K,GAAL,CAAS,WAAT,EAAsB4O,SAAtB;EACH,OAND;;EAOAhB,MAAAA,SAAS,CAAC7N,IAAV,CAAe,MAAf,EAAuBsO,eAAvB;EACAT,MAAAA,SAAS,CAAC7N,IAAV,CAAe,OAAf,EAAwBoL,OAAxB;EACAyC,MAAAA,SAAS,CAAC7N,IAAV,CAAe,OAAf,EAAwB4O,gBAAxB;EACA,WAAK5O,IAAL,CAAU,OAAV,EAAmBgL,OAAnB;EACA,WAAKhL,IAAL,CAAU,WAAV,EAAuB6O,SAAvB;EACAhB,MAAAA,SAAS,CAAC1F,IAAV;EACH;EACD;EACJ;EACA;EACA;EACA;;EA7QA;EAAA;EAAA,WA8QI,kBAAS;EACL,WAAK5E,UAAL,GAAkB,MAAlB;EACAsJ,MAAAA,MAAM,CAACmB,qBAAP,GAA+B,gBAAgB,KAAKH,SAAL,CAAevC,IAA9D;EACA,WAAKxK,YAAL,CAAkB,MAAlB;EACA,WAAK2N,KAAL,GAJK;EAML;;EACA,UAAI,WAAW,KAAKlL,UAAhB,IACA,KAAKxB,IAAL,CAAUkL,OADV,IAEA,KAAKY,SAAL,CAAetH,KAFnB,EAE0B;EACtB,YAAI1I,CAAC,GAAG,CAAR;EACA,YAAMgF,CAAC,GAAG,KAAK4K,QAAL,CAAclP,MAAxB;;EACA,eAAOV,CAAC,GAAGgF,CAAX,EAAchF,CAAC,EAAf,EAAmB;EACf,eAAKkR,KAAL,CAAW,KAAKtB,QAAL,CAAc5P,CAAd,CAAX;EACH;EACJ;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAnSA;EAAA;EAAA,WAoSI,kBAASqB,MAAT,EAAiB;EACb,UAAI,cAAc,KAAKqE,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAFvB,EAEmC;EAC/B,aAAKzC,YAAL,CAAkB,QAAlB,EAA4B5B,MAA5B,EAD+B;;EAG/B,aAAK4B,YAAL,CAAkB,WAAlB;;EACA,gBAAQ5B,MAAM,CAAC7C,IAAf;EACI,eAAK,MAAL;EACI,iBAAK2S,WAAL,CAAiBC,IAAI,CAACtD,KAAL,CAAWzM,MAAM,CAAC5C,IAAlB,CAAjB;EACA;;EACJ,eAAK,MAAL;EACI,iBAAK4S,gBAAL;EACA,iBAAKC,UAAL,CAAgB,MAAhB;EACA,iBAAKrO,YAAL,CAAkB,MAAlB;EACA,iBAAKA,YAAL,CAAkB,MAAlB;EACA;;EACJ,eAAK,OAAL;EACI,gBAAMoE,GAAG,GAAG,IAAI/B,KAAJ,CAAU,cAAV,CAAZ,CADJ;;EAGI+B,YAAAA,GAAG,CAACkK,IAAJ,GAAWlQ,MAAM,CAAC5C,IAAlB;EACA,iBAAKuL,OAAL,CAAa3C,GAAb;EACA;;EACJ,eAAK,SAAL;EACI,iBAAKpE,YAAL,CAAkB,MAAlB,EAA0B5B,MAAM,CAAC5C,IAAjC;EACA,iBAAKwE,YAAL,CAAkB,SAAlB,EAA6B5B,MAAM,CAAC5C,IAApC;EACA;EAnBR;EAqBH;EAGJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EAzUA;EAAA;EAAA,WA0UI,qBAAYA,IAAZ,EAAkB;EACd,WAAKwE,YAAL,CAAkB,WAAlB,EAA+BxE,IAA/B;EACA,WAAKkR,EAAL,GAAUlR,IAAI,CAAC0K,GAAf;EACA,WAAK6G,SAAL,CAAevK,KAAf,CAAqB0D,GAArB,GAA2B1K,IAAI,CAAC0K,GAAhC;EACA,WAAKyG,QAAL,GAAgB,KAAK4B,cAAL,CAAoB/S,IAAI,CAACmR,QAAzB,CAAhB;EACA,WAAKC,YAAL,GAAoBpR,IAAI,CAACoR,YAAzB;EACA,WAAKC,WAAL,GAAmBrR,IAAI,CAACqR,WAAxB;EACA,WAAK2B,UAAL,GAAkBhT,IAAI,CAACgT,UAAvB;EACA,WAAK5I,MAAL,GARc;;EAUd,UAAI,aAAa,KAAKnD,UAAtB,EACI;EACJ,WAAK2L,gBAAL;EACH;EACD;EACJ;EACA;EACA;EACA;;EA5VA;EAAA;EAAA,WA6VI,4BAAmB;EAAA;;EACf,WAAK9M,cAAL,CAAoB,KAAKwL,gBAAzB;EACA,WAAKA,gBAAL,GAAwB,KAAK3L,YAAL,CAAkB,YAAM;EAC5C,QAAA,MAAI,CAAC0B,OAAL,CAAa,cAAb;EACH,OAFuB,EAErB,KAAK+J,YAAL,GAAoB,KAAKC,WAFJ,CAAxB;;EAGA,UAAI,KAAK5L,IAAL,CAAU8I,SAAd,EAAyB;EACrB,aAAK+C,gBAAL,CAAsB7C,KAAtB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA1WA;EAAA;EAAA,WA2WI,mBAAU;EACN,WAAK+B,WAAL,CAAiBrM,MAAjB,CAAwB,CAAxB,EAA2B,KAAKsM,aAAhC,EADM;EAGN;EACA;;EACA,WAAKA,aAAL,GAAqB,CAArB;;EACA,UAAI,MAAM,KAAKD,WAAL,CAAiBvO,MAA3B,EAAmC;EAC/B,aAAKuC,YAAL,CAAkB,OAAlB;EACH,OAFD,MAGK;EACD,aAAK2N,KAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA5XA;EAAA;EAAA,WA6XI,iBAAQ;EACJ,UAAI,aAAa,KAAKlL,UAAlB,IACA,KAAKsK,SAAL,CAAexK,QADf,IAEA,CAAC,KAAKmL,SAFN,IAGA,KAAK1B,WAAL,CAAiBvO,MAHrB,EAG6B;EACzB,YAAMO,OAAO,GAAG,KAAKyQ,kBAAL,EAAhB;EACA,aAAK1B,SAAL,CAAehF,IAAf,CAAoB/J,OAApB,EAFyB;EAIzB;;EACA,aAAKiO,aAAL,GAAqBjO,OAAO,CAACP,MAA7B;EACA,aAAKuC,YAAL,CAAkB,OAAlB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EA/YA;EAAA;EAAA,WAgZI,8BAAqB;EACjB,UAAM0O,sBAAsB,GAAG,KAAKF,UAAL,IAC3B,KAAKzB,SAAL,CAAevC,IAAf,KAAwB,SADG,IAE3B,KAAKwB,WAAL,CAAiBvO,MAAjB,GAA0B,CAF9B;;EAGA,UAAI,CAACiR,sBAAL,EAA6B;EACzB,eAAO,KAAK1C,WAAZ;EACH;;EACD,UAAI2C,WAAW,GAAG,CAAlB,CAPiB;;EAQjB,WAAK,IAAI5R,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKiP,WAAL,CAAiBvO,MAArC,EAA6CV,CAAC,EAA9C,EAAkD;EAC9C,YAAMvB,IAAI,GAAG,KAAKwQ,WAAL,CAAiBjP,CAAjB,EAAoBvB,IAAjC;;EACA,YAAIA,IAAJ,EAAU;EACNmT,UAAAA,WAAW,IAAInN,UAAU,CAAChG,IAAD,CAAzB;EACH;;EACD,YAAIuB,CAAC,GAAG,CAAJ,IAAS4R,WAAW,GAAG,KAAKH,UAAhC,EAA4C;EACxC,iBAAO,KAAKxC,WAAL,CAAiBlM,KAAjB,CAAuB,CAAvB,EAA0B/C,CAA1B,CAAP;EACH;;EACD4R,QAAAA,WAAW,IAAI,CAAf,CAR8C;EASjD;;EACD,aAAO,KAAK3C,WAAZ;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA5aA;EAAA;EAAA,WA6aI,eAAMyB,GAAN,EAAWmB,OAAX,EAAoB5P,EAApB,EAAwB;EACpB,WAAKqP,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCmB,OAAhC,EAAyC5P,EAAzC;EACA,aAAO,IAAP;EACH;EAhbL;EAAA;EAAA,WAibI,cAAKyO,GAAL,EAAUmB,OAAV,EAAmB5P,EAAnB,EAAuB;EACnB,WAAKqP,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCmB,OAAhC,EAAyC5P,EAAzC;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA7bA;EAAA;EAAA,WA8bI,oBAAWzD,IAAX,EAAiBC,IAAjB,EAAuBoT,OAAvB,EAAgC5P,EAAhC,EAAoC;EAChC,UAAI,eAAe,OAAOxD,IAA1B,EAAgC;EAC5BwD,QAAAA,EAAE,GAAGxD,IAAL;EACAA,QAAAA,IAAI,GAAG2L,SAAP;EACH;;EACD,UAAI,eAAe,OAAOyH,OAA1B,EAAmC;EAC/B5P,QAAAA,EAAE,GAAG4P,OAAL;EACAA,QAAAA,OAAO,GAAG,IAAV;EACH;;EACD,UAAI,cAAc,KAAKnM,UAAnB,IAAiC,aAAa,KAAKA,UAAvD,EAAmE;EAC/D;EACH;;EACDmM,MAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;EACAA,MAAAA,OAAO,CAACC,QAAR,GAAmB,UAAUD,OAAO,CAACC,QAArC;EACA,UAAMzQ,MAAM,GAAG;EACX7C,QAAAA,IAAI,EAAEA,IADK;EAEXC,QAAAA,IAAI,EAAEA,IAFK;EAGXoT,QAAAA,OAAO,EAAEA;EAHE,OAAf;EAKA,WAAK5O,YAAL,CAAkB,cAAlB,EAAkC5B,MAAlC;EACA,WAAK4N,WAAL,CAAiBvN,IAAjB,CAAsBL,MAAtB;EACA,UAAIY,EAAJ,EACI,KAAKE,IAAL,CAAU,OAAV,EAAmBF,EAAnB;EACJ,WAAK2O,KAAL;EACH;EACD;EACJ;EACA;EACA;EACA;;EA3dA;EAAA;EAAA,WA4dI,iBAAQ;EAAA;;EACJ,UAAM9H,KAAK,GAAG,SAARA,KAAQ,GAAM;EAChB,QAAA,MAAI,CAAChD,OAAL,CAAa,cAAb;;EACA,QAAA,MAAI,CAACkK,SAAL,CAAelH,KAAf;EACH,OAHD;;EAIA,UAAMiJ,eAAe,GAAG,SAAlBA,eAAkB,GAAM;EAC1B,QAAA,MAAI,CAAC3P,GAAL,CAAS,SAAT,EAAoB2P,eAApB;;EACA,QAAA,MAAI,CAAC3P,GAAL,CAAS,cAAT,EAAyB2P,eAAzB;;EACAjJ,QAAAA,KAAK;EACR,OAJD;;EAKA,UAAMkJ,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EACzB;EACA,QAAA,MAAI,CAAC7P,IAAL,CAAU,SAAV,EAAqB4P,eAArB;;EACA,QAAA,MAAI,CAAC5P,IAAL,CAAU,cAAV,EAA0B4P,eAA1B;EACH,OAJD;;EAKA,UAAI,cAAc,KAAKrM,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;EAC7D,aAAKA,UAAL,GAAkB,SAAlB;;EACA,YAAI,KAAKuJ,WAAL,CAAiBvO,MAArB,EAA6B;EACzB,eAAKyB,IAAL,CAAU,OAAV,EAAmB,YAAM;EACrB,gBAAI,MAAI,CAACwO,SAAT,EAAoB;EAChBqB,cAAAA,cAAc;EACjB,aAFD,MAGK;EACDlJ,cAAAA,KAAK;EACR;EACJ,WAPD;EAQH,SATD,MAUK,IAAI,KAAK6H,SAAT,EAAoB;EACrBqB,UAAAA,cAAc;EACjB,SAFI,MAGA;EACDlJ,UAAAA,KAAK;EACR;EACJ;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EApgBA;EAAA;EAAA,WAqgBI,iBAAQzB,GAAR,EAAa;EACT2H,MAAAA,MAAM,CAACmB,qBAAP,GAA+B,KAA/B;EACA,WAAKlN,YAAL,CAAkB,OAAlB,EAA2BoE,GAA3B;EACA,WAAKvB,OAAL,CAAa,iBAAb,EAAgCuB,GAAhC;EACH;EACD;EACJ;EACA;EACA;EACA;;EA9gBA;EAAA;EAAA,WA+gBI,iBAAQlC,MAAR,EAAgBC,WAAhB,EAA6B;EACzB,UAAI,cAAc,KAAKM,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAFvB,EAEmC;EAC/B;EACA,aAAKnB,cAAL,CAAoB,KAAKwL,gBAAzB,EAF+B;;EAI/B,aAAKC,SAAL,CAAexN,kBAAf,CAAkC,OAAlC,EAJ+B;;EAM/B,aAAKwN,SAAL,CAAelH,KAAf,GAN+B;;EAQ/B,aAAKkH,SAAL,CAAexN,kBAAf;;EACA,YAAI,OAAOC,mBAAP,KAA+B,UAAnC,EAA+C;EAC3CA,UAAAA,mBAAmB,CAAC,SAAD,EAAY,KAAKwN,oBAAjB,EAAuC,KAAvC,CAAnB;EACH,SAX8B;;;EAa/B,aAAKvK,UAAL,GAAkB,QAAlB,CAb+B;;EAe/B,aAAKiK,EAAL,GAAU,IAAV,CAf+B;;EAiB/B,aAAK1M,YAAL,CAAkB,OAAlB,EAA2BkC,MAA3B,EAAmCC,WAAnC,EAjB+B;EAmB/B;;EACA,aAAK6J,WAAL,GAAmB,EAAnB;EACA,aAAKC,aAAL,GAAqB,CAArB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAhjBA;EAAA;EAAA,WAijBI,wBAAeU,QAAf,EAAyB;EACrB,UAAMqC,gBAAgB,GAAG,EAAzB;EACA,UAAIjS,CAAC,GAAG,CAAR;EACA,UAAMkS,CAAC,GAAGtC,QAAQ,CAAClP,MAAnB;;EACA,aAAOV,CAAC,GAAGkS,CAAX,EAAclS,CAAC,EAAf,EAAmB;EACf,YAAI,CAAC,KAAK0N,UAAL,CAAgBlE,OAAhB,CAAwBoG,QAAQ,CAAC5P,CAAD,CAAhC,CAAL,EACIiS,gBAAgB,CAACvQ,IAAjB,CAAsBkO,QAAQ,CAAC5P,CAAD,CAA9B;EACP;;EACD,aAAOiS,gBAAP;EACH;EA1jBL;;EAAA;EAAA,EAA4BrQ,OAA5B;AA4jBAoN,UAAM,CAACrN,QAAP,GAAkBA,UAAlB;;ECjkBA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASwQ,GAAT,CAAaxI,GAAb,EAAkC;EAAA,MAAhBF,IAAgB,uEAAT,EAAS;EAAA,MAAL2I,GAAK;EACrC,MAAIlT,GAAG,GAAGyK,GAAV,CADqC;;EAGrCyI,EAAAA,GAAG,GAAGA,GAAG,IAAK,OAAOpK,QAAP,KAAoB,WAApB,IAAmCA,QAAjD;EACA,MAAI,QAAQ2B,GAAZ,EACIA,GAAG,GAAGyI,GAAG,CAACzQ,QAAJ,GAAe,IAAf,GAAsByQ,GAAG,CAAC/D,IAAhC,CALiC;;EAOrC,MAAI,OAAO1E,GAAP,KAAe,QAAnB,EAA6B;EACzB,QAAI,QAAQA,GAAG,CAACrJ,MAAJ,CAAW,CAAX,CAAZ,EAA2B;EACvB,UAAI,QAAQqJ,GAAG,CAACrJ,MAAJ,CAAW,CAAX,CAAZ,EAA2B;EACvBqJ,QAAAA,GAAG,GAAGyI,GAAG,CAACzQ,QAAJ,GAAegI,GAArB;EACH,OAFD,MAGK;EACDA,QAAAA,GAAG,GAAGyI,GAAG,CAAC/D,IAAJ,GAAW1E,GAAjB;EACH;EACJ;;EACD,QAAI,CAAC,sBAAsB0I,IAAtB,CAA2B1I,GAA3B,CAAL,EAAsC;EAClC,UAAI,gBAAgB,OAAOyI,GAA3B,EAAgC;EAC5BzI,QAAAA,GAAG,GAAGyI,GAAG,CAACzQ,QAAJ,GAAe,IAAf,GAAsBgI,GAA5B;EACH,OAFD,MAGK;EACDA,QAAAA,GAAG,GAAG,aAAaA,GAAnB;EACH;EACJ,KAhBwB;;;EAkBzBzK,IAAAA,GAAG,GAAG4O,KAAK,CAACnE,GAAD,CAAX;EACH,GA1BoC;;;EA4BrC,MAAI,CAACzK,GAAG,CAACgJ,IAAT,EAAe;EACX,QAAI,cAAcmK,IAAd,CAAmBnT,GAAG,CAACyC,QAAvB,CAAJ,EAAsC;EAClCzC,MAAAA,GAAG,CAACgJ,IAAJ,GAAW,IAAX;EACH,KAFD,MAGK,IAAI,eAAemK,IAAf,CAAoBnT,GAAG,CAACyC,QAAxB,CAAJ,EAAuC;EACxCzC,MAAAA,GAAG,CAACgJ,IAAJ,GAAW,KAAX;EACH;EACJ;;EACDhJ,EAAAA,GAAG,CAACuK,IAAJ,GAAWvK,GAAG,CAACuK,IAAJ,IAAY,GAAvB;EACA,MAAMF,IAAI,GAAGrK,GAAG,CAACmP,IAAJ,CAAS7E,OAAT,CAAiB,GAAjB,MAA0B,CAAC,CAAxC;EACA,MAAM6E,IAAI,GAAG9E,IAAI,GAAG,MAAMrK,GAAG,CAACmP,IAAV,GAAiB,GAApB,GAA0BnP,GAAG,CAACmP,IAA/C,CAtCqC;;EAwCrCnP,EAAAA,GAAG,CAACyQ,EAAJ,GAASzQ,GAAG,CAACyC,QAAJ,GAAe,KAAf,GAAuB0M,IAAvB,GAA8B,GAA9B,GAAoCnP,GAAG,CAACgJ,IAAxC,GAA+CuB,IAAxD,CAxCqC;;EA0CrCvK,EAAAA,GAAG,CAACoT,IAAJ,GACIpT,GAAG,CAACyC,QAAJ,GACI,KADJ,GAEI0M,IAFJ,IAGK+D,GAAG,IAAIA,GAAG,CAAClK,IAAJ,KAAahJ,GAAG,CAACgJ,IAAxB,GAA+B,EAA/B,GAAoC,MAAMhJ,GAAG,CAACgJ,IAHnD,CADJ;EAKA,SAAOhJ,GAAP;EACH;;EC1DD,IAAMH,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;EACA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAACC,GAAD,EAAS;EACpB,SAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,CAACC,MAAJ,YAAsBH,WAF5B;EAGH,CAJD;;EAKA,IAAMH,QAAQ,GAAGZ,MAAM,CAACW,SAAP,CAAiBC,QAAlC;EACA,IAAMH,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGE,QAAQ,CAACC,IAAT,CAAcH,IAAd,MAAwB,0BAFhC;EAGA,IAAM4T,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACG3T,QAAQ,CAACC,IAAT,CAAc0T,IAAd,MAAwB,0BAFhC;EAGA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAAkBvT,GAAlB,EAAuB;EAC1B,SAASH,qBAAqB,KAAKG,GAAG,YAAYF,WAAf,IAA8BC,MAAM,CAACC,GAAD,CAAzC,CAAtB,IACHR,cAAc,IAAIQ,GAAG,YAAYP,IAD9B,IAEH4T,cAAc,IAAIrT,GAAG,YAAYsT,IAFtC;EAGH;EACM,SAASE,SAAT,CAAmBxT,GAAnB,EAAwByT,MAAxB,EAAgC;EACnC,MAAI,CAACzT,GAAD,IAAQ,QAAOA,GAAP,MAAe,QAA3B,EAAqC;EACjC,WAAO,KAAP;EACH;;EACD,MAAIiC,KAAK,CAACyR,OAAN,CAAc1T,GAAd,CAAJ,EAAwB;EACpB,SAAK,IAAIc,CAAC,GAAG,CAAR,EAAWgF,CAAC,GAAG9F,GAAG,CAACwB,MAAxB,EAAgCV,CAAC,GAAGgF,CAApC,EAAuChF,CAAC,EAAxC,EAA4C;EACxC,UAAI0S,SAAS,CAACxT,GAAG,CAACc,CAAD,CAAJ,CAAb,EAAuB;EACnB,eAAO,IAAP;EACH;EACJ;;EACD,WAAO,KAAP;EACH;;EACD,MAAIyS,QAAQ,CAACvT,GAAD,CAAZ,EAAmB;EACf,WAAO,IAAP;EACH;;EACD,MAAIA,GAAG,CAACyT,MAAJ,IACA,OAAOzT,GAAG,CAACyT,MAAX,KAAsB,UADtB,IAEArQ,SAAS,CAAC5B,MAAV,KAAqB,CAFzB,EAE4B;EACxB,WAAOgS,SAAS,CAACxT,GAAG,CAACyT,MAAJ,EAAD,EAAe,IAAf,CAAhB;EACH;;EACD,OAAK,IAAMrU,GAAX,IAAkBY,GAAlB,EAAuB;EACnB,QAAIjB,MAAM,CAACW,SAAP,CAAiBgF,cAAjB,CAAgC9E,IAAhC,CAAqCI,GAArC,EAA0CZ,GAA1C,KAAkDoU,SAAS,CAACxT,GAAG,CAACZ,GAAD,CAAJ,CAA/D,EAA2E;EACvE,aAAO,IAAP;EACH;EACJ;;EACD,SAAO,KAAP;EACH;;EChDD;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASuU,iBAAT,CAA2BxR,MAA3B,EAAmC;EACtC,MAAMyR,OAAO,GAAG,EAAhB;EACA,MAAMC,UAAU,GAAG1R,MAAM,CAAC5C,IAA1B;EACA,MAAMuU,IAAI,GAAG3R,MAAb;EACA2R,EAAAA,IAAI,CAACvU,IAAL,GAAYwU,kBAAkB,CAACF,UAAD,EAAaD,OAAb,CAA9B;EACAE,EAAAA,IAAI,CAACE,WAAL,GAAmBJ,OAAO,CAACpS,MAA3B,CALsC;;EAMtC,SAAO;EAAEW,IAAAA,MAAM,EAAE2R,IAAV;EAAgBF,IAAAA,OAAO,EAAEA;EAAzB,GAAP;EACH;;EACD,SAASG,kBAAT,CAA4BxU,IAA5B,EAAkCqU,OAAlC,EAA2C;EACvC,MAAI,CAACrU,IAAL,EACI,OAAOA,IAAP;;EACJ,MAAIgU,QAAQ,CAAChU,IAAD,CAAZ,EAAoB;EAChB,QAAM0U,WAAW,GAAG;EAAEC,MAAAA,YAAY,EAAE,IAAhB;EAAsB7M,MAAAA,GAAG,EAAEuM,OAAO,CAACpS;EAAnC,KAApB;EACAoS,IAAAA,OAAO,CAACpR,IAAR,CAAajD,IAAb;EACA,WAAO0U,WAAP;EACH,GAJD,MAKK,IAAIhS,KAAK,CAACyR,OAAN,CAAcnU,IAAd,CAAJ,EAAyB;EAC1B,QAAM4U,OAAO,GAAG,IAAIlS,KAAJ,CAAU1C,IAAI,CAACiC,MAAf,CAAhB;;EACA,SAAK,IAAIV,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvB,IAAI,CAACiC,MAAzB,EAAiCV,CAAC,EAAlC,EAAsC;EAClCqT,MAAAA,OAAO,CAACrT,CAAD,CAAP,GAAaiT,kBAAkB,CAACxU,IAAI,CAACuB,CAAD,CAAL,EAAU8S,OAAV,CAA/B;EACH;;EACD,WAAOO,OAAP;EACH,GANI,MAOA,IAAI,QAAO5U,IAAP,MAAgB,QAAhB,IAA4B,EAAEA,IAAI,YAAYmI,IAAlB,CAAhC,EAAyD;EAC1D,QAAMyM,QAAO,GAAG,EAAhB;;EACA,SAAK,IAAM/U,GAAX,IAAkBG,IAAlB,EAAwB;EACpB,UAAIR,MAAM,CAACW,SAAP,CAAiBgF,cAAjB,CAAgC9E,IAAhC,CAAqCL,IAArC,EAA2CH,GAA3C,CAAJ,EAAqD;EACjD+U,QAAAA,QAAO,CAAC/U,GAAD,CAAP,GAAe2U,kBAAkB,CAACxU,IAAI,CAACH,GAAD,CAAL,EAAYwU,OAAZ,CAAjC;EACH;EACJ;;EACD,WAAOO,QAAP;EACH;;EACD,SAAO5U,IAAP;EACH;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACO,SAAS6U,iBAAT,CAA2BjS,MAA3B,EAAmCyR,OAAnC,EAA4C;EAC/CzR,EAAAA,MAAM,CAAC5C,IAAP,GAAc8U,kBAAkB,CAAClS,MAAM,CAAC5C,IAAR,EAAcqU,OAAd,CAAhC;EACAzR,EAAAA,MAAM,CAAC6R,WAAP,GAAqB9I,SAArB,CAF+C;;EAG/C,SAAO/I,MAAP;EACH;;EACD,SAASkS,kBAAT,CAA4B9U,IAA5B,EAAkCqU,OAAlC,EAA2C;EACvC,MAAI,CAACrU,IAAL,EACI,OAAOA,IAAP;;EACJ,MAAIA,IAAI,IAAIA,IAAI,CAAC2U,YAAjB,EAA+B;EAC3B,WAAON,OAAO,CAACrU,IAAI,CAAC8H,GAAN,CAAd,CAD2B;EAE9B,GAFD,MAGK,IAAIpF,KAAK,CAACyR,OAAN,CAAcnU,IAAd,CAAJ,EAAyB;EAC1B,SAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvB,IAAI,CAACiC,MAAzB,EAAiCV,CAAC,EAAlC,EAAsC;EAClCvB,MAAAA,IAAI,CAACuB,CAAD,CAAJ,GAAUuT,kBAAkB,CAAC9U,IAAI,CAACuB,CAAD,CAAL,EAAU8S,OAAV,CAA5B;EACH;EACJ,GAJI,MAKA,IAAI,QAAOrU,IAAP,MAAgB,QAApB,EAA8B;EAC/B,SAAK,IAAMH,GAAX,IAAkBG,IAAlB,EAAwB;EACpB,UAAIR,MAAM,CAACW,SAAP,CAAiBgF,cAAjB,CAAgC9E,IAAhC,CAAqCL,IAArC,EAA2CH,GAA3C,CAAJ,EAAqD;EACjDG,QAAAA,IAAI,CAACH,GAAD,CAAJ,GAAYiV,kBAAkB,CAAC9U,IAAI,CAACH,GAAD,CAAL,EAAYwU,OAAZ,CAA9B;EACH;EACJ;EACJ;;EACD,SAAOrU,IAAP;EACH;;ECvED;EACA;EACA;EACA;EACA;;EACO,IAAMkD,QAAQ,GAAG,CAAjB;EACA,IAAI6R,UAAJ;;EACP,CAAC,UAAUA,UAAV,EAAsB;EACnBA,EAAAA,UAAU,CAACA,UAAU,CAAC,SAAD,CAAV,GAAwB,CAAzB,CAAV,GAAwC,SAAxC;EACAA,EAAAA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;EACAA,EAAAA,UAAU,CAACA,UAAU,CAAC,OAAD,CAAV,GAAsB,CAAvB,CAAV,GAAsC,OAAtC;EACAA,EAAAA,UAAU,CAACA,UAAU,CAAC,KAAD,CAAV,GAAoB,CAArB,CAAV,GAAoC,KAApC;EACAA,EAAAA,UAAU,CAACA,UAAU,CAAC,eAAD,CAAV,GAA8B,CAA/B,CAAV,GAA8C,eAA9C;EACAA,EAAAA,UAAU,CAACA,UAAU,CAAC,cAAD,CAAV,GAA6B,CAA9B,CAAV,GAA6C,cAA7C;EACAA,EAAAA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;EACH,CARD,EAQGA,UAAU,KAAKA,UAAU,GAAG,EAAlB,CARb;EASA;EACA;EACA;;;MACaC,OAAb;EACI;EACJ;EACA;EACA;EACA;EACI,mBAAYC,QAAZ,EAAsB;EAAA;;EAClB,SAAKA,QAAL,GAAgBA,QAAhB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EAdA;EAAA;EAAA,WAeI,gBAAOxU,GAAP,EAAY;EACR,UAAIA,GAAG,CAACV,IAAJ,KAAagV,UAAU,CAACG,KAAxB,IAAiCzU,GAAG,CAACV,IAAJ,KAAagV,UAAU,CAACI,GAA7D,EAAkE;EAC9D,YAAIlB,SAAS,CAACxT,GAAD,CAAb,EAAoB;EAChBA,UAAAA,GAAG,CAACV,IAAJ,GACIU,GAAG,CAACV,IAAJ,KAAagV,UAAU,CAACG,KAAxB,GACMH,UAAU,CAACK,YADjB,GAEML,UAAU,CAACM,UAHrB;EAIA,iBAAO,KAAKC,cAAL,CAAoB7U,GAApB,CAAP;EACH;EACJ;;EACD,aAAO,CAAC,KAAK8U,cAAL,CAAoB9U,GAApB,CAAD,CAAP;EACH;EACD;EACJ;EACA;;EA7BA;EAAA;EAAA,WA8BI,wBAAeA,GAAf,EAAoB;EAChB;EACA,UAAI4F,GAAG,GAAG,KAAK5F,GAAG,CAACV,IAAnB,CAFgB;;EAIhB,UAAIU,GAAG,CAACV,IAAJ,KAAagV,UAAU,CAACK,YAAxB,IACA3U,GAAG,CAACV,IAAJ,KAAagV,UAAU,CAACM,UAD5B,EACwC;EACpChP,QAAAA,GAAG,IAAI5F,GAAG,CAACgU,WAAJ,GAAkB,GAAzB;EACH,OAPe;EAShB;;;EACA,UAAIhU,GAAG,CAAC+U,GAAJ,IAAW,QAAQ/U,GAAG,CAAC+U,GAA3B,EAAgC;EAC5BnP,QAAAA,GAAG,IAAI5F,GAAG,CAAC+U,GAAJ,GAAU,GAAjB;EACH,OAZe;;;EAchB,UAAI,QAAQ/U,GAAG,CAACyQ,EAAhB,EAAoB;EAChB7K,QAAAA,GAAG,IAAI5F,GAAG,CAACyQ,EAAX;EACH,OAhBe;;;EAkBhB,UAAI,QAAQzQ,GAAG,CAACT,IAAhB,EAAsB;EAClBqG,QAAAA,GAAG,IAAIsM,IAAI,CAAC8C,SAAL,CAAehV,GAAG,CAACT,IAAnB,EAAyB,KAAKiV,QAA9B,CAAP;EACH;;EACD,aAAO5O,GAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAzDA;EAAA;EAAA,WA0DI,wBAAe5F,GAAf,EAAoB;EAChB,UAAMiV,cAAc,GAAGtB,iBAAiB,CAAC3T,GAAD,CAAxC;EACA,UAAM8T,IAAI,GAAG,KAAKgB,cAAL,CAAoBG,cAAc,CAAC9S,MAAnC,CAAb;EACA,UAAMyR,OAAO,GAAGqB,cAAc,CAACrB,OAA/B;EACAA,MAAAA,OAAO,CAACsB,OAAR,CAAgBpB,IAAhB,EAJgB;;EAKhB,aAAOF,OAAP,CALgB;EAMnB;EAhEL;;EAAA;EAAA;EAkEA;EACA;EACA;EACA;EACA;;MACauB,OAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACI,mBAAYC,OAAZ,EAAqB;EAAA;;EAAA;;EACjB;EACA,UAAKA,OAAL,GAAeA,OAAf;EAFiB;EAGpB;EACD;EACJ;EACA;EACA;EACA;;;EAdA;EAAA;EAAA,WAeI,aAAIpV,GAAJ,EAAS;EACL,UAAImC,MAAJ;;EACA,UAAI,OAAOnC,GAAP,KAAe,QAAnB,EAA6B;EACzBmC,QAAAA,MAAM,GAAG,KAAKkT,YAAL,CAAkBrV,GAAlB,CAAT;;EACA,YAAImC,MAAM,CAAC7C,IAAP,KAAgBgV,UAAU,CAACK,YAA3B,IACAxS,MAAM,CAAC7C,IAAP,KAAgBgV,UAAU,CAACM,UAD/B,EAC2C;EACvC;EACA,eAAKU,aAAL,GAAqB,IAAIC,mBAAJ,CAAwBpT,MAAxB,CAArB,CAFuC;;EAIvC,cAAIA,MAAM,CAAC6R,WAAP,KAAuB,CAA3B,EAA8B;EAC1B,sFAAmB,SAAnB,EAA8B7R,MAA9B;EACH;EACJ,SARD,MASK;EACD;EACA,oFAAmB,SAAnB,EAA8BA,MAA9B;EACH;EACJ,OAfD,MAgBK,IAAIoR,QAAQ,CAACvT,GAAD,CAAR,IAAiBA,GAAG,CAAC0B,MAAzB,EAAiC;EAClC;EACA,YAAI,CAAC,KAAK4T,aAAV,EAAyB;EACrB,gBAAM,IAAIlP,KAAJ,CAAU,kDAAV,CAAN;EACH,SAFD,MAGK;EACDjE,UAAAA,MAAM,GAAG,KAAKmT,aAAL,CAAmBE,cAAnB,CAAkCxV,GAAlC,CAAT;;EACA,cAAImC,MAAJ,EAAY;EACR;EACA,iBAAKmT,aAAL,GAAqB,IAArB;;EACA,sFAAmB,SAAnB,EAA8BnT,MAA9B;EACH;EACJ;EACJ,OAbI,MAcA;EACD,cAAM,IAAIiE,KAAJ,CAAU,mBAAmBpG,GAA7B,CAAN;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxDA;EAAA;EAAA,WAyDI,sBAAa4F,GAAb,EAAkB;EACd,UAAI9E,CAAC,GAAG,CAAR,CADc;;EAGd,UAAM2U,CAAC,GAAG;EACNnW,QAAAA,IAAI,EAAE6K,MAAM,CAACvE,GAAG,CAACxE,MAAJ,CAAW,CAAX,CAAD;EADN,OAAV;;EAGA,UAAIkT,UAAU,CAACmB,CAAC,CAACnW,IAAH,CAAV,KAAuB4L,SAA3B,EAAsC;EAClC,cAAM,IAAI9E,KAAJ,CAAU,yBAAyBqP,CAAC,CAACnW,IAArC,CAAN;EACH,OARa;;;EAUd,UAAImW,CAAC,CAACnW,IAAF,KAAWgV,UAAU,CAACK,YAAtB,IACAc,CAAC,CAACnW,IAAF,KAAWgV,UAAU,CAACM,UAD1B,EACsC;EAClC,YAAMc,KAAK,GAAG5U,CAAC,GAAG,CAAlB;;EACA,eAAO8E,GAAG,CAACxE,MAAJ,CAAW,EAAEN,CAAb,MAAoB,GAApB,IAA2BA,CAAC,IAAI8E,GAAG,CAACpE,MAA3C,EAAmD;;EACnD,YAAMmU,GAAG,GAAG/P,GAAG,CAACtE,SAAJ,CAAcoU,KAAd,EAAqB5U,CAArB,CAAZ;;EACA,YAAI6U,GAAG,IAAIxL,MAAM,CAACwL,GAAD,CAAb,IAAsB/P,GAAG,CAACxE,MAAJ,CAAWN,CAAX,MAAkB,GAA5C,EAAiD;EAC7C,gBAAM,IAAIsF,KAAJ,CAAU,qBAAV,CAAN;EACH;;EACDqP,QAAAA,CAAC,CAACzB,WAAF,GAAgB7J,MAAM,CAACwL,GAAD,CAAtB;EACH,OAnBa;;;EAqBd,UAAI,QAAQ/P,GAAG,CAACxE,MAAJ,CAAWN,CAAC,GAAG,CAAf,CAAZ,EAA+B;EAC3B,YAAM4U,MAAK,GAAG5U,CAAC,GAAG,CAAlB;;EACA,eAAO,EAAEA,CAAT,EAAY;EACR,cAAM+E,CAAC,GAAGD,GAAG,CAACxE,MAAJ,CAAWN,CAAX,CAAV;EACA,cAAI,QAAQ+E,CAAZ,EACI;EACJ,cAAI/E,CAAC,KAAK8E,GAAG,CAACpE,MAAd,EACI;EACP;;EACDiU,QAAAA,CAAC,CAACV,GAAF,GAAQnP,GAAG,CAACtE,SAAJ,CAAcoU,MAAd,EAAqB5U,CAArB,CAAR;EACH,OAVD,MAWK;EACD2U,QAAAA,CAAC,CAACV,GAAF,GAAQ,GAAR;EACH,OAlCa;;;EAoCd,UAAMa,IAAI,GAAGhQ,GAAG,CAACxE,MAAJ,CAAWN,CAAC,GAAG,CAAf,CAAb;;EACA,UAAI,OAAO8U,IAAP,IAAezL,MAAM,CAACyL,IAAD,CAAN,IAAgBA,IAAnC,EAAyC;EACrC,YAAMF,OAAK,GAAG5U,CAAC,GAAG,CAAlB;;EACA,eAAO,EAAEA,CAAT,EAAY;EACR,cAAM+E,EAAC,GAAGD,GAAG,CAACxE,MAAJ,CAAWN,CAAX,CAAV;;EACA,cAAI,QAAQ+E,EAAR,IAAasE,MAAM,CAACtE,EAAD,CAAN,IAAaA,EAA9B,EAAiC;EAC7B,cAAE/E,CAAF;EACA;EACH;;EACD,cAAIA,CAAC,KAAK8E,GAAG,CAACpE,MAAd,EACI;EACP;;EACDiU,QAAAA,CAAC,CAAChF,EAAF,GAAOtG,MAAM,CAACvE,GAAG,CAACtE,SAAJ,CAAcoU,OAAd,EAAqB5U,CAAC,GAAG,CAAzB,CAAD,CAAb;EACH,OAjDa;;;EAmDd,UAAI8E,GAAG,CAACxE,MAAJ,CAAW,EAAEN,CAAb,CAAJ,EAAqB;EACjB,YAAM+U,OAAO,GAAG,KAAKC,QAAL,CAAclQ,GAAG,CAAC8J,MAAJ,CAAW5O,CAAX,CAAd,CAAhB;;EACA,YAAIqU,OAAO,CAACY,cAAR,CAAuBN,CAAC,CAACnW,IAAzB,EAA+BuW,OAA/B,CAAJ,EAA6C;EACzCJ,UAAAA,CAAC,CAAClW,IAAF,GAASsW,OAAT;EACH,SAFD,MAGK;EACD,gBAAM,IAAIzP,KAAJ,CAAU,iBAAV,CAAN;EACH;EACJ;;EACD,aAAOqP,CAAP;EACH;EAtHL;EAAA;EAAA,WAuHI,kBAAS7P,GAAT,EAAc;EACV,UAAI;EACA,eAAOsM,IAAI,CAACtD,KAAL,CAAWhJ,GAAX,EAAgB,KAAKwP,OAArB,CAAP;EACH,OAFD,CAGA,OAAO9M,CAAP,EAAU;EACN,eAAO,KAAP;EACH;EACJ;EA9HL;EAAA;EAAA;EA+II;EACJ;EACA;EACI,uBAAU;EACN,UAAI,KAAKgN,aAAT,EAAwB;EACpB,aAAKA,aAAL,CAAmBU,sBAAnB;EACH;EACJ;EAtJL;EAAA;EAAA,WA+HI,wBAAsB1W,IAAtB,EAA4BuW,OAA5B,EAAqC;EACjC,cAAQvW,IAAR;EACI,aAAKgV,UAAU,CAAC2B,OAAhB;EACI,iBAAO,QAAOJ,OAAP,MAAmB,QAA1B;;EACJ,aAAKvB,UAAU,CAAC4B,UAAhB;EACI,iBAAOL,OAAO,KAAK3K,SAAnB;;EACJ,aAAKoJ,UAAU,CAAC6B,aAAhB;EACI,iBAAO,OAAON,OAAP,KAAmB,QAAnB,IAA+B,QAAOA,OAAP,MAAmB,QAAzD;;EACJ,aAAKvB,UAAU,CAACG,KAAhB;EACA,aAAKH,UAAU,CAACK,YAAhB;EACI,iBAAO1S,KAAK,CAACyR,OAAN,CAAcmC,OAAd,KAA0BA,OAAO,CAACrU,MAAR,GAAiB,CAAlD;;EACJ,aAAK8S,UAAU,CAACI,GAAhB;EACA,aAAKJ,UAAU,CAACM,UAAhB;EACI,iBAAO3S,KAAK,CAACyR,OAAN,CAAcmC,OAAd,CAAP;EAZR;EAcH;EA9IL;;EAAA;EAAA,EAA6BnT,OAA7B;EAwJA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MACM6S;EACF,+BAAYpT,MAAZ,EAAoB;EAAA;;EAChB,SAAKA,MAAL,GAAcA,MAAd;EACA,SAAKyR,OAAL,GAAe,EAAf;EACA,SAAKwC,SAAL,GAAiBjU,MAAjB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;;;aACI,wBAAekU,OAAf,EAAwB;EACpB,WAAKzC,OAAL,CAAapR,IAAb,CAAkB6T,OAAlB;;EACA,UAAI,KAAKzC,OAAL,CAAapS,MAAb,KAAwB,KAAK4U,SAAL,CAAepC,WAA3C,EAAwD;EACpD;EACA,YAAM7R,MAAM,GAAGiS,iBAAiB,CAAC,KAAKgC,SAAN,EAAiB,KAAKxC,OAAtB,CAAhC;EACA,aAAKoC,sBAAL;EACA,eAAO7T,MAAP;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;;;;aACI,kCAAyB;EACrB,WAAKiU,SAAL,GAAiB,IAAjB;EACA,WAAKxC,OAAL,GAAe,EAAf;EACH;;;;;;;;;;;;;;EC3RE,SAAShR,EAAT,CAAY5C,GAAZ,EAAiBoO,EAAjB,EAAqBrL,EAArB,EAAyB;EAC5B/C,EAAAA,GAAG,CAAC4C,EAAJ,CAAOwL,EAAP,EAAWrL,EAAX;EACA,SAAO,SAASuT,UAAT,GAAsB;EACzBtW,IAAAA,GAAG,CAACkD,GAAJ,CAAQkL,EAAR,EAAYrL,EAAZ;EACH,GAFD;EAGH;;ECFD;EACA;EACA;EACA;;EACA,IAAMwT,eAAe,GAAGxX,MAAM,CAACyX,MAAP,CAAc;EAClCC,EAAAA,OAAO,EAAE,CADyB;EAElCC,EAAAA,aAAa,EAAE,CAFmB;EAGlCC,EAAAA,UAAU,EAAE,CAHsB;EAIlCC,EAAAA,aAAa,EAAE,CAJmB;EAKlC;EACAC,EAAAA,WAAW,EAAE,CANqB;EAOlCxT,EAAAA,cAAc,EAAE;EAPkB,CAAd,CAAxB;MASayM,MAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACI,kBAAYgH,EAAZ,EAAgB/B,GAAhB,EAAqB/P,IAArB,EAA2B;EAAA;;EAAA;;EACvB;EACA,UAAK+R,SAAL,GAAiB,KAAjB;EACA,UAAKC,aAAL,GAAqB,EAArB;EACA,UAAKC,UAAL,GAAkB,EAAlB;EACA,UAAKC,GAAL,GAAW,CAAX;EACA,UAAKC,IAAL,GAAY,EAAZ;EACA,UAAKC,KAAL,GAAa,EAAb;EACA,UAAKN,EAAL,GAAUA,EAAV;EACA,UAAK/B,GAAL,GAAWA,GAAX;;EACA,QAAI/P,IAAI,IAAIA,IAAI,CAACqS,IAAjB,EAAuB;EACnB,YAAKA,IAAL,GAAYrS,IAAI,CAACqS,IAAjB;EACH;;EACD,QAAI,MAAKP,EAAL,CAAQQ,YAAZ,EACI,MAAKlM,IAAL;EAdmB;EAe1B;EACD;EACJ;EACA;;;EAxBA;EAAA;EAAA,SAyBI,eAAmB;EACf,aAAO,CAAC,KAAK2L,SAAb;EACH;EACD;EACJ;EACA;EACA;EACA;;EAhCA;EAAA;EAAA,WAiCI,qBAAY;EACR,UAAI,KAAKQ,IAAT,EACI;EACJ,UAAMT,EAAE,GAAG,KAAKA,EAAhB;EACA,WAAKS,IAAL,GAAY,CACR3U,EAAE,CAACkU,EAAD,EAAK,MAAL,EAAa,KAAKjJ,MAAL,CAAY1I,IAAZ,CAAiB,IAAjB,CAAb,CADM,EAERvC,EAAE,CAACkU,EAAD,EAAK,QAAL,EAAe,KAAKU,QAAL,CAAcrS,IAAd,CAAmB,IAAnB,CAAf,CAFM,EAGRvC,EAAE,CAACkU,EAAD,EAAK,OAAL,EAAc,KAAKzI,OAAL,CAAalJ,IAAb,CAAkB,IAAlB,CAAd,CAHM,EAIRvC,EAAE,CAACkU,EAAD,EAAK,OAAL,EAAc,KAAK7I,OAAL,CAAa9I,IAAb,CAAkB,IAAlB,CAAd,CAJM,CAAZ;EAMH;EACD;EACJ;EACA;;EA9CA;EAAA;EAAA,SA+CI,eAAa;EACT,aAAO,CAAC,CAAC,KAAKoS,IAAd;EACH;EACD;EACJ;EACA;EACA;EACA;;EAtDA;EAAA;EAAA,WAuDI,mBAAU;EACN,UAAI,KAAKR,SAAT,EACI,OAAO,IAAP;EACJ,WAAKU,SAAL;EACA,UAAI,CAAC,KAAKX,EAAL,CAAQ,eAAR,CAAL,EACI,KAAKA,EAAL,CAAQ1L,IAAR,GALE;;EAMN,UAAI,WAAW,KAAK0L,EAAL,CAAQY,WAAvB,EACI,KAAK7J,MAAL;EACJ,aAAO,IAAP;EACH;EACD;EACJ;EACA;;EAnEA;EAAA;EAAA,WAoEI,gBAAO;EACH,aAAO,KAAK4I,OAAL,EAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA5EA;EAAA;EAAA,WA6EI,gBAAc;EAAA,wCAAN7S,IAAM;EAANA,QAAAA,IAAM;EAAA;;EACVA,MAAAA,IAAI,CAACsR,OAAL,CAAa,SAAb;EACA,WAAKvR,IAAL,CAAUR,KAAV,CAAgB,IAAhB,EAAsBS,IAAtB;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAxFA;EAAA;EAAA,WAyFI,cAAKwK,EAAL,EAAkB;EACd,UAAImI,eAAe,CAAC7R,cAAhB,CAA+B0J,EAA/B,CAAJ,EAAwC;EACpC,cAAM,IAAIhI,KAAJ,CAAU,MAAMgI,EAAN,GAAW,4BAArB,CAAN;EACH;;EAHa,yCAANxK,IAAM;EAANA,QAAAA,IAAM;EAAA;;EAIdA,MAAAA,IAAI,CAACsR,OAAL,CAAa9G,EAAb;EACA,UAAMjM,MAAM,GAAG;EACX7C,QAAAA,IAAI,EAAEgV,UAAU,CAACG,KADN;EAEXlV,QAAAA,IAAI,EAAEqE;EAFK,OAAf;EAIAzB,MAAAA,MAAM,CAACwQ,OAAP,GAAiB,EAAjB;EACAxQ,MAAAA,MAAM,CAACwQ,OAAP,CAAeC,QAAf,GAA0B,KAAKwE,KAAL,CAAWxE,QAAX,KAAwB,KAAlD,CAVc;;EAYd,UAAI,eAAe,OAAOhP,IAAI,CAACA,IAAI,CAACpC,MAAL,GAAc,CAAf,CAA9B,EAAiD;EAC7C,YAAMiP,EAAE,GAAG,KAAKyG,GAAL,EAAX;EACA,YAAMS,GAAG,GAAG/T,IAAI,CAACgU,GAAL,EAAZ;;EACA,aAAKC,oBAAL,CAA0BpH,EAA1B,EAA8BkH,GAA9B;;EACAxV,QAAAA,MAAM,CAACsO,EAAP,GAAYA,EAAZ;EACH;;EACD,UAAMqH,mBAAmB,GAAG,KAAKhB,EAAL,CAAQiB,MAAR,IACxB,KAAKjB,EAAL,CAAQiB,MAAR,CAAejH,SADS,IAExB,KAAKgG,EAAL,CAAQiB,MAAR,CAAejH,SAAf,CAAyBxK,QAF7B;EAGA,UAAM0R,aAAa,GAAG,KAAKZ,KAAL,iBAAwB,CAACU,mBAAD,IAAwB,CAAC,KAAKf,SAAtD,CAAtB;;EACA,UAAIiB,aAAJ,EAAmB,CAAnB,MAEK,IAAI,KAAKjB,SAAT,EAAoB;EACrB,aAAKkB,uBAAL,CAA6B9V,MAA7B;EACA,aAAKA,MAAL,CAAYA,MAAZ;EACH,OAHI,MAIA;EACD,aAAK8U,UAAL,CAAgBzU,IAAhB,CAAqBL,MAArB;EACH;;EACD,WAAKiV,KAAL,GAAa,EAAb;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;;EA7HA;EAAA;EAAA,WA8HI,8BAAqB3G,EAArB,EAAyBkH,GAAzB,EAA8B;EAAA;;EAC1B,UAAMjM,OAAO,GAAG,KAAK0L,KAAL,CAAW1L,OAA3B;;EACA,UAAIA,OAAO,KAAKR,SAAhB,EAA2B;EACvB,aAAKiM,IAAL,CAAU1G,EAAV,IAAgBkH,GAAhB;EACA;EACH,OALyB;;;EAO1B,UAAMO,KAAK,GAAG,KAAKpB,EAAL,CAAQ5R,YAAR,CAAqB,YAAM;EACrC,eAAO,MAAI,CAACiS,IAAL,CAAU1G,EAAV,CAAP;;EACA,aAAK,IAAI3P,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,MAAI,CAACmW,UAAL,CAAgBzV,MAApC,EAA4CV,CAAC,EAA7C,EAAiD;EAC7C,cAAI,MAAI,CAACmW,UAAL,CAAgBnW,CAAhB,EAAmB2P,EAAnB,KAA0BA,EAA9B,EAAkC;EAC9B,YAAA,MAAI,CAACwG,UAAL,CAAgBvT,MAAhB,CAAuB5C,CAAvB,EAA0B,CAA1B;EACH;EACJ;;EACD6W,QAAAA,GAAG,CAAC/X,IAAJ,CAAS,MAAT,EAAe,IAAIwG,KAAJ,CAAU,yBAAV,CAAf;EACH,OARa,EAQXsF,OARW,CAAd;;EASA,WAAKyL,IAAL,CAAU1G,EAAV,IAAgB,YAAa;EACzB;EACA,QAAA,MAAI,CAACqG,EAAL,CAAQzR,cAAR,CAAuB6S,KAAvB;;EAFyB,2CAATtU,IAAS;EAATA,UAAAA,IAAS;EAAA;;EAGzB+T,QAAAA,GAAG,CAACxU,KAAJ,CAAU,MAAV,GAAiB,IAAjB,SAA0BS,IAA1B;EACH,OAJD;EAKH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAzJA;EAAA;EAAA,WA0JI,gBAAOzB,OAAP,EAAe;EACXA,MAAAA,OAAM,CAAC4S,GAAP,GAAa,KAAKA,GAAlB;;EACA,WAAK+B,EAAL,CAAQqB,OAAR,CAAgBhW,OAAhB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAlKA;EAAA;EAAA,WAmKI,kBAAS;EAAA;;EACL,UAAI,OAAO,KAAKkV,IAAZ,IAAoB,UAAxB,EAAoC;EAChC,aAAKA,IAAL,CAAU,UAAC9X,IAAD,EAAU;EAChB,UAAA,MAAI,CAAC4C,MAAL,CAAY;EAAE7C,YAAAA,IAAI,EAAEgV,UAAU,CAAC2B,OAAnB;EAA4B1W,YAAAA,IAAI,EAAJA;EAA5B,WAAZ;EACH,SAFD;EAGH,OAJD,MAKK;EACD,aAAK4C,MAAL,CAAY;EAAE7C,UAAAA,IAAI,EAAEgV,UAAU,CAAC2B,OAAnB;EAA4B1W,UAAAA,IAAI,EAAE,KAAK8X;EAAvC,SAAZ;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EAlLA;EAAA;EAAA,WAmLI,iBAAQlP,GAAR,EAAa;EACT,UAAI,CAAC,KAAK4O,SAAV,EAAqB;EACjB,aAAKhT,YAAL,CAAkB,eAAlB,EAAmCoE,GAAnC;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EA9LA;EAAA;EAAA,WA+LI,iBAAQlC,MAAR,EAAgBC,WAAhB,EAA6B;EACzB,WAAK6Q,SAAL,GAAiB,KAAjB;EACA,aAAO,KAAKtG,EAAZ;EACA,WAAK1M,YAAL,CAAkB,YAAlB,EAAgCkC,MAAhC,EAAwCC,WAAxC;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAzMA;EAAA;EAAA,WA0MI,kBAAS/D,MAAT,EAAiB;EACb,UAAMiW,aAAa,GAAGjW,MAAM,CAAC4S,GAAP,KAAe,KAAKA,GAA1C;EACA,UAAI,CAACqD,aAAL,EACI;;EACJ,cAAQjW,MAAM,CAAC7C,IAAf;EACI,aAAKgV,UAAU,CAAC2B,OAAhB;EACI,cAAI9T,MAAM,CAAC5C,IAAP,IAAe4C,MAAM,CAAC5C,IAAP,CAAY0K,GAA/B,EAAoC;EAChC,gBAAMwG,EAAE,GAAGtO,MAAM,CAAC5C,IAAP,CAAY0K,GAAvB;EACA,iBAAKoO,SAAL,CAAe5H,EAAf;EACH,WAHD,MAIK;EACD,iBAAK1M,YAAL,CAAkB,eAAlB,EAAmC,IAAIqC,KAAJ,CAAU,2LAAV,CAAnC;EACH;;EACD;;EACJ,aAAKkO,UAAU,CAACG,KAAhB;EACA,aAAKH,UAAU,CAACK,YAAhB;EACI,eAAK2D,OAAL,CAAanW,MAAb;EACA;;EACJ,aAAKmS,UAAU,CAACI,GAAhB;EACA,aAAKJ,UAAU,CAACM,UAAhB;EACI,eAAK2D,KAAL,CAAWpW,MAAX;EACA;;EACJ,aAAKmS,UAAU,CAAC4B,UAAhB;EACI,eAAKsC,YAAL;EACA;;EACJ,aAAKlE,UAAU,CAAC6B,aAAhB;EACI,eAAKsC,OAAL;EACA,cAAMtQ,GAAG,GAAG,IAAI/B,KAAJ,CAAUjE,MAAM,CAAC5C,IAAP,CAAYmZ,OAAtB,CAAZ,CAFJ;;EAIIvQ,UAAAA,GAAG,CAAC5I,IAAJ,GAAW4C,MAAM,CAAC5C,IAAP,CAAYA,IAAvB;EACA,eAAKwE,YAAL,CAAkB,eAAlB,EAAmCoE,GAAnC;EACA;EA3BR;EA6BH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAjPA;EAAA;EAAA,WAkPI,iBAAQhG,MAAR,EAAgB;EACZ,UAAMyB,IAAI,GAAGzB,MAAM,CAAC5C,IAAP,IAAe,EAA5B;;EACA,UAAI,QAAQ4C,MAAM,CAACsO,EAAnB,EAAuB;EACnB7M,QAAAA,IAAI,CAACpB,IAAL,CAAU,KAAKmV,GAAL,CAASxV,MAAM,CAACsO,EAAhB,CAAV;EACH;;EACD,UAAI,KAAKsG,SAAT,EAAoB;EAChB,aAAK4B,SAAL,CAAe/U,IAAf;EACH,OAFD,MAGK;EACD,aAAKoT,aAAL,CAAmBxU,IAAnB,CAAwBzD,MAAM,CAACyX,MAAP,CAAc5S,IAAd,CAAxB;EACH;EACJ;EA7PL;EAAA;EAAA,WA8PI,mBAAUA,IAAV,EAAgB;EACZ,UAAI,KAAKgV,aAAL,IAAsB,KAAKA,aAAL,CAAmBpX,MAA7C,EAAqD;EACjD,YAAMwC,SAAS,GAAG,KAAK4U,aAAL,CAAmB/U,KAAnB,EAAlB;;EADiD,mDAE1BG,SAF0B;EAAA;;EAAA;EAEjD,8DAAkC;EAAA,gBAAvB6U,QAAuB;EAC9BA,YAAAA,QAAQ,CAAC1V,KAAT,CAAe,IAAf,EAAqBS,IAArB;EACH;EAJgD;EAAA;EAAA;EAAA;EAAA;EAKpD;;EACD,4DAAWT,KAAX,CAAiB,IAAjB,EAAuBS,IAAvB;EACH;EACD;EACJ;EACA;EACA;EACA;;EA3QA;EAAA;EAAA,WA4QI,aAAI6M,EAAJ,EAAQ;EACJ,UAAMvM,IAAI,GAAG,IAAb;EACA,UAAI4U,IAAI,GAAG,KAAX;EACA,aAAO,YAAmB;EACtB;EACA,YAAIA,IAAJ,EACI;EACJA,QAAAA,IAAI,GAAG,IAAP;;EAJsB,2CAANlV,IAAM;EAANA,UAAAA,IAAM;EAAA;;EAKtBM,QAAAA,IAAI,CAAC/B,MAAL,CAAY;EACR7C,UAAAA,IAAI,EAAEgV,UAAU,CAACI,GADT;EAERjE,UAAAA,EAAE,EAAEA,EAFI;EAGRlR,UAAAA,IAAI,EAAEqE;EAHE,SAAZ;EAKH,OAVD;EAWH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAhSA;EAAA;EAAA,WAiSI,eAAMzB,MAAN,EAAc;EACV,UAAMwV,GAAG,GAAG,KAAKR,IAAL,CAAUhV,MAAM,CAACsO,EAAjB,CAAZ;;EACA,UAAI,eAAe,OAAOkH,GAA1B,EAA+B;EAC3BA,QAAAA,GAAG,CAACxU,KAAJ,CAAU,IAAV,EAAgBhB,MAAM,CAAC5C,IAAvB;EACA,eAAO,KAAK4X,IAAL,CAAUhV,MAAM,CAACsO,EAAjB,CAAP;EACH;EAGJ;EACD;EACJ;EACA;EACA;EACA;;EA9SA;EAAA;EAAA,WA+SI,mBAAUA,EAAV,EAAc;EACV,WAAKA,EAAL,GAAUA,EAAV;EACA,WAAKsG,SAAL,GAAiB,IAAjB;EACA,WAAKgC,YAAL;EACA,WAAKhV,YAAL,CAAkB,SAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAzTA;EAAA;EAAA,WA0TI,wBAAe;EAAA;;EACX,WAAKiT,aAAL,CAAmB7X,OAAnB,CAA2B,UAACyE,IAAD;EAAA,eAAU,MAAI,CAAC+U,SAAL,CAAe/U,IAAf,CAAV;EAAA,OAA3B;EACA,WAAKoT,aAAL,GAAqB,EAArB;EACA,WAAKC,UAAL,CAAgB9X,OAAhB,CAAwB,UAACgD,MAAD,EAAY;EAChC,QAAA,MAAI,CAAC8V,uBAAL,CAA6B9V,MAA7B;;EACA,QAAA,MAAI,CAACA,MAAL,CAAYA,MAAZ;EACH,OAHD;EAIA,WAAK8U,UAAL,GAAkB,EAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAvUA;EAAA;EAAA,WAwUI,wBAAe;EACX,WAAKwB,OAAL;EACA,WAAKxK,OAAL,CAAa,sBAAb;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAlVA;EAAA;EAAA,WAmVI,mBAAU;EACN,UAAI,KAAKsJ,IAAT,EAAe;EACX;EACA,aAAKA,IAAL,CAAUpY,OAAV,CAAkB,UAACmX,UAAD;EAAA,iBAAgBA,UAAU,EAA1B;EAAA,SAAlB;EACA,aAAKiB,IAAL,GAAYrM,SAAZ;EACH;;EACD,WAAK4L,EAAL,CAAQ,UAAR,EAAoB,IAApB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAhWA;EAAA;EAAA,WAiWI,sBAAa;EACT,UAAI,KAAKC,SAAT,EAAoB;EAChB,aAAK5U,MAAL,CAAY;EAAE7C,UAAAA,IAAI,EAAEgV,UAAU,CAAC4B;EAAnB,SAAZ;EACH,OAHQ;;;EAKT,WAAKuC,OAAL;;EACA,UAAI,KAAK1B,SAAT,EAAoB;EAChB;EACA,aAAK9I,OAAL,CAAa,sBAAb;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAlXA;EAAA;EAAA,WAmXI,iBAAQ;EACJ,aAAO,KAAK0I,UAAL,EAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EA5XA;EAAA;EAAA,WA6XI,kBAAS/D,SAAT,EAAmB;EACf,WAAKwE,KAAL,CAAWxE,QAAX,GAAsBA,SAAtB;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAvYA;EAAA;EAAA,SAwYI,eAAe;EACX,WAAKwE,KAAL,eAAsB,IAAtB;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA1ZA;EAAA;EAAA,WA2ZI,iBAAQ1L,QAAR,EAAiB;EACb,WAAK0L,KAAL,CAAW1L,OAAX,GAAqBA,QAArB;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAraA;EAAA;EAAA,WAsaI,eAAMmN,QAAN,EAAgB;EACZ,WAAKD,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;EACA,WAAKA,aAAL,CAAmBpW,IAAnB,CAAwBqW,QAAxB;;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAjbA;EAAA;EAAA,WAkbI,oBAAWA,QAAX,EAAqB;EACjB,WAAKD,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;EACA,WAAKA,aAAL,CAAmB1D,OAAnB,CAA2B2D,QAA3B;;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA5bA;EAAA;EAAA,WA6bI,gBAAOA,QAAP,EAAiB;EACb,UAAI,CAAC,KAAKD,aAAV,EAAyB;EACrB,eAAO,IAAP;EACH;;EACD,UAAIC,QAAJ,EAAc;EACV,YAAM7U,SAAS,GAAG,KAAK4U,aAAvB;;EACA,aAAK,IAAI9X,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkD,SAAS,CAACxC,MAA9B,EAAsCV,CAAC,EAAvC,EAA2C;EACvC,cAAI+X,QAAQ,KAAK7U,SAAS,CAAClD,CAAD,CAA1B,EAA+B;EAC3BkD,YAAAA,SAAS,CAACN,MAAV,CAAiB5C,CAAjB,EAAoB,CAApB;EACA,mBAAO,IAAP;EACH;EACJ;EACJ,OARD,MASK;EACD,aAAK8X,aAAL,GAAqB,EAArB;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EApdA;EAAA;EAAA,WAqdI,wBAAe;EACX,aAAO,KAAKA,aAAL,IAAsB,EAA7B;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAveA;EAAA;EAAA,WAweI,uBAAcC,QAAd,EAAwB;EACpB,WAAKG,qBAAL,GAA6B,KAAKA,qBAAL,IAA8B,EAA3D;;EACA,WAAKA,qBAAL,CAA2BxW,IAA3B,CAAgCqW,QAAhC;;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA5fA;EAAA;EAAA,WA6fI,4BAAmBA,QAAnB,EAA6B;EACzB,WAAKG,qBAAL,GAA6B,KAAKA,qBAAL,IAA8B,EAA3D;;EACA,WAAKA,qBAAL,CAA2B9D,OAA3B,CAAmC2D,QAAnC;;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EArhBA;EAAA;EAAA,WAshBI,wBAAeA,QAAf,EAAyB;EACrB,UAAI,CAAC,KAAKG,qBAAV,EAAiC;EAC7B,eAAO,IAAP;EACH;;EACD,UAAIH,QAAJ,EAAc;EACV,YAAM7U,SAAS,GAAG,KAAKgV,qBAAvB;;EACA,aAAK,IAAIlY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkD,SAAS,CAACxC,MAA9B,EAAsCV,CAAC,EAAvC,EAA2C;EACvC,cAAI+X,QAAQ,KAAK7U,SAAS,CAAClD,CAAD,CAA1B,EAA+B;EAC3BkD,YAAAA,SAAS,CAACN,MAAV,CAAiB5C,CAAjB,EAAoB,CAApB;EACA,mBAAO,IAAP;EACH;EACJ;EACJ,OARD,MASK;EACD,aAAKkY,qBAAL,GAA6B,EAA7B;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA7iBA;EAAA;EAAA,WA8iBI,gCAAuB;EACnB,aAAO,KAAKA,qBAAL,IAA8B,EAArC;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAvjBA;EAAA;EAAA,WAwjBI,iCAAwB7W,MAAxB,EAAgC;EAC5B,UAAI,KAAK6W,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BxX,MAA7D,EAAqE;EACjE,YAAMwC,SAAS,GAAG,KAAKgV,qBAAL,CAA2BnV,KAA3B,EAAlB;;EADiE,oDAE1CG,SAF0C;EAAA;;EAAA;EAEjE,iEAAkC;EAAA,gBAAvB6U,QAAuB;EAC9BA,YAAAA,QAAQ,CAAC1V,KAAT,CAAe,IAAf,EAAqBhB,MAAM,CAAC5C,IAA5B;EACH;EAJgE;EAAA;EAAA;EAAA;EAAA;EAKpE;EACJ;EA/jBL;;EAAA;EAAA,EAA4BmD,OAA5B;;EChBA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASuW,OAAT,CAAiBjU,IAAjB,EAAuB;EAC1BA,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAf;EACA,OAAKkU,EAAL,GAAUlU,IAAI,CAACmU,GAAL,IAAY,GAAtB;EACA,OAAKC,GAAL,GAAWpU,IAAI,CAACoU,GAAL,IAAY,KAAvB;EACA,OAAKC,MAAL,GAAcrU,IAAI,CAACqU,MAAL,IAAe,CAA7B;EACA,OAAKC,MAAL,GAActU,IAAI,CAACsU,MAAL,GAAc,CAAd,IAAmBtU,IAAI,CAACsU,MAAL,IAAe,CAAlC,GAAsCtU,IAAI,CAACsU,MAA3C,GAAoD,CAAlE;EACA,OAAKC,QAAL,GAAgB,CAAhB;EACH;EACD;EACA;EACA;EACA;EACA;EACA;;EACAN,OAAO,CAACvZ,SAAR,CAAkB8Z,QAAlB,GAA6B,YAAY;EACrC,MAAIN,EAAE,GAAG,KAAKA,EAAL,GAAUzT,IAAI,CAACgU,GAAL,CAAS,KAAKJ,MAAd,EAAsB,KAAKE,QAAL,EAAtB,CAAnB;;EACA,MAAI,KAAKD,MAAT,EAAiB;EACb,QAAII,IAAI,GAAGjU,IAAI,CAACkU,MAAL,EAAX;EACA,QAAIC,SAAS,GAAGnU,IAAI,CAAC8B,KAAL,CAAWmS,IAAI,GAAG,KAAKJ,MAAZ,GAAqBJ,EAAhC,CAAhB;EACAA,IAAAA,EAAE,GAAG,CAACzT,IAAI,CAAC8B,KAAL,CAAWmS,IAAI,GAAG,EAAlB,IAAwB,CAAzB,KAA+B,CAA/B,GAAmCR,EAAE,GAAGU,SAAxC,GAAoDV,EAAE,GAAGU,SAA9D;EACH;;EACD,SAAOnU,IAAI,CAAC0T,GAAL,CAASD,EAAT,EAAa,KAAKE,GAAlB,IAAyB,CAAhC;EACH,CARD;EASA;EACA;EACA;EACA;EACA;;;EACAH,OAAO,CAACvZ,SAAR,CAAkBma,KAAlB,GAA0B,YAAY;EAClC,OAAKN,QAAL,GAAgB,CAAhB;EACH,CAFD;EAGA;EACA;EACA;EACA;EACA;;;EACAN,OAAO,CAACvZ,SAAR,CAAkBoa,MAAlB,GAA2B,UAAUX,GAAV,EAAe;EACtC,OAAKD,EAAL,GAAUC,GAAV;EACH,CAFD;EAGA;EACA;EACA;EACA;EACA;;;EACAF,OAAO,CAACvZ,SAAR,CAAkBqa,MAAlB,GAA2B,UAAUX,GAAV,EAAe;EACtC,OAAKA,GAAL,GAAWA,GAAX;EACH,CAFD;EAGA;EACA;EACA;EACA;EACA;;;EACAH,OAAO,CAACvZ,SAAR,CAAkBsa,SAAlB,GAA8B,UAAUV,MAAV,EAAkB;EAC5C,OAAKA,MAAL,GAAcA,MAAd;EACH,CAFD;;MCzDaW,OAAb;EAAA;;EAAA;;EACI,mBAAYxP,GAAZ,EAAiBzF,IAAjB,EAAuB;EAAA;;EAAA;;EACnB,QAAIkV,EAAJ;;EACA;EACA,UAAKC,IAAL,GAAY,EAAZ;EACA,UAAK5C,IAAL,GAAY,EAAZ;;EACA,QAAI9M,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;EAChCzF,MAAAA,IAAI,GAAGyF,GAAP;EACAA,MAAAA,GAAG,GAAGS,SAAN;EACH;;EACDlG,IAAAA,IAAI,GAAGA,IAAI,IAAI,EAAf;EACAA,IAAAA,IAAI,CAACuF,IAAL,GAAYvF,IAAI,CAACuF,IAAL,IAAa,YAAzB;EACA,UAAKvF,IAAL,GAAYA,IAAZ;EACAD,IAAAA,qBAAqB,gCAAOC,IAAP,CAArB;;EACA,UAAKoV,YAAL,CAAkBpV,IAAI,CAACoV,YAAL,KAAsB,KAAxC;;EACA,UAAKC,oBAAL,CAA0BrV,IAAI,CAACqV,oBAAL,IAA6BC,QAAvD;;EACA,UAAKC,iBAAL,CAAuBvV,IAAI,CAACuV,iBAAL,IAA0B,IAAjD;;EACA,UAAKC,oBAAL,CAA0BxV,IAAI,CAACwV,oBAAL,IAA6B,IAAvD;;EACA,UAAKC,mBAAL,CAAyB,CAACP,EAAE,GAAGlV,IAAI,CAACyV,mBAAX,MAAoC,IAApC,IAA4CP,EAAE,KAAK,KAAK,CAAxD,GAA4DA,EAA5D,GAAiE,GAA1F;;EACA,UAAKQ,OAAL,GAAe,IAAIzB,OAAJ,CAAY;EACvBE,MAAAA,GAAG,EAAE,MAAKoB,iBAAL,EADkB;EAEvBnB,MAAAA,GAAG,EAAE,MAAKoB,oBAAL,EAFkB;EAGvBlB,MAAAA,MAAM,EAAE,MAAKmB,mBAAL;EAHe,KAAZ,CAAf;;EAKA,UAAK/O,OAAL,CAAa,QAAQ1G,IAAI,CAAC0G,OAAb,GAAuB,KAAvB,GAA+B1G,IAAI,CAAC0G,OAAjD;;EACA,UAAKgM,WAAL,GAAmB,QAAnB;EACA,UAAKjN,GAAL,GAAWA,GAAX;;EACA,QAAMkQ,OAAO,GAAG3V,IAAI,CAAC4V,MAAL,IAAeA,MAA/B;;EACA,UAAKC,OAAL,GAAe,IAAIF,OAAO,CAACpG,OAAZ,EAAf;EACA,UAAKuG,OAAL,GAAe,IAAIH,OAAO,CAACxF,OAAZ,EAAf;EACA,UAAKmC,YAAL,GAAoBtS,IAAI,CAAC+V,WAAL,KAAqB,KAAzC;EACA,QAAI,MAAKzD,YAAT,EACI,MAAKlM,IAAL;EA/Be;EAgCtB;;EAjCL;EAAA;EAAA,WAkCI,sBAAa4P,CAAb,EAAgB;EACZ,UAAI,CAAC5X,SAAS,CAAC5B,MAAf,EACI,OAAO,KAAKyZ,aAAZ;EACJ,WAAKA,aAAL,GAAqB,CAAC,CAACD,CAAvB;EACA,aAAO,IAAP;EACH;EAvCL;EAAA;EAAA,WAwCI,8BAAqBA,CAArB,EAAwB;EACpB,UAAIA,CAAC,KAAK9P,SAAV,EACI,OAAO,KAAKgQ,qBAAZ;EACJ,WAAKA,qBAAL,GAA6BF,CAA7B;EACA,aAAO,IAAP;EACH;EA7CL;EAAA;EAAA,WA8CI,2BAAkBA,CAAlB,EAAqB;EACjB,UAAId,EAAJ;;EACA,UAAIc,CAAC,KAAK9P,SAAV,EACI,OAAO,KAAKiQ,kBAAZ;EACJ,WAAKA,kBAAL,GAA0BH,CAA1B;EACA,OAACd,EAAE,GAAG,KAAKQ,OAAX,MAAwB,IAAxB,IAAgCR,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACJ,MAAH,CAAUkB,CAAV,CAAzD;EACA,aAAO,IAAP;EACH;EArDL;EAAA;EAAA,WAsDI,6BAAoBA,CAApB,EAAuB;EACnB,UAAId,EAAJ;;EACA,UAAIc,CAAC,KAAK9P,SAAV,EACI,OAAO,KAAKkQ,oBAAZ;EACJ,WAAKA,oBAAL,GAA4BJ,CAA5B;EACA,OAACd,EAAE,GAAG,KAAKQ,OAAX,MAAwB,IAAxB,IAAgCR,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACF,SAAH,CAAagB,CAAb,CAAzD;EACA,aAAO,IAAP;EACH;EA7DL;EAAA;EAAA,WA8DI,8BAAqBA,CAArB,EAAwB;EACpB,UAAId,EAAJ;;EACA,UAAIc,CAAC,KAAK9P,SAAV,EACI,OAAO,KAAKmQ,qBAAZ;EACJ,WAAKA,qBAAL,GAA6BL,CAA7B;EACA,OAACd,EAAE,GAAG,KAAKQ,OAAX,MAAwB,IAAxB,IAAgCR,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACH,MAAH,CAAUiB,CAAV,CAAzD;EACA,aAAO,IAAP;EACH;EArEL;EAAA;EAAA,WAsEI,iBAAQA,CAAR,EAAW;EACP,UAAI,CAAC5X,SAAS,CAAC5B,MAAf,EACI,OAAO,KAAK8Z,QAAZ;EACJ,WAAKA,QAAL,GAAgBN,CAAhB;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAjFA;EAAA;EAAA,WAkFI,gCAAuB;EACnB;EACA,UAAI,CAAC,KAAKO,aAAN,IACA,KAAKN,aADL,IAEA,KAAKP,OAAL,CAAanB,QAAb,KAA0B,CAF9B,EAEiC;EAC7B;EACA,aAAKiC,SAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAjGA;EAAA;EAAA,WAkGI,cAAKzY,EAAL,EAAS;EAAA;;EACL,UAAI,CAAC,KAAK2U,WAAL,CAAiBpN,OAAjB,CAAyB,MAAzB,CAAL,EACI,OAAO,IAAP;EACJ,WAAKyN,MAAL,GAAc,IAAI0D,QAAJ,CAAW,KAAKhR,GAAhB,EAAqB,KAAKzF,IAA1B,CAAd;EACA,UAAMyB,MAAM,GAAG,KAAKsR,MAApB;EACA,UAAM7T,IAAI,GAAG,IAAb;EACA,WAAKwT,WAAL,GAAmB,SAAnB;EACA,WAAKgE,aAAL,GAAqB,KAArB,CAPK;;EASL,UAAMC,cAAc,GAAG/Y,EAAE,CAAC6D,MAAD,EAAS,MAAT,EAAiB,YAAY;EAClDvC,QAAAA,IAAI,CAAC2J,MAAL;EACA9K,QAAAA,EAAE,IAAIA,EAAE,EAAR;EACH,OAHwB,CAAzB,CATK;;EAcL,UAAM6Y,QAAQ,GAAGhZ,EAAE,CAAC6D,MAAD,EAAS,OAAT,EAAkB,UAAC0B,GAAD,EAAS;EAC1CjE,QAAAA,IAAI,CAACiI,OAAL;EACAjI,QAAAA,IAAI,CAACwT,WAAL,GAAmB,QAAnB;;EACA,QAAA,MAAI,CAAC3T,YAAL,CAAkB,OAAlB,EAA2BoE,GAA3B;;EACA,YAAIpF,EAAJ,EAAQ;EACJA,UAAAA,EAAE,CAACoF,GAAD,CAAF;EACH,SAFD,MAGK;EACD;EACAjE,UAAAA,IAAI,CAAC2X,oBAAL;EACH;EACJ,OAXkB,CAAnB;;EAYA,UAAI,UAAU,KAAKP,QAAnB,EAA6B;EACzB,YAAM5P,OAAO,GAAG,KAAK4P,QAArB;;EACA,YAAI5P,OAAO,KAAK,CAAhB,EAAmB;EACfiQ,UAAAA,cAAc,GADC;EAElB,SAJwB;;;EAMzB,YAAMzD,KAAK,GAAG,KAAKhT,YAAL,CAAkB,YAAM;EAClCyW,UAAAA,cAAc;EACdlV,UAAAA,MAAM,CAACmD,KAAP,GAFkC;;EAIlCnD,UAAAA,MAAM,CAAC9C,IAAP,CAAY,OAAZ,EAAqB,IAAIyC,KAAJ,CAAU,SAAV,CAArB;EACH,SALa,EAKXsF,OALW,CAAd;;EAMA,YAAI,KAAK1G,IAAL,CAAU8I,SAAd,EAAyB;EACrBoK,UAAAA,KAAK,CAAClK,KAAN;EACH;;EACD,aAAKuJ,IAAL,CAAU/U,IAAV,CAAe,SAAS8T,UAAT,GAAsB;EACjCxR,UAAAA,YAAY,CAACoT,KAAD,CAAZ;EACH,SAFD;EAGH;;EACD,WAAKX,IAAL,CAAU/U,IAAV,CAAemZ,cAAf;EACA,WAAKpE,IAAL,CAAU/U,IAAV,CAAeoZ,QAAf;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxJA;EAAA;EAAA,WAyJI,iBAAQ7Y,EAAR,EAAY;EACR,aAAO,KAAKqI,IAAL,CAAUrI,EAAV,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAhKA;EAAA;EAAA,WAiKI,kBAAS;EACL;EACA,WAAKoJ,OAAL,GAFK;;EAIL,WAAKuL,WAAL,GAAmB,MAAnB;EACA,WAAK3T,YAAL,CAAkB,MAAlB,EALK;;EAOL,UAAM0C,MAAM,GAAG,KAAKsR,MAApB;EACA,WAAKR,IAAL,CAAU/U,IAAV,CAAeI,EAAE,CAAC6D,MAAD,EAAS,MAAT,EAAiB,KAAKqV,MAAL,CAAY3W,IAAZ,CAAiB,IAAjB,CAAjB,CAAjB,EAA2DvC,EAAE,CAAC6D,MAAD,EAAS,MAAT,EAAiB,KAAKsV,MAAL,CAAY5W,IAAZ,CAAiB,IAAjB,CAAjB,CAA7D,EAAuGvC,EAAE,CAAC6D,MAAD,EAAS,OAAT,EAAkB,KAAK4H,OAAL,CAAalJ,IAAb,CAAkB,IAAlB,CAAlB,CAAzG,EAAqJvC,EAAE,CAAC6D,MAAD,EAAS,OAAT,EAAkB,KAAKwH,OAAL,CAAa9I,IAAb,CAAkB,IAAlB,CAAlB,CAAvJ,EAAmMvC,EAAE,CAAC,KAAKkY,OAAN,EAAe,SAAf,EAA0B,KAAKkB,SAAL,CAAe7W,IAAf,CAAoB,IAApB,CAA1B,CAArM;EACH;EACD;EACJ;EACA;EACA;EACA;;EA/KA;EAAA;EAAA,WAgLI,kBAAS;EACL,WAAKpB,YAAL,CAAkB,MAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAvLA;EAAA;EAAA,WAwLI,gBAAOxE,IAAP,EAAa;EACT,WAAKub,OAAL,CAAamB,GAAb,CAAiB1c,IAAjB;EACH;EACD;EACJ;EACA;EACA;EACA;;EA/LA;EAAA;EAAA,WAgMI,mBAAU4C,MAAV,EAAkB;EACd,WAAK4B,YAAL,CAAkB,QAAlB,EAA4B5B,MAA5B;EACH;EACD;EACJ;EACA;EACA;EACA;;EAvMA;EAAA;EAAA,WAwMI,iBAAQgG,GAAR,EAAa;EACT,WAAKpE,YAAL,CAAkB,OAAlB,EAA2BoE,GAA3B;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAhNA;EAAA;EAAA,WAiNI,gBAAO4M,GAAP,EAAY/P,IAAZ,EAAkB;EACd,UAAIyB,MAAM,GAAG,KAAK0T,IAAL,CAAUpF,GAAV,CAAb;;EACA,UAAI,CAACtO,MAAL,EAAa;EACTA,QAAAA,MAAM,GAAG,IAAIqJ,MAAJ,CAAW,IAAX,EAAiBiF,GAAjB,EAAsB/P,IAAtB,CAAT;EACA,aAAKmV,IAAL,CAAUpF,GAAV,IAAiBtO,MAAjB;EACH;;EACD,aAAOA,MAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9NA;EAAA;EAAA,WA+NI,kBAASA,MAAT,EAAiB;EACb,UAAM0T,IAAI,GAAGpb,MAAM,CAACG,IAAP,CAAY,KAAKib,IAAjB,CAAb;;EACA,+BAAkBA,IAAlB,2BAAwB;EAAnB,YAAMpF,GAAG,YAAT;EACD,YAAMtO,OAAM,GAAG,KAAK0T,IAAL,CAAUpF,GAAV,CAAf;;EACA,YAAItO,OAAM,CAACyV,MAAX,EAAmB;EACf;EACH;EACJ;;EACD,WAAKC,MAAL;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9OA;EAAA;EAAA,WA+OI,iBAAQha,MAAR,EAAgB;EACZ,UAAMH,cAAc,GAAG,KAAK6Y,OAAL,CAAazT,MAAb,CAAoBjF,MAApB,CAAvB;;EACA,WAAK,IAAIrB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,cAAc,CAACR,MAAnC,EAA2CV,CAAC,EAA5C,EAAgD;EAC5C,aAAKiX,MAAL,CAAYlR,KAAZ,CAAkB7E,cAAc,CAAClB,CAAD,CAAhC,EAAqCqB,MAAM,CAACwQ,OAA5C;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAzPA;EAAA;EAAA,WA0PI,mBAAU;EACN,WAAK4E,IAAL,CAAUpY,OAAV,CAAkB,UAACmX,UAAD;EAAA,eAAgBA,UAAU,EAA1B;EAAA,OAAlB;EACA,WAAKiB,IAAL,CAAU/V,MAAV,GAAmB,CAAnB;EACA,WAAKsZ,OAAL,CAAarC,OAAb;EACH;EACD;EACJ;EACA;EACA;EACA;;EAnQA;EAAA;EAAA,WAoQI,kBAAS;EACL,WAAKiD,aAAL,GAAqB,IAArB;EACA,WAAKH,aAAL,GAAqB,KAArB;EACA,WAAKtN,OAAL,CAAa,cAAb;EACA,UAAI,KAAK8J,MAAT,EACI,KAAKA,MAAL,CAAYnO,KAAZ;EACP;EACD;EACJ;EACA;EACA;EACA;;EA/QA;EAAA;EAAA,WAgRI,sBAAa;EACT,aAAO,KAAKuS,MAAL,EAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAvRA;EAAA;EAAA,WAwRI,iBAAQlW,MAAR,EAAgBC,WAAhB,EAA6B;EACzB,WAAKiG,OAAL;EACA,WAAKuO,OAAL,CAAab,KAAb;EACA,WAAKnC,WAAL,GAAmB,QAAnB;EACA,WAAK3T,YAAL,CAAkB,OAAlB,EAA2BkC,MAA3B,EAAmCC,WAAnC;;EACA,UAAI,KAAK+U,aAAL,IAAsB,CAAC,KAAKS,aAAhC,EAA+C;EAC3C,aAAKF,SAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EArSA;EAAA;EAAA,WAsSI,qBAAY;EAAA;;EACR,UAAI,KAAKD,aAAL,IAAsB,KAAKG,aAA/B,EACI,OAAO,IAAP;EACJ,UAAMxX,IAAI,GAAG,IAAb;;EACA,UAAI,KAAKwW,OAAL,CAAanB,QAAb,IAAyB,KAAK2B,qBAAlC,EAAyD;EACrD,aAAKR,OAAL,CAAab,KAAb;EACA,aAAK9V,YAAL,CAAkB,kBAAlB;EACA,aAAKwX,aAAL,GAAqB,KAArB;EACH,OAJD,MAKK;EACD,YAAMa,KAAK,GAAG,KAAK1B,OAAL,CAAalB,QAAb,EAAd;EACA,aAAK+B,aAAL,GAAqB,IAArB;EACA,YAAMrD,KAAK,GAAG,KAAKhT,YAAL,CAAkB,YAAM;EAClC,cAAIhB,IAAI,CAACwX,aAAT,EACI;;EACJ,UAAA,MAAI,CAAC3X,YAAL,CAAkB,mBAAlB,EAAuCG,IAAI,CAACwW,OAAL,CAAanB,QAApD,EAHkC;;;EAKlC,cAAIrV,IAAI,CAACwX,aAAT,EACI;EACJxX,UAAAA,IAAI,CAACkH,IAAL,CAAU,UAACjD,GAAD,EAAS;EACf,gBAAIA,GAAJ,EAAS;EACLjE,cAAAA,IAAI,CAACqX,aAAL,GAAqB,KAArB;EACArX,cAAAA,IAAI,CAACsX,SAAL;;EACA,cAAA,MAAI,CAACzX,YAAL,CAAkB,iBAAlB,EAAqCoE,GAArC;EACH,aAJD,MAKK;EACDjE,cAAAA,IAAI,CAACmY,WAAL;EACH;EACJ,WATD;EAUH,SAjBa,EAiBXD,KAjBW,CAAd;;EAkBA,YAAI,KAAKpX,IAAL,CAAU8I,SAAd,EAAyB;EACrBoK,UAAAA,KAAK,CAAClK,KAAN;EACH;;EACD,aAAKuJ,IAAL,CAAU/U,IAAV,CAAe,SAAS8T,UAAT,GAAsB;EACjCxR,UAAAA,YAAY,CAACoT,KAAD,CAAZ;EACH,SAFD;EAGH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAhVA;EAAA;EAAA,WAiVI,uBAAc;EACV,UAAMoE,OAAO,GAAG,KAAK5B,OAAL,CAAanB,QAA7B;EACA,WAAKgC,aAAL,GAAqB,KAArB;EACA,WAAKb,OAAL,CAAab,KAAb;EACA,WAAK9V,YAAL,CAAkB,WAAlB,EAA+BuY,OAA/B;EACH;EAtVL;;EAAA;EAAA,EAA6B5Z,OAA7B;;ECHA;EACA;EACA;;EACA,IAAM6Z,KAAK,GAAG,EAAd;;EACA,SAAS1b,MAAT,CAAgB4J,GAAhB,EAAqBzF,IAArB,EAA2B;EACvB,MAAI,QAAOyF,GAAP,MAAe,QAAnB,EAA6B;EACzBzF,IAAAA,IAAI,GAAGyF,GAAP;EACAA,IAAAA,GAAG,GAAGS,SAAN;EACH;;EACDlG,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAf;EACA,MAAMwX,MAAM,GAAGvJ,GAAG,CAACxI,GAAD,EAAMzF,IAAI,CAACuF,IAAL,IAAa,YAAnB,CAAlB;EACA,MAAM2E,MAAM,GAAGsN,MAAM,CAACtN,MAAtB;EACA,MAAMuB,EAAE,GAAG+L,MAAM,CAAC/L,EAAlB;EACA,MAAMlG,IAAI,GAAGiS,MAAM,CAACjS,IAApB;EACA,MAAM6N,aAAa,GAAGmE,KAAK,CAAC9L,EAAD,CAAL,IAAalG,IAAI,IAAIgS,KAAK,CAAC9L,EAAD,CAAL,CAAU,MAAV,CAA3C;EACA,MAAMgM,aAAa,GAAGzX,IAAI,CAAC0X,QAAL,IAClB1X,IAAI,CAAC,sBAAD,CADc,IAElB,UAAUA,IAAI,CAAC2X,SAFG,IAGlBvE,aAHJ;EAIA,MAAItB,EAAJ;;EACA,MAAI2F,aAAJ,EAAmB;EACf3F,IAAAA,EAAE,GAAG,IAAImD,OAAJ,CAAY/K,MAAZ,EAAoBlK,IAApB,CAAL;EACH,GAFD,MAGK;EACD,QAAI,CAACuX,KAAK,CAAC9L,EAAD,CAAV,EAAgB;EACZ8L,MAAAA,KAAK,CAAC9L,EAAD,CAAL,GAAY,IAAIwJ,OAAJ,CAAY/K,MAAZ,EAAoBlK,IAApB,CAAZ;EACH;;EACD8R,IAAAA,EAAE,GAAGyF,KAAK,CAAC9L,EAAD,CAAV;EACH;;EACD,MAAI+L,MAAM,CAACjW,KAAP,IAAgB,CAACvB,IAAI,CAACuB,KAA1B,EAAiC;EAC7BvB,IAAAA,IAAI,CAACuB,KAAL,GAAaiW,MAAM,CAACjN,QAApB;EACH;;EACD,SAAOuH,EAAE,CAACrQ,MAAH,CAAU+V,MAAM,CAACjS,IAAjB,EAAuBvF,IAAvB,CAAP;EACH;EAED;;;EACA,SAAcnE,MAAd,EAAsB;EAClBoZ,EAAAA,OAAO,EAAPA,OADkB;EAElBnK,EAAAA,MAAM,EAANA,MAFkB;EAGlBgH,EAAAA,EAAE,EAAEjW,MAHc;EAIlB4V,EAAAA,OAAO,EAAE5V;EAJS,CAAtB;;;;;;;;"}